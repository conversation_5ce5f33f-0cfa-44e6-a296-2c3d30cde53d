---
description:
globs:
alwaysApply: true
---
# 数据库模式规则

## PostgreSQL模式

### 用户表(users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    role VARCHAR(20) NOT NULL DEFAULT 'user',
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

### 账户配置表(accounts)
```sql
CREATE TABLE accounts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    account_type VARCHAR(20) NOT NULL, -- 'spot' or 'future'
    platform_name VARCHAR(100) NOT NULL,
    platform_url VARCHAR(255),
    contract_code VARCHAR(50),
    username VA<PERSON>HAR(100) NOT NULL,
    password_encrypted VARCHAR(255) NOT NULL,
    cost_per_gram NUMERIC(10, 2) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    connection_status VARCHAR(20) DEFAULT 'disconnected',
    last_connection_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, account_type)
);
```

### 设置表(settings)
```sql
CREATE TABLE settings (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    target_profit_diff NUMERIC(10, 2) NOT NULL DEFAULT 0.5,
    position_size INTEGER NOT NULL DEFAULT 1,
    max_positions INTEGER NOT NULL DEFAULT 5,
    max_daily_cycles INTEGER NOT NULL DEFAULT 10,
    spot_refresh_rate INTEGER NOT NULL DEFAULT 3,
    forward_basis_range1_min NUMERIC(10, 2) NOT NULL DEFAULT 0,
    forward_basis_range1_max NUMERIC(10, 2) NOT NULL DEFAULT -0.2,
    forward_basis_range2 VARCHAR(20) NOT NULL DEFAULT '>=1',
    reverse_basis_range1_min NUMERIC(10, 2) NOT NULL DEFAULT 0,
    reverse_basis_range1_max NUMERIC(10, 2) NOT NULL DEFAULT 0.2,
    reverse_basis_range2 VARCHAR(20) NOT NULL DEFAULT '<=-1',
    auto_trade_enabled BOOLEAN NOT NULL DEFAULT false,
    base_weight INTEGER NOT NULL DEFAULT 1000, -- 基础克重(g)
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id)
);
```

### 交易时间设置表(trading_times)
```sql
CREATE TABLE trading_times (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    period_name VARCHAR(50) NOT NULL,
    weekdays INTEGER[] NOT NULL, -- 0-6，0是周一
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, period_name)
);
```

### 订单表(orders)
```sql
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    direction INTEGER NOT NULL, -- 1: 正向(现货买跌-期货买涨), -1: 反向(现货买涨-期货买跌)
    status VARCHAR(20) NOT NULL DEFAULT 'open', -- 'open', 'closed', 'cancelled', 'error'
    spot_order_id VARCHAR(100),
    future_order_id VARCHAR(64),  -- 扩展长度以支持TqSDK订单ID
    volume INTEGER NOT NULL DEFAULT 1, -- 开仓手数
    base_weight INTEGER NOT NULL DEFAULT 1000, -- 基础克重(g)

    -- 开仓信息
    spot_open_price NUMERIC(10, 2) NOT NULL,
    future_open_price NUMERIC(10, 2) NOT NULL,
    open_basis NUMERIC(10, 2) NOT NULL,
    target_basis NUMERIC(10, 2) NOT NULL,
    open_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

    -- 平仓信息
    spot_close_price NUMERIC(10, 2),
    future_close_price NUMERIC(10, 2),
    close_basis NUMERIC(10, 2),
    close_time TIMESTAMP WITH TIME ZONE,

    -- 盈亏信息
    spot_pnl NUMERIC(10, 2),
    future_pnl NUMERIC(10, 2),
    total_pnl NUMERIC(10, 2),
    spot_cost NUMERIC(10, 2) NOT NULL,
    future_cost NUMERIC(10, 2) NOT NULL,
    total_cost NUMERIC(10, 2) NOT NULL,
    net_profit NUMERIC(10, 2),

    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_open_time ON orders(open_time);
```

### 操作日志表(operation_logs)
```sql
CREATE TABLE operation_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    operation_type VARCHAR(50) NOT NULL,
    operation_data JSONB NOT NULL,
    status VARCHAR(20) NOT NULL,
    message TEXT,
    ip_address VARCHAR(50),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_logs_user_id ON operation_logs(user_id);
CREATE INDEX idx_logs_operation_type ON operation_logs(operation_type);
CREATE INDEX idx_logs_created_at ON operation_logs(created_at);
```

## Redis键设计

1. **实时行情数据**
   ```
   # 现货价格
   goldArb:market:spot:{timestamp} -> Hash
     - bid: 现货买入价
     - ask: 现货卖出价
     - timestamp: 时间戳

   # 期货价格
   goldArb:market:future:{timestamp} -> Hash
     - bid: 期货买入价
     - ask: 期货卖出价
     - timestamp: 时间戳

   # 最新行情
   goldArb:market:latest -> Hash
     - spot_bid: 现货买入价
     - spot_ask: 现货卖出价
     - future_bid: 期货买入价
     - future_ask: 期货卖出价
     - forward_basis: 正向基差
     - reverse_basis: 反向基差
     - timestamp: 时间戳
   ```

2. **基差历史数据**
   ```
   # 正向基差历史最高/最低值(按日期)
   goldArb:basis:forward:high:{date} -> Sorted Set
     score: 时间戳
     member: 基差值

   goldArb:basis:forward:low:{date} -> Sorted Set
     score: 时间戳
     member: 基差值

   # 反向基差历史最高/最低值(按日期)
   goldArb:basis:reverse:high:{date} -> Sorted Set
     score: 时间戳
     member: 基差值

   goldArb:basis:reverse:low:{date} -> Sorted Set
     score: 时间戳
     member: 基差值
   ```

3. **用户会话数据**
   ```
   # 用户令牌
   goldArb:auth:token:{token} -> Hash
     - user_id: 用户ID
     - username: 用户名
     - role: 角色
     - expires: 过期时间

   # 用户在线状态
   goldArb:user:online:{user_id} -> String
     - 值为连接ID
   ```

4. **统计数据**
   ```
   # 当日交易统计
   goldArb:stats:daily:{date}:{user_id} -> Hash
     - trade_count: 交易次数
     - profit: 盈利总额
     - loss: 亏损总额
     - net: 净收益
   ```

5. **分布式锁**
   ```
   # 交易锁
   goldArb:lock:trade:{user_id} -> String
     - 值为锁ID，过期时间设为5秒
   ```

## InfluxDB测量

1. **现货价格时序数据**
   ```
   measurement: spot_prices
   tags:
     - user_id: 用户ID
     - source: 数据来源
   fields:
     - bid: 买入价
     - ask: 卖出价
   timestamp: 采集时间
   ```

2. **期货价格时序数据**
   ```
   measurement: future_prices
   tags:
     - user_id: 用户ID
     - contract: 合约代码
   fields:
     - bid: 买入价
     - ask: 卖出价
   timestamp: 采集时间
   ```

3. **基差时序数据**
   ```
   measurement: basis_values
   tags:
     - user_id: 用户ID
     - type: 基差类型('forward', 'reverse')
   fields:
     - value: 基差值
   timestamp: 计算时间
   ```

4. **性能监控数据**
   ```
   measurement: performance_metrics
   tags:
     - component: 组件名称
     - operation: 操作类型
   fields:
     - duration_ms: 耗时(毫秒)
     - success: 是否成功
   timestamp: 操作时间
   ```
