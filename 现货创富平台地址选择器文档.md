现货平台持仓订单页：https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e



结料按钮选择器：element.style {
}
<style>
.feedingback[data-v-9a81d21c] {
    background-color: #f29100;
}
<style>
.des-button[data-v-9a81d21c] {
    padding: 5px 10px;
    text-align: center;
    color: #fff;
    border-radius: 5px;
}
<style>
.ml20[data-v-9a81d21c] {
    margin-left: 10px;
}
<style>
.ml20 {
    margin-left: 10px;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.deposit-bottom[data-v-9a81d21c] {
    width: 100%;
    padding: 10px 10px 0;
    background-color: #f7f8fa;
    font-size: 14px;
    border-radius: 5px;
    margin-top: 10px;
    position: relative;
}
<style>
.puwidth[data-v-9a81d21c] {
    width: 98%;
    margin: auto;
    font-size: 15px;
    background-color: #fff;
    padding: 10px 10px;
    border-radius: 5px;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，违约结算按钮选择器：element.style {
}
<style>
.des-button[data-v-9a81d21c] {
    padding: 5px 10px;
    text-align: center;
    color: #fff;
    border-radius: 5px;
}
<style>
.back1[data-v-9a81d21c] {
    background-color: #e10000;
}
<style>
.ml20[data-v-9a81d21c] {
    margin-left: 10px;
}
<style>
.back1[data-v-9a81d21c] {
    background-color: #de1a1a;
}
<style>
.ml20 {
    margin-left: 10px;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.deposit-bottom[data-v-9a81d21c] {
    width: 100%;
    padding: 10px 10px 0;
    background-color: #f7f8fa;
    font-size: 14px;
    border-radius: 5px;
    margin-top: 10px;
    position: relative;
}
<style>
.puwidth[data-v-9a81d21c] {
    width: 98%;
    margin: auto;
    font-size: 15px;
    background-color: #fff;
    padding: 10px 10px;
    border-radius: 5px;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，开仓价格选择器：element.style {
}
<style>
.per-price[data-v-9a81d21c], .new-price[data-v-9a81d21c] {
    font-size: 14px;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.puwidth[data-v-9a81d21c] {
    width: 98%;
    margin: auto;
    font-size: 15px;
    background-color: #fff;
    padding: 10px 10px;
    border-radius: 5px;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，实时平仓价格选择器：element.style {
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}

<style>
.color1[data-v-9a81d21c] {
    color: #e10000;
}
<style>
.color1 {
    color: #e10000;
}
<style>
.color2[data-v-9a81d21c] {
    color: green;
}
<style>
.per-price[data-v-9a81d21c], .new-price[data-v-9a81d21c] {
    font-size: 14px;
}
<style>
.color2 {
    color: #999;
}
<style>
.puwidth[data-v-9a81d21c] {
    width: 98%;
    margin: auto;
    font-size: 15px;
    background-color: #fff;
    padding: 10px 10px;
    border-radius: 5px;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，订单号选择器：element.style {
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.deposit-bottom[data-v-9a81d21c] {
    width: 100%;
    padding: 10px 10px 0;
    background-color: #f7f8fa;
    font-size: 14px;
    border-radius: 5px;
    margin-top: 10px;
    position: relative;
}
<style>
.puwidth[data-v-9a81d21c] {
    width: 98%;
    margin: auto;
    font-size: 15px;
    background-color: #fff;
    padding: 10px 10px;
    border-radius: 5px;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}， 平仓结算选择器：element.style {
    overflow: visible;
}
<style>
.settle-btn .sttlebutton[data-v-9a81d21c] {
    width: 48%;
    padding: 8px 10px;
}
<style>
.pup-sbtn[data-v-9a81d21c] .u-size-default {
    height: unset;
    line-height: unset;
}
<style>
.pup-sbtn[data-v-9a81d21c] uni-button {
    margin: unset;
    font-size: unset;
}
<style>
.des-button[data-v-9a81d21c] {
    padding: 5px 10px;
    text-align: center;
    color: #fff;
    border-radius: 5px;
}
<style>
.back2[data-v-9a81d21c] {
    background-color: green;
}
<style>
.back2[data-v-9a81d21c] {
    background-color: green;
}
<style>
.u-size-default[data-v-476963b6] {
    font-size: 15px;
    height: 40px;
    line-height: 40px;
}
<style>
.u-btn--default[data-v-476963b6] {
    color: #606266;
    border-color: #c0c4cc;
    background-color: #fff;
}
<style>
.u-btn[data-v-476963b6] {
    position: relative;
    border: 0;
    display: inline-flex;
    overflow: visible;
    line-height: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0 20px;
    z-index: 1;
    box-sizing: border-box;
    transition: all .15s;
}
<style>
.u-line-1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
<style>
.u-fix-ios-appearance {
    -webkit-appearance: none;
}
uni-button {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding-left: 14px;
    padding-right: 14px;
    box-sizing: border-box;
    font-size: 18px;
    text-align: center;
    text-decoration: none;
    line-height: 2.55555556;
    border-radius: 5px;
    -webkit-tap-highlight-color: transparent;
    overflow: hidden;
    color: #000;
    background-color: #f8f8f8;
    cursor: pointer;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
uni-button[Attributes Style] {
    -webkit-locale: "en";
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
.pup-sbtn[data-v-9a81d21c] uni-button::after {
    border: unset;
}
<style>
.u-hairline-border[data-v-476963b6]:after {
    content: " ";
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    left: 0;
    top: 0;
    width: 199.8%;
    height: 199.7%;
    -webkit-transform: scale(.5);
    transform: scale(.5);
    border: 1px solid currentColor;
    z-index: 1;
}
<style>
.u-btn[data-v-476963b6]::after {
    border: none;
}
uni-button:after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid rgba(0,0,0,.2);
    -webkit-transform: scale(.5);
    transform: scale(.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: 10px;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，平仓取消选择器：element.style {
}
<style>
.settle-btn .sttlebutton[data-v-9a81d21c] {
    width: 48%;
    padding: 8px 10px;
}
<style>
.cancelbtn[data-v-9a81d21c] {
    background-color: #ccc;
}
<style>
.des-button[data-v-9a81d21c] {
    padding: 5px 10px;
    text-align: center;
    color: #fff;
    border-radius: 5px;
}
<style>
.cancelbtn[data-v-9a81d21c] {
    background-color: #ccc;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，平仓弹窗订单号选择器：element.style {
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.popcont .boxinfo[data-v-9a81d21c] {
    line-height: 31px;
    align-items: center;
    text-align: justify;
}
<style>
.popcont[data-v-9a81d21c] {
    padding: 5px 12px;
    color: #444;
    font-size: 14px;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，平仓弹窗开仓价选择器：element.style {
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.popcont .boxinfo[data-v-9a81d21c] {
    line-height: 31px;
    align-items: center;
    text-align: justify;
}
<style>
.popcont[data-v-9a81d21c] {
    padding: 5px 12px;
    color: #444;
    font-size: 14px;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，平仓弹窗平仓价选择器：element.style {
}
<style>
.color2[data-v-9a81d21c] {
    color: green;
}
<style>
.color2 {
    color: #999;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.popcont .boxinfo[data-v-9a81d21c] {
    line-height: 31px;
    align-items: center;
    text-align: justify;
}
<style>
.popcont[data-v-9a81d21c] {
    padding: 5px 12px;
    color: #444;
    font-size: 14px;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，

现货平台登录页：https://j.jtd9999.vip/h5/#/pages/login/login?demp_code=944440b68743bdaaf6e4cf7b5745893e&amp;salesman_id=
手机号输入：element.style {
}
<style>
.tell-rule .uni-form-item .uni-input-input[data-v-18848018], .tell-rule .uni-form-item .uni-input-placeholder[data-v-18848018] {
    font-size: 15px;
    color: #565656;
}
.uni-input-placeholder {
    position: absolute;
    top: auto!important;
    left: 0;
    color: grey;
    overflow: hidden;
    text-overflow: clip;
    white-space: pre;
    word-break: keep-all;
    pointer-events: none;
    line-height: inherit;
}
.uni-input-input, .uni-input-placeholder {
    width: 100%;
}
.uni-input-form, .uni-input-input, .uni-input-placeholder, .uni-input-wrapper {
    outline: none;
    border: none;
    padding: 0;
    margin: 0;
    text-decoration: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
用户代理样式表
div {
    display: block;
}
<style>
.tell-rule .uni-form-item .uni-input[data-v-18848018] {
    font-size: 15px;
    color: #565656;
}
uni-input {
    display: block;
    font-size: 16px;
    line-height: 1.4em;
    height: 1.4em;
    min-height: 1.4em;
    overflow: hidden;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}   
密码输入：element.style {
}
<style>
.tell-rule .uni-form-item .uni-input-input[data-v-18848018], .tell-rule .uni-form-item .uni-input-placeholder[data-v-18848018] {
    font-size: 15px;
    color: #565656;
}
.uni-input-placeholder {
    position: absolute;
    top: auto!important;
    left: 0;
    color: grey;
    overflow: hidden;
    text-overflow: clip;
    white-space: pre;
    word-break: keep-all;
    pointer-events: none;
    line-height: inherit;
}
.uni-input-input, .uni-input-placeholder {
    width: 100%;
}
.uni-input-form, .uni-input-input, .uni-input-placeholder, .uni-input-wrapper {
    outline: none;
    border: none;
    padding: 0;
    margin: 0;
    text-decoration: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
用户代理样式表
div {
    display: block;
}
<style>
.tell-rule .uni-form-item .uni-input[data-v-18848018] {
    font-size: 15px;
    color: #565656;
}
uni-input {
    display: block;
    font-size: 16px;
    line-height: 1.4em;
    height: 1.4em;
    min-height: 1.4em;
    overflow: hidden;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}  
登录按钮：element.style {
}
<style>
.login-reg .login[data-v-06e88858] {
    background-color: #de1a1a;
    color: #fff;
}
<style>
.login-reg .uni-btn[data-v-06e88858] {
    width: 325px;
    font-size: 15px;
}
uni-button {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding-left: 14px;
    padding-right: 14px;
    box-sizing: border-box;
    font-size: 18px;
    text-align: center;
    text-decoration: none;
    line-height: 2.55555556;
    border-radius: 5px;
    -webkit-tap-highlight-color: transparent;
    overflow: hidden;
    color: #000;
    background-color: #f8f8f8;
    cursor: pointer;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.login-reg[data-v-06e88858] {
    letter-spacing: 2px;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
uni-button:after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid rgba(0,0,0,.2);
    -webkit-transform: scale(.5);
    transform: scale(.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: 10px;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}

现货平台下单页：https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
实时回购（买入）价格选择器：element.style {
}
<style>
.amountsetbox .amountlist[data-v-0fa31b49] {
    width: 25%;
    text-align: center;
    padding-bottom: 7px;
}
<style>
.greecolor[data-v-0fa31b49] {
    color: green;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}
实时销售（卖出）价格选择器：element.style {
}
<style>
.amountsetbox .amountlist[data-v-0fa31b49] {
    width: 25%;
    text-align: center;
    padding-bottom: 7px;
}
<style>
.redcolor[data-v-0fa31b49] {
    color: #e10000;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}
输入框无法直接输入数值，靠左右两边加减号调整数值，每一次点击差距是1000，
减号选择器：element.style {
    background: rgb(225, 0, 0);
}
<style>
.ydweight[data-v-0fa31b49] .uni-numbox__minus {
    border-top-left-radius: 15px;
    border-bottom-left-radius: 15px;
}
<style>
.uni-numbox__minus[data-v-2449fa78] {
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
}
<style>
.uni-numbox-btns[data-v-2449fa78] {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 0 8px;
    background-color: #f5f5f5;
    cursor: pointer;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.amountbox[data-v-0fa31b49] {
    font-size: 14px;
    color: #242424;
    padding: 0 14px;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}
数值框选择器：element.style {
}
<style>
.uni-small-input-number[data-v-0fa31b49] .uni-input-input {
    pointer-events: none;
    scursor: not-allowed;
}
<style>
.ydweight[data-v-0fa31b49] .uni-input-input {
    color: #e10000;
    font-weight: 700;
    font-size: 15px;
}
.uni-input-input[type=number] {
    -moz-appearance: textfield;
}
.uni-input-input {
    position: relative;
    display: block;
    height: 100%;
    background: none;
    color: inherit;
    opacity: 1;
    font: inherit;
    line-height: inherit;
    letter-spacing: inherit;
    text-align: inherit;
    text-indent: inherit;
    text-transform: inherit;
    text-shadow: inherit;
}
.uni-input-input, .uni-input-placeholder {
    width: 100%;
}
.uni-input-form, .uni-input-input, .uni-input-placeholder, .uni-input-wrapper {
    outline: none;
    border: none;
    padding: 0;
    margin: 0;
    text-decoration: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
用户代理样式表
input[type="number" i] {
    padding-block: 1px;
    padding-inline: 2px;
}
用户代理样式表
input:not([type="file" i], [type="image" i], [type="checkbox" i], [type="radio" i]) {
}
用户代理样式表
input {
    font-style: ;
    font-variant-ligatures: ;
    font-variant-caps: ;
    font-variant-numeric: ;
    font-variant-east-asian: ;
    font-variant-alternates: ;
    font-variant-position: ;
    font-weight: ;
    font-stretch: ;
    font-size: ;
    font-family: ;
    font-optical-sizing: ;
    font-kerning: ;
    font-feature-settings: ;
    font-variation-settings: ;
    text-rendering: auto;
    color: fieldtext;
    letter-spacing: normal;
    word-spacing: normal;
    line-height: normal;
    text-transform: none;
    text-indent: 0px;
    text-shadow: none;
    display: inline-block;
    text-align: start;
    appearance: auto;
    -webkit-rtl-ordering: logical;
    cursor: text;
    background-color: field;
    margin: 0em;
    padding: 1px 0px;
    border-width: 2px;
    border-style: inset;
    border-color: -internal-light-dark(rgb(118, 118, 118), rgb(133, 133, 133));
    border-image: initial;
    padding-block: 1px;
    padding-inline: 2px;
}
style 属性 {
    background: rgb(225, 0, 0);
    color: rgb(255, 255, 255);
}
<style>
.uni-numbox__value[data-v-2449fa78] {
    margin: 0 2px;
    background-color: #f5f5f5;
    width: 40px;
    height: 26px;
    text-align: center;
    font-size: 14px;
    border-left-width: 0;
    border-right-width: 0;
    color: #333;
}
uni-input {
    display: block;
    font-size: 16px;
    line-height: 1.4em;
    height: 1.4em;
    min-height: 1.4em;
    overflow: hidden;
}
<style>
.amountbox[data-v-0fa31b49] {
    font-size: 14px;
    color: #242424;
    padding: 0 14px;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
加号选择器：element.style {
    background: rgb(225, 0, 0);
}
<style>
.ydweight[data-v-0fa31b49] .uni-numbox__plus {
    border-top-right-radius: 15px;
    border-bottom-right-radius: 15px;
}
<style>
.uni-numbox__plus[data-v-2449fa78] {
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
<style>
.uni-numbox-btns[data-v-2449fa78] {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 0 8px;
    background-color: #f5f5f5;
    cursor: pointer;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.amountbox[data-v-0fa31b49] {
    font-size: 14px;
    color: #242424;
    padding: 0 14px;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，
1000g选择器：element.style {
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.tradeinfo .weigthbox .weigthnumber[data-v-0fa31b49] {
    width: 21%;
    margin-bottom: 10px;
    background-color: #dbdbdb;
    padding: 7px 0;
    text-align: center;
    border-radius: 5px;
    color: #fff;
    position: relative;
}
<style>
.amountbox[data-v-0fa31b49] {
    font-size: 14px;
    color: #242424;
    padding: 0 14px;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，
2000g选择器：element.style {
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.tradeinfo .weigthbox .weigthnumber[data-v-0fa31b49] {
    width: 21%;
    margin-bottom: 10px;
    background-color: #dbdbdb;
    padding: 7px 0;
    text-align: center;
    border-radius: 5px;
    color: #fff;
    position: relative;
}
<style>
.amountbox[data-v-0fa31b49] {
    font-size: 14px;
    color: #242424;
    padding: 0 14px;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，
3000g选择器：element.style {
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.tradeinfo .weigthbox .weigthnumber[data-v-0fa31b49] {
    width: 21%;
    margin-bottom: 10px;
    background-color: #dbdbdb;
    padding: 7px 0;
    text-align: center;
    border-radius: 5px;
    color: #fff;
    position: relative;
}
<style>
.amountbox[data-v-0fa31b49] {
    font-size: 14px;
    color: #242424;
    padding: 0 14px;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，
5000g选择器：element.style {
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.tradeinfo .weigthbox .weigthnumber[data-v-0fa31b49] {
    width: 21%;
    margin-bottom: 10px;
    background-color: #dbdbdb;
    padding: 7px 0;
    text-align: center;
    border-radius: 5px;
    color: #fff;
    position: relative;
}
<style>
.amountbox[data-v-0fa31b49] {
    font-size: 14px;
    color: #242424;
    padding: 0 14px;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}
，我要卖料选择器：element.style {
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.amountbox .lfbtn[data-v-0fa31b49] {
    width: 100%;
    border: 0.5px solid #2fc959;
    color: #2fc959;
    border-radius: 26px;
}
<style>
.amountbox .btnbox[data-v-0fa31b49] {
    height: 46px;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    position: relative;
}
<style>
.amountbox[data-v-0fa31b49] {
    font-size: 14px;
    color: #242424;
    padding: 0 14px;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，
我要买料选择器：element.style {
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.amountbox .rgbtn[data-v-0fa31b49] {
    width: 100%;
    border: 0.5px solid #e10000;
    color: #e10000;
    border-radius: 26px;
}
<style>
.amountbox .btnbox[data-v-0fa31b49] {
    height: 46px;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    position: relative;
}
<style>
.amountbox[data-v-0fa31b49] {
    font-size: 14px;
    color: #242424;
    padding: 0 14px;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}
以下是我要卖料点击后弹窗

确认按钮选择器：element.style {
    overflow: visible;
    background-color: rgb(47, 201, 89);
    border: 1px solid rgb(47, 201, 89);
}
<style>
.content .orderbtn[data-v-0fa31b49] .u-size-default {
    height: unset;
    line-height: unset;
}
<style>
.content .orderbtn .custom-class[data-v-0fa31b49] {
    margin-top: 15px;
    border-radius: 1px;
    padding: 5px 15px;
    color: #fff;
    border-radius: 5px;
}
<style>
.content .orderbtn[data-v-0fa31b49] uni-button {
    margin: unset;
    padding: unset;
    font-size: unset;
}
<style>
.u-size-default[data-v-476963b6] {
    font-size: 15px;
    height: 40px;
    line-height: 40px;
}
<style>
.u-btn--default[data-v-476963b6] {
    color: #606266;
    border-color: #c0c4cc;
    background-color: #fff;
}
<style>
.u-btn[data-v-476963b6] {
    position: relative;
    border: 0;
    display: inline-flex;
    overflow: visible;
    line-height: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0 20px;
    z-index: 1;
    box-sizing: border-box;
    transition: all .15s;
}
<style>
.u-line-1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
<style>
.u-fix-ios-appearance {
    -webkit-appearance: none;
}
uni-button {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding-left: 14px;
    padding-right: 14px;
    box-sizing: border-box;
    font-size: 18px;
    text-align: center;
    text-decoration: none;
    line-height: 2.55555556;
    border-radius: 5px;
    -webkit-tap-highlight-color: transparent;
    overflow: hidden;
    color: #000;
    background-color: #f8f8f8;
    cursor: pointer;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
uni-button[Attributes Style] {
    -webkit-locale: "en";
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
.content .orderbtn[data-v-0fa31b49] uni-button::after {
    border: unset;
}
<style>
.u-hairline-border[data-v-476963b6]:after {
    content: " ";
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    left: 0;
    top: 0;
    width: 199.8%;
    height: 199.7%;
    -webkit-transform: scale(.5);
    transform: scale(.5);
    border: 1px solid currentColor;
    z-index: 1;
}
<style>
.u-btn[data-v-476963b6]::after {
    border: none;
}
uni-button:after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid rgba(0,0,0,.2);
    -webkit-transform: scale(.5);
    transform: scale(.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: 10px;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，
取消按钮选择器：element.style {
    background: rgb(197, 202, 208);
}
<style>
.content .orderbtn .custom-class[data-v-0fa31b49] {
    margin-top: 15px;
    border-radius: 1px;
    padding: 5px 15px;
    color: #fff;
    border-radius: 5px;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，
以下是我要买料点击后弹窗

取消选择器：element.style {
    background: rgb(197, 202, 208);
}
<style>
.content .orderbtn .custom-class[data-v-0fa31b49] {
    margin-top: 15px;
    border-radius: 1px;
    padding: 5px 15px;
    color: #fff;
    border-radius: 5px;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}，确认按钮选择器：element.style {
    overflow: visible;
    background-color: rgb(225, 0, 0);
    border: 1px solid rgb(225, 0, 0);
}
<style>
.content .orderbtn[data-v-0fa31b49] .u-size-default {
    height: unset;
    line-height: unset;
}
<style>
.content .orderbtn .custom-class[data-v-0fa31b49] {
    margin-top: 15px;
    border-radius: 1px;
    padding: 5px 15px;
    color: #fff;
    border-radius: 5px;
}
<style>
.content .orderbtn[data-v-0fa31b49] uni-button {
    margin: unset;
    padding: unset;
    font-size: unset;
}
<style>
.u-size-default[data-v-476963b6] {
    font-size: 15px;
    height: 40px;
    line-height: 40px;
}
<style>
.u-btn--default[data-v-476963b6] {
    color: #606266;
    border-color: #c0c4cc;
    background-color: #fff;
}
<style>
.u-btn[data-v-476963b6] {
    position: relative;
    border: 0;
    display: inline-flex;
    overflow: visible;
    line-height: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0 20px;
    z-index: 1;
    box-sizing: border-box;
    transition: all .15s;
}
<style>
.u-line-1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
<style>
.u-fix-ios-appearance {
    -webkit-appearance: none;
}
uni-button {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding-left: 14px;
    padding-right: 14px;
    box-sizing: border-box;
    font-size: 18px;
    text-align: center;
    text-decoration: none;
    line-height: 2.55555556;
    border-radius: 5px;
    -webkit-tap-highlight-color: transparent;
    overflow: hidden;
    color: #000;
    background-color: #f8f8f8;
    cursor: pointer;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
uni-button[Attributes Style] {
    -webkit-locale: "en";
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
.content .orderbtn[data-v-0fa31b49] uni-button::after {
    border: unset;
}
<style>
.u-hairline-border[data-v-476963b6]:after {
    content: " ";
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    left: 0;
    top: 0;
    width: 199.8%;
    height: 199.7%;
    -webkit-transform: scale(.5);
    transform: scale(.5);
    border: 1px solid currentColor;
    z-index: 1;
}
<style>
.u-btn[data-v-476963b6]::after {
    border: none;
}
uni-button:after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid rgba(0,0,0,.2);
    -webkit-transform: scale(.5);
    transform: scale(.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: 10px;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}

登录成功后的页面地址：https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
平台标志LOGO图片选择器：element.style {
    background-image: url(https://img.em9999.com/images/20250113/IMG20250113302065.jpg);
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
}
uni-image>div, uni-image>img {
    width: 100%;
    height: 100%;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
用户代理样式表
div {
    display: block;
}
uni-swiper-item {
    display: block;
    overflow: hidden;
    will-change: transform;
    position: absolute;
    width: 100%;
    height: 100%;
    cursor: grab;
}
<style>
.drak-theme[data-v-6a432cca] {
    width: 100%;
    min-height: 100vh;
    background-color: #000;
    color: #fff;
}
<style>
.container {
    font-size: 14px;
    padding-bottom: 15px;
    padding-bottom: calc(env(safe-area-inset-bottom) + 85px) /* 底部安全区域高度加 50px 的高度 */;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: calc(120px + env(safe-area-inset-bottom));
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}

会员中心地址：https://j.jtd9999.vip/h5/#/pages/mine/mine?demp_code=944440b68743bdaaf6e4cf7b5745893e
验证登录状态，ID选择器：uni-page-body {
    color: #303133;
    font-size: 14px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: calc(120px + env(safe-area-inset-bottom));
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}
订单页退/付定金选择器：element.style {
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.nav-box .nav-back .nav-user .auth-button[data-v-1ffc035e] {
    padding: 5px 14px;
    border: 1px solid #fee4d1;
    border-radius: 28px;
    font-size: 14px;
    color: #6b3617;
    background: linear-gradient(#ffebd8,#f8bea9);
}
<style>
.nav-box .nav-back[data-v-1ffc035e] {
    width: 100%;
    height: 184px;
    color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 16px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}
批量退款按钮选择器：element.style {
}
<style>
.batch-into .batch-button[data-v-14224f5e] {
    padding: 4px 11px;
    background-color: #de1a1a;
    color: #fff;
    border-radius: 5px;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 16px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}
订单页开仓价选择器：element.style {
}
<style>
.per-price[data-v-14224f5e], .new-price[data-v-14224f5e] {
    font-size: 16px;
}
<style>
uni-view, uni-text {
    box-sizing: border-box;
}
uni-view {
    display: block;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
* {
    margin: 0;
    -webkit-tap-highlight-color: transparent;
}
<style>
.puwidth[data-v-14224f5e] {
    width: 98%;
    margin: auto;
    font-size: 17px;
    background-color: #fff;
    padding: 11px 11px;
    border-radius: 5px;
}
<style>
uni-page-body {
    color: #303133;
    font-size: 16px;
}
body, uni-page-body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
}
body, html {
    -webkit-user-select: none;
    user-select: none;
    width: 100%;
    height: 100%;
}
style 属性 {
    --status-bar-height: 0px;
    --top-window-height: 0px;
    --window-left: 0px;
    --window-right: 0px;
    --window-margin: 0px;
    --window-top: calc(var(--top-window-height) + 0px);
    --window-bottom: 0px;
}
html {
    --UI-BG: #fff;
    --UI-BG-1: #f7f7f7;
    --UI-BG-2: #fff;
    --UI-BG-3: #f7f7f7;
    --UI-BG-4: #4c4c4c;
    --UI-BG-5: #fff;
    --UI-FG: #000;
    --UI-FG-0: rgba(0,0,0,0.9);
    --UI-FG-HALF: rgba(0,0,0,0.9);
    --UI-FG-1: rgba(0,0,0,0.5);
    --UI-FG-2: rgba(0,0,0,0.3);
    --UI-FG-3: rgba(0,0,0,0.1);
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
*, *:before, *:after {
    box-sizing: inherit;
}
<style>
::-webkit-scrollbar {
    display: none;
    width: 0!important;
    height: 0!important;
    -webkit-appearance: none;
    background: transparent;
}