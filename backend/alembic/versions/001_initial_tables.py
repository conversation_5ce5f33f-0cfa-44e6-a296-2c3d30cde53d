"""Initial tables

Revision ID: 001
Revises:
Create Date: 2023-06-26 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # 创建用户表
    op.create_table(
        'users',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('username', sa.String(50), unique=True, nullable=False),
        sa.Column('password_hash', sa.String(100), nullable=False),
        sa.Column('email', sa.String(100), nullable=True),
        sa.Column('phone', sa.String(20), nullable=True),
        sa.Column('role', sa.String(20), server_default='user', nullable=False),
        sa.Column('is_active', sa.Boolean, server_default='true', nullable=False),
        sa.Column('last_login', sa.DateTime, nullable=True),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False)
    )

    # 创建账户表
    op.create_table(
        'accounts',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('user_id', sa.String(36), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('account_type', sa.String(10), nullable=False),
        sa.Column('platform_name', sa.String(100), nullable=False),
        sa.Column('platform_url', sa.String(200), nullable=True),
        sa.Column('contract_code', sa.String(20), nullable=True),
        sa.Column('username', sa.String(50), nullable=False),
        sa.Column('password_encrypted', sa.String(200), nullable=False),
        sa.Column('cost_per_gram', sa.Float, nullable=False),
        sa.Column('is_active', sa.Boolean, server_default='true', nullable=False),
        sa.Column('connection_status', sa.String(20), server_default='disconnected', nullable=False),
        sa.Column('last_connection_time', sa.DateTime, nullable=True),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False)
    )

    # 创建设置表
    op.create_table(
        'settings',
        sa.Column('id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.String(36), sa.ForeignKey('users.id'), unique=True, nullable=False),
        sa.Column('target_profit_diff', sa.Float, server_default='0.5', nullable=False),
        sa.Column('position_size', sa.Integer, server_default='1', nullable=False),
        sa.Column('max_positions', sa.Integer, server_default='5', nullable=False),
        sa.Column('max_daily_cycles', sa.Integer, server_default='10', nullable=False),
        sa.Column('spot_refresh_rate', sa.Integer, server_default='3', nullable=False),
        sa.Column('base_weight', sa.Integer, server_default='1000', nullable=False),
        sa.Column('forward_basis_ranges', sa.JSON, server_default='[]', nullable=False),
        sa.Column('reverse_basis_ranges', sa.JSON, server_default='[]', nullable=False),
        sa.Column('auto_trade_enabled', sa.Boolean, server_default='false', nullable=False),
        sa.Column('trading_times', sa.JSON, server_default='[]', nullable=False),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False)
    )

    # 创建订单表
    op.create_table(
        'orders',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('user_id', sa.String(36), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('direction', sa.Integer, nullable=False),
        sa.Column('status', sa.String(20), server_default='open', nullable=False),
        sa.Column('spot_order_id', sa.String(36), nullable=True),
        sa.Column('future_order_id', sa.String(64), nullable=True),  # 扩展长度以支持TqSDK订单ID
        sa.Column('volume', sa.Integer, server_default='1', nullable=False),
        sa.Column('base_weight', sa.Integer, server_default='1000', nullable=False),

        # 开仓信息
        sa.Column('spot_open_price', sa.Float, nullable=False),
        sa.Column('future_open_price', sa.Float, nullable=False),
        sa.Column('open_basis', sa.Float, nullable=False),
        sa.Column('target_basis', sa.Float, nullable=False),
        sa.Column('open_time', sa.DateTime, nullable=False),

        # 平仓信息
        sa.Column('spot_close_price', sa.Float, nullable=True),
        sa.Column('future_close_price', sa.Float, nullable=True),
        sa.Column('close_basis', sa.Float, nullable=True),
        sa.Column('close_time', sa.DateTime, nullable=True),

        # 交易成本
        sa.Column('spot_cost', sa.Float, nullable=False),
        sa.Column('future_cost', sa.Float, nullable=False),
        sa.Column('total_cost', sa.Float, nullable=False),

        # 盈亏信息
        sa.Column('spot_pnl', sa.Float, nullable=True),
        sa.Column('future_pnl', sa.Float, nullable=True),
        sa.Column('total_pnl', sa.Float, nullable=True),
        sa.Column('net_profit', sa.Float, nullable=True),

        sa.Column('created_at', sa.DateTime, server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False)
    )

    # 创建系统日志表
    op.create_table(
        'system_logs',
        sa.Column('id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.String(36), sa.ForeignKey('users.id'), nullable=True),
        sa.Column('level', sa.String(20), nullable=False),
        sa.Column('category', sa.String(50), nullable=False),
        sa.Column('message', sa.String(500), nullable=False),
        sa.Column('details', sa.JSON, nullable=True),
        sa.Column('ip_address', sa.String(50), nullable=True),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now(), nullable=False)
    )

    # 创建通知表
    op.create_table(
        'notifications',
        sa.Column('id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.String(36), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('type', sa.String(20), nullable=False),
        sa.Column('title', sa.String(100), nullable=False),
        sa.Column('content', sa.String(500), nullable=False),
        sa.Column('is_read', sa.Boolean, server_default='false', nullable=False),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now(), nullable=False),
        sa.Column('read_at', sa.DateTime, nullable=True)
    )

    # 创建交易配置表
    op.create_table(
        'trade_configs',
        sa.Column('id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.String(36), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('name', sa.String(100), nullable=False),
        sa.Column('target_profit_diff', sa.Float, server_default='0.5', nullable=False),
        sa.Column('position_size', sa.Integer, server_default='1', nullable=False),
        sa.Column('base_weight', sa.Integer, server_default='1000', nullable=False),
        sa.Column('is_default', sa.Boolean, server_default='false', nullable=False),
        sa.Column('created_at', sa.DateTime, server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime, server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False)
    )

    # 创建索引
    op.create_index('ix_users_username', 'users', ['username'], unique=True)
    op.create_index('ix_accounts_user_id', 'accounts', ['user_id'])
    op.create_index('ix_settings_user_id', 'settings', ['user_id'], unique=True)
    op.create_index('ix_orders_user_id', 'orders', ['user_id'])
    op.create_index('ix_orders_status', 'orders', ['status'])
    op.create_index('ix_system_logs_user_id', 'system_logs', ['user_id'])
    op.create_index('ix_system_logs_level', 'system_logs', ['level'])
    op.create_index('ix_system_logs_category', 'system_logs', ['category'])
    op.create_index('ix_notifications_user_id', 'notifications', ['user_id'])
    op.create_index('ix_notifications_is_read', 'notifications', ['is_read'])
    op.create_index('ix_trade_configs_user_id', 'trade_configs', ['user_id'])
    op.create_index('ix_trade_configs_is_default', 'trade_configs', ['is_default'])


def downgrade():
    # 删除表
    op.drop_table('trade_configs')
    op.drop_table('notifications')
    op.drop_table('system_logs')
    op.drop_table('orders')
    op.drop_table('settings')
    op.drop_table('accounts')
    op.drop_table('users')
