"""
日志工具
"""
import logging
import sys
import time
from datetime import datetime
from logging.handlers import RotatingFileHandler
import os
from typing import Dict, Any, Optional

# 导入日志抑制器
from .log_suppressor import get_suppressed_logger, log_suppressor

# 配置日志抑制器
log_suppressor.configure(suppression_time=300, enabled=True)  # 5分钟抑制时间

def setup_logger(name: str, log_file: str = None, level: int = logging.INFO,
                 suppress: bool = True) -> logging.Logger:
    """
    设置日志记录器

    Args:
        name: 日志记录器名称
        log_file: 日志文件路径
        level: 日志级别
        suppress: 是否启用日志抑制

    Returns:
        日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)

    # 清除现有处理器，避免重复
    if logger.handlers:
        logger.handlers.clear()

    logger.setLevel(level)

    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 如果提供了日志文件路径，创建文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 创建轮转文件处理器
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10 MB
            backupCount=5
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    # 如果启用日志抑制，返回带抑制功能的日志记录器
    if suppress:
        return get_suppressed_logger(name)

    return logger

# 日志消息缓存，用于跟踪重复日志
_log_cache: Dict[str, Dict[str, Any]] = {}

def setup_trade_logger(name: str = "trade_close", log_file: str = None) -> logging.Logger:
    """
    设置专门的交易日志记录器（用于平仓等交易操作）

    Args:
        name: 日志记录器名称
        log_file: 日志文件路径，默认为 /gold/logs/trade_close.log

    Returns:
        交易日志记录器
    """
    if log_file is None:
        log_file = "/gold/logs/trade_close.log"

    # 确保日志目录存在
    os.makedirs("/gold/logs", exist_ok=True)

    # 创建日志记录器
    logger = logging.getLogger(name)

    # 清除现有处理器，避免重复
    if logger.handlers:
        logger.handlers.clear()

    logger.setLevel(logging.DEBUG)  # 设置为DEBUG级别以记录详细信息

    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建轮转文件处理器
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=50 * 1024 * 1024,  # 50 MB
        backupCount=10
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 创建控制台处理器（仅ERROR级别以上）
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.ERROR)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger
