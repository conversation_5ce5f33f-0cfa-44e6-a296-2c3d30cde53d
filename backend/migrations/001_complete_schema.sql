-- 完整的数据库迁移脚本
-- 包含所有必要的表结构和字段

-- 创建数据库版本表
CREATE TABLE IF NOT EXISTS db_version (
    id SERIAL PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建迁移记录表
CREATE TABLE IF NOT EXISTS migrations (
    name VARCHAR(100) PRIMARY KEY,
    executed_at TIMESTAMP NOT NULL
);

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    role VARCHAR(20) NOT NULL DEFAULT 'user',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_login TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建账户表
CREATE TABLE IF NOT EXISTS accounts (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    account_type VARCHAR(10) NOT NULL,
    platform_name VARCHAR(100) NOT NULL,
    platform_url VARCHAR(200),
    contract_code VARCHAR(20),
    username VARCHAR(50) NOT NULL,
    password_encrypted VARCHAR(200) NOT NULL,
    cost_per_gram DECIMAL(10, 4) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    connection_status VARCHAR(20) NOT NULL DEFAULT 'disconnected',
    last_connection_time TIMESTAMP,
    broker_id VARCHAR(20),
    td_server VARCHAR(100),
    md_server VARCHAR(100),
    app_id VARCHAR(50),
    auth_code VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_id, account_type)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS ix_accounts_broker_id ON accounts(broker_id);

-- 创建设置表
CREATE TABLE IF NOT EXISTS settings (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    target_profit_diff DECIMAL(10, 4) NOT NULL DEFAULT 0.5,
    position_size INTEGER NOT NULL DEFAULT 1,
    max_positions INTEGER NOT NULL DEFAULT 5,
    max_daily_cycles INTEGER NOT NULL DEFAULT 10,
    spot_refresh_rate INTEGER NOT NULL DEFAULT 3,
    base_weight INTEGER NOT NULL DEFAULT 1000,
    forward_basis_ranges JSONB NOT NULL DEFAULT '[]',
    reverse_basis_ranges JSONB NOT NULL DEFAULT '[]',
    auto_trade_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    trading_times JSONB NOT NULL DEFAULT '[]',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_id)
);

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    direction INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'open',
    spot_order_id VARCHAR(36),
    future_order_id VARCHAR(64),  -- 扩展长度以支持TqSDK订单ID
    volume INTEGER NOT NULL DEFAULT 1,
    base_weight INTEGER NOT NULL DEFAULT 1000,

    -- 开仓信息
    spot_open_price DECIMAL(10, 2) NOT NULL,
    future_open_price DECIMAL(10, 2) NOT NULL,
    open_basis DECIMAL(10, 2) NOT NULL,
    target_basis DECIMAL(10, 2) NOT NULL,
    open_time TIMESTAMP NOT NULL,

    -- 平仓信息
    spot_close_price DECIMAL(10, 2),
    future_close_price DECIMAL(10, 2),
    close_basis DECIMAL(10, 2),
    close_time TIMESTAMP,
    spot_close_order_id VARCHAR(36),
    future_close_order_id VARCHAR(64),  -- 扩展长度以支持TqSDK订单ID
    cancel_reason VARCHAR(200),

    -- 交易成本
    spot_cost DECIMAL(10, 2) NOT NULL,
    future_cost DECIMAL(10, 2) NOT NULL,
    total_cost DECIMAL(10, 2) NOT NULL,

    -- 盈亏信息
    spot_pnl DECIMAL(10, 2),
    future_pnl DECIMAL(10, 2),
    total_pnl DECIMAL(10, 2),
    net_profit DECIMAL(10, 2),

    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建风控限制表
CREATE TABLE IF NOT EXISTS risk_limits (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    daily_pnl_limit DECIMAL(10, 2) NOT NULL DEFAULT 10000.0,
    max_drawdown_limit DECIMAL(10, 2) NOT NULL DEFAULT 5000.0,
    max_positions INTEGER NOT NULL DEFAULT 10,
    max_daily_trades INTEGER NOT NULL DEFAULT 50,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_id)
);

-- 创建风控指标表
CREATE TABLE IF NOT EXISTS risk_metrics (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    daily_pnl DECIMAL(10, 2) NOT NULL DEFAULT 0.0,
    max_drawdown DECIMAL(10, 2) NOT NULL DEFAULT 0.0,
    risk_status VARCHAR(20) NOT NULL DEFAULT 'normal',
    metrics_data JSONB,
    date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建风控日志表
CREATE TABLE IF NOT EXISTS risk_logs (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    log_id VARCHAR(36) NOT NULL,
    type VARCHAR(20) NOT NULL DEFAULT 'risk_alert',
    message TEXT NOT NULL,
    metrics_data JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建自动交易设置表
CREATE TABLE IF NOT EXISTS auto_trade_settings (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL REFERENCES users(id),
    enabled BOOLEAN NOT NULL DEFAULT FALSE,
    max_positions INTEGER NOT NULL DEFAULT 5,
    max_daily_trades INTEGER NOT NULL DEFAULT 20,
    settings_data JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_id)
);

-- 记录迁移版本
INSERT INTO migrations (name, executed_at)
VALUES ('001_complete_schema.sql', CURRENT_TIMESTAMP)
ON CONFLICT (name) DO UPDATE SET executed_at = CURRENT_TIMESTAMP;

-- 更新数据库版本
INSERT INTO db_version (version, description)
VALUES ('1.0.0', '完整数据库结构')
ON CONFLICT (version) DO NOTHING;
