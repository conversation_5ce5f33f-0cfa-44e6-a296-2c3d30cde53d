-- 修复期货订单ID字段长度限制
-- TqSDK返回的订单ID格式为 PYSDK_insert_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (48字符)
-- 原来的VARCHAR(36)不够用，需要扩展到VARCHAR(64)

ALTER TABLE orders ALTER COLUMN future_order_id TYPE VARCHAR(64);
ALTER TABLE orders ALTER COLUMN future_close_order_id TYPE VARCHAR(64);

-- 记录迁移
INSERT INTO migrations (name, executed_at) 
VALUES ('fix_future_order_id_length.sql', CURRENT_TIMESTAMP)
ON CONFLICT (name) DO UPDATE SET executed_at = CURRENT_TIMESTAMP;
