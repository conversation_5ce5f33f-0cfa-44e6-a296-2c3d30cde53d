"""
订单相关路由
"""
from fastapi import APIRouter, Query, Path, Depends, HTTPException, status, Request
from typing import Optional, Dict, Any, List
import sys
import os
import logging
import traceback

# 设置日志
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from services.order_service import OrderService
from models.order import Order, OrderCreate, OrderUpdate, PositionsResponse, HistoryOrdersResponse, TradeResponse
from models.user import User
from api.deps import get_current_user
from data_storage.db_manager import db_manager

router = APIRouter(tags=["订单"])

# 创建订单服务实例
postgres_client = db_manager.get_postgres()
order_service = OrderService(postgres_client)

@router.get("/orders/positions", response_model=PositionsResponse)
async def get_positions(current_user: User = Depends(get_current_user)) -> PositionsResponse:
    """
    获取当前持仓列表

    Args:
        current_user: 当前用户

    Returns:
        持仓列表响应

    Raises:
        HTTPException: 获取失败
    """
    try:
        return await order_service.get_positions(current_user.id)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取持仓列表失败: {str(e)}"
        )

@router.get("/orders/history", response_model=HistoryOrdersResponse)
async def get_history_orders(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_user)
) -> HistoryOrdersResponse:
    """
    获取历史订单列表

    Args:
        page: 页码
        page_size: 每页数量
        current_user: 当前用户

    Returns:
        历史订单列表响应

    Raises:
        HTTPException: 获取失败
    """
    try:
        return await order_service.get_history_orders(current_user.id, page, page_size)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取历史订单列表失败: {str(e)}"
        )

@router.post("/trade/open", response_model=TradeResponse)
async def open_position(
    order_data: OrderCreate,
    current_user: User = Depends(get_current_user),
    request: Request = None
) -> TradeResponse:
    """
    开仓操作

    Args:
        order_data: 订单创建数据
        current_user: 当前用户
        request: 请求对象，用于获取原始请求数据

    Returns:
        交易响应

    Raises:
        HTTPException: 开仓失败
    """
    try:
        # 记录请求数据，帮助调试
        logger.info(f"开仓请求数据: {order_data.model_dump() if hasattr(order_data, 'model_dump') else order_data}")

        # 如果请求对象可用，记录原始请求数据
        if request:
            try:
                body = await request.json()
                logger.info(f"原始请求数据: {body}")
            except Exception as e:
                logger.warning(f"无法解析请求体: {e}")

        # 设置用户ID（即使前端提供了，也使用当前认证用户的ID）
        order_data.user_id = current_user.id

        # 验证必要字段
        if not hasattr(order_data, 'spot_open_price') and hasattr(order_data, 'spot_price'):
            order_data.spot_open_price = order_data.spot_price

        if not hasattr(order_data, 'future_open_price') and hasattr(order_data, 'future_price'):
            order_data.future_open_price = order_data.future_price

        # 计算缺失的字段
        if not order_data.open_basis and order_data.spot_open_price and order_data.future_open_price:
            if order_data.direction == 1:
                order_data.open_basis = order_data.spot_open_price - order_data.future_open_price
            else:
                order_data.open_basis = order_data.future_open_price - order_data.spot_open_price

        if not order_data.target_basis and order_data.open_basis:
            order_data.target_basis = order_data.open_basis + (0.5 if order_data.direction == 1 else -0.5)

        if not order_data.spot_cost and order_data.base_weight:
            order_data.spot_cost = 0.4 * order_data.base_weight * order_data.volume / 100

        if not order_data.future_cost and order_data.base_weight:
            order_data.future_cost = 0.01 * order_data.base_weight * order_data.volume / 100

        if not order_data.total_cost and order_data.spot_cost and order_data.future_cost:
            order_data.total_cost = order_data.spot_cost + order_data.future_cost

        # 记录处理后的请求数据
        logger.info(f"处理后的开仓请求数据: {order_data.model_dump() if hasattr(order_data, 'model_dump') else order_data}")

        # 验证所有必需字段是否都有值
        required_fields = ['spot_open_price', 'future_open_price', 'open_basis', 'target_basis', 'spot_cost', 'future_cost', 'total_cost']
        missing_fields = [field for field in required_fields if not getattr(order_data, field, None)]

        if missing_fields:
            logger.error(f"缺少必需字段: {missing_fields}")
            raise ValueError(f"缺少必需字段: {', '.join(missing_fields)}")

        # 创建订单
        order = await order_service.open_position(order_data)

        # 调用交易服务执行实际交易
        from services.trade_service import TradeService
        trade_service = TradeService()

        # 记录日志
        logger.info(f"订单创建成功，开始执行实际交易: {order.id}")

        # 执行交易 - 使用 open_position 方法
        trade_result = await trade_service.open_position(
            user_id=current_user.id,
            direction=order_data.direction
        )

        # 根据交易结果返回响应
        if trade_result.success:
            return trade_result
        else:
            # 如果交易失败，返回失败响应
            return trade_result
    except ValueError as e:
        logger.error(f"开仓验证错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"开仓失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"开仓失败: {str(e)}"
        )


