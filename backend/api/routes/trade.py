"""
交易相关路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Path, Query, Request
from typing import Dict, Any, Optional
import sys
import os
import logging

# 设置日志
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from services.trade_service import TradeService
from models.order import TradeResponse
from models.user import User
from api.deps import get_current_user

router = APIRouter(prefix="/trade", tags=["交易"])
# 创建服务实例，但不立即初始化异步部分
trade_service = TradeService()

# 在应用启动时初始化
# 注意：这个函数将在 main.py 中的 startup_event 中被调用
async def initialize_trade_service():
    """在应用启动时初始化交易服务"""
    await trade_service.initialize()
    logger.info("交易服务初始化完成")

@router.post("/open", response_model=TradeResponse)
async def open_position(
    direction: Optional[int] = Query(None, description="交易方向，1为正向套利，-1为反向套利"),
    current_user: User = Depends(get_current_user),
    request: Request = None
) -> TradeResponse:
    """
    开仓接口

    Args:
        direction: 交易方向，1为正向套利，-1为反向套利
        current_user: 当前用户
        request: 请求对象

    Returns:
        交易响应
    """
    from services.trade_service import TradeService
    from services.market_service import MarketService
    import os
    import logging
    from datetime import datetime

    # 确保日志目录存在
    os.makedirs("/gold/logs", exist_ok=True)

    # 记录API请求到专门的日志文件
    with open("/gold/logs/api_requests.log", "a") as f:
        f.write(f"{datetime.now().isoformat()} - 收到开仓API请求: 用户ID={current_user.id}, 方向={direction}\n")

    # 使用多种日志级别记录请求，确保在任何日志级别下都能看到
    logger.critical(f"收到开仓API请求: 用户ID={current_user.id}, 方向={direction}")
    logger.error(f"处理开仓API请求: 用户ID={current_user.id}, 方向={direction}")
    logger.warning(f"开仓API请求参数: 用户ID={current_user.id}, 方向={direction}")
    logger.info(f"收到开仓请求: 用户ID={current_user.id}, 方向={direction}")

    # 打印到控制台
    print(f"[API] 收到开仓请求: 用户ID={current_user.id}, 方向={direction}, 时间={datetime.now().isoformat()}")

    # 如果未指定方向，尝试从请求体中获取
    if direction is None and request:
        try:
            body = await request.json()
            direction = body.get("direction")
            logger.info(f"从请求体获取到方向: {direction}")
        except Exception as e:
            logger.error(f"解析请求体失败: {e}")

    # 如果仍未获取到方向，返回错误
    if direction is None:
        logger.error("未指定交易方向")
        return TradeResponse(
            success=False,
            message="未指定交易方向，请传入 direction 参数（1为正向套利，-1为反向套利）",
            order_id=None
        )

    # 确保direction是整数
    try:
        actual_direction = int(direction)
        if actual_direction not in [1, -1]:
            logger.error(f"无效的交易方向: {actual_direction}")
            return TradeResponse(
                success=False,
                message="无效的交易方向，direction 只能是 1（正向套利）或 -1（反向套利）",
                order_id=None
            )
    except (ValueError, TypeError):
        logger.error(f"交易方向不是有效的整数: {direction}")
        return TradeResponse(
            success=False,
            message="交易方向必须是整数值，1为正向套利，-1为反向套利",
            order_id=None
        )

    # 获取最新市场数据
    try:
        market_service = MarketService()
        market_data = await market_service.get_latest_market_data()

        # 记录当前市场价格，用于日志和调试
        if actual_direction == 1:  # 正向套利
            spot_price = market_data.spot_ask  # 现货买入价
            future_price = market_data.future_bid  # 期货卖出价
            basis = spot_price - future_price
            logger.info(f"当前市场价格 - 正向套利: 现货买入={spot_price}, 期货卖出={future_price}, 基差={basis}")
        else:  # 反向套利
            spot_price = market_data.spot_bid  # 现货卖出价
            future_price = market_data.future_ask  # 期货买入价
            basis = spot_price - future_price
            logger.info(f"当前市场价格 - 反向套利: 现货卖出={spot_price}, 期货买入={future_price}, 基差={basis}")
    except Exception as e:
        logger.warning(f"获取最新市场数据失败: {e}，将继续执行交易")

    # 创建交易服务
    trade_service = TradeService()

    try:
        # 执行开仓操作
        logger.info(f"开始执行开仓操作: 用户ID={current_user.id}, 方向={actual_direction}")
        print(f"[API] 调用TradeService.open_position: 用户ID={current_user.id}, 方向={actual_direction}")

        # 记录到专门的日志文件
        with open("/gold/logs/api_execution.log", "a") as f:
            f.write(f"{datetime.now().isoformat()} - 开始执行开仓操作: 用户ID={current_user.id}, 方向={actual_direction}\n")

        # 执行开仓操作
        response = await trade_service.open_position(current_user.id, actual_direction)

        # 记录响应结果
        if response.success:
            logger.critical(f"开仓API调用成功: 用户ID={current_user.id}, 订单ID={response.order_id}")
            logger.error(f"开仓操作成功: 用户ID={current_user.id}, 订单ID={response.order_id}")
            logger.warning(f"开仓成功: 用户ID={current_user.id}, 订单ID={response.order_id}")
            logger.info(f"开仓成功: 用户ID={current_user.id}, 订单ID={response.order_id}")
            print(f"[API] 开仓成功: 用户ID={current_user.id}, 订单ID={response.order_id}, 时间={datetime.now().isoformat()}")

            # 记录到专门的日志文件
            with open("/gold/logs/api_responses.log", "a") as f:
                f.write(f"{datetime.now().isoformat()} - 开仓成功: 用户ID={current_user.id}, 订单ID={response.order_id}\n")
        else:
            logger.critical(f"开仓API调用失败: 用户ID={current_user.id}, 错误信息={response.message}")
            logger.error(f"开仓失败: 用户ID={current_user.id}, 错误信息={response.message}")
            print(f"[API] 开仓失败: 用户ID={current_user.id}, 错误信息={response.message}, 时间={datetime.now().isoformat()}")

            # 记录到专门的日志文件
            with open("/gold/logs/api_errors.log", "a") as f:
                f.write(f"{datetime.now().isoformat()} - 开仓失败: 用户ID={current_user.id}, 错误信息={response.message}\n")

        return response
    except Exception as e:
        error_message = f"开仓操作异常: {e}"
        logger.critical(error_message)
        logger.error(error_message, exc_info=True)
        print(f"[API] 开仓操作异常: {e}, 时间={datetime.now().isoformat()}")

        # 记录到专门的日志文件
        with open("/gold/logs/api_exceptions.log", "a") as f:
            f.write(f"{datetime.now().isoformat()} - 开仓操作异常: {e}\n")
            import traceback
            f.write(traceback.format_exc() + "\n")

        return TradeResponse(
            success=False,
            message=f"开仓操作异常: {str(e)}",
            order_id=None
        )

@router.post("/close/{order_id}", response_model=TradeResponse)
async def close_position(
    order_id: str = Path(..., description="订单ID"),
    current_user: User = Depends(get_current_user)
) -> TradeResponse:
    """
    平仓操作

    Args:
        order_id: 订单ID
        current_user: 当前用户

    Returns:
        交易响应

    Raises:
        HTTPException: 平仓失败
    """
    from datetime import datetime
    from utils.logger import setup_trade_logger

    # 设置专门的平仓日志记录器
    trade_logger = setup_trade_logger("trade_close_api", "/gold/logs/trade_close.log")

    # 记录API请求开始
    trade_logger.info(f"=== 平仓API请求开始 ===")
    trade_logger.info(f"用户ID: {current_user.id}")
    trade_logger.info(f"订单ID: {order_id}")
    trade_logger.info(f"请求时间: {datetime.now().isoformat()}")

    # 同时记录到控制台（用于实时监控）
    print(f"[CLOSE_API] 收到平仓请求: 用户ID={current_user.id}, 订单ID={order_id}, 时间={datetime.now().isoformat()}")

    try:
        # 创建新的交易服务实例，确保正确初始化
        trade_service_instance = TradeService()

        trade_logger.info(f"开始执行平仓操作...")

        # 执行平仓操作
        response = await trade_service_instance.close_position(current_user.id, order_id)

        # 记录响应结果
        if response.success:
            trade_logger.info(f"=== 平仓操作成功 ===")
            trade_logger.info(f"订单ID: {order_id}")
            trade_logger.info(f"平仓时间: {response.close_time if hasattr(response, 'close_time') else '未知'}")
            trade_logger.info(f"响应消息: {response.message}")

            print(f"[CLOSE_API] 平仓成功: 用户ID={current_user.id}, 订单ID={order_id}")
        else:
            trade_logger.error(f"=== 平仓操作失败 ===")
            trade_logger.error(f"订单ID: {order_id}")
            trade_logger.error(f"错误信息: {response.message}")
            trade_logger.error(f"失败步骤: {getattr(response, 'error_step', '未知')}")

            print(f"[CLOSE_API] 平仓失败: 用户ID={current_user.id}, 订单ID={order_id}, 错误={response.message}")

        trade_logger.info(f"=== 平仓API请求结束 ===")
        return response

    except ValueError as e:
        error_message = f"平仓参数错误: {e}"
        trade_logger.error(f"=== 平仓参数错误 ===")
        trade_logger.error(f"订单ID: {order_id}")
        trade_logger.error(f"错误详情: {error_message}")

        print(f"[CLOSE_API] 参数错误: {e}")

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        error_message = f"平仓操作异常: {e}"
        trade_logger.error(f"=== 平仓操作异常 ===")
        trade_logger.error(f"订单ID: {order_id}")
        trade_logger.error(f"异常详情: {error_message}")
        trade_logger.error(f"异常堆栈:", exc_info=True)

        print(f"[CLOSE_API] 操作异常: {e}")

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"平仓失败: {str(e)}"
        )

@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    交易服务健康检查

    Returns:
        服务状态信息
    """
    try:
        from services.spot_service_manager import spot_service_manager
        from services.future_service_manager import future_service_manager

        # 检查现货服务状态
        spot_service = await spot_service_manager.get_service()
        spot_status = {
            "available": spot_service is not None,
            "logged_in": spot_service.logged_in if spot_service else False,
            "account_id": spot_service.account_id if spot_service else None
        }

        # 检查期货服务状态
        future_service = await future_service_manager.get_service()
        future_status = {
            "available": future_service is not None,
            "connected": True if future_service else False  # 简化检查
        }

        # 整体状态
        overall_status = spot_status["available"] and future_status["available"]

        return {
            "status": "healthy" if overall_status else "degraded",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "spot": spot_status,
                "future": future_status
            }
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@router.post("/auto")
async def auto_trade(current_user: User = Depends(get_current_user)) -> Dict[str, Any]:
    """
    自动交易

    Args:
        current_user: 当前用户

    Returns:
        自动交易结果

    Raises:
        HTTPException: 自动交易失败
    """
    try:
        return await trade_service.auto_trade(current_user.id)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"自动交易失败: {str(e)}"
        )
