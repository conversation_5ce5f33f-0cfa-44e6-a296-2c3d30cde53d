"""
数据库模型
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, JSON, Text, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

Base = declarative_base()

class User(Base):
    """用户表"""
    __tablename__ = "users"

    id = Column(String(36), primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(100), nullable=False)
    email = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    role = Column(String(20), default="user", nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    last_login = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # 关系
    accounts = relationship("Account", back_populates="user", cascade="all, delete-orphan")
    settings = relationship("UserSettings", back_populates="user", uselist=False, cascade="all, delete-orphan")
    orders = relationship("Order", back_populates="user", cascade="all, delete-orphan")

class Account(Base):
    """账户表"""
    __tablename__ = "accounts"

    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    account_type = Column(String(10), nullable=False)  # 'spot' or 'future'
    platform_name = Column(String(100), nullable=False)
    platform_url = Column(String(200), nullable=True)
    contract_code = Column(String(20), nullable=True)
    username = Column(String(50), nullable=False)
    password_encrypted = Column(String(200), nullable=False)
    cost_per_gram = Column(Float, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    connection_status = Column(String(20), default="disconnected", nullable=False)
    last_connection_time = Column(DateTime, nullable=True)
    # CTP交易所需的字段
    broker_id = Column(String(50), nullable=True)
    td_server = Column(String(255), nullable=True)
    md_server = Column(String(255), nullable=True)
    app_id = Column(String(100), nullable=True)
    auth_code = Column(String(100), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # 关系
    user = relationship("User", back_populates="accounts")

    # 唯一约束
    __table_args__ = (
        {"sqlite_autoincrement": True},
    )

class UserSettings(Base):
    """用户设置表"""
    __tablename__ = "settings"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(36), ForeignKey("users.id"), unique=True, nullable=False)
    target_profit_diff = Column(Float, default=0.5, nullable=False)
    position_size = Column(Integer, default=1, nullable=False)
    max_positions = Column(Integer, default=5, nullable=False)
    max_daily_cycles = Column(Integer, default=10, nullable=False)
    spot_refresh_rate = Column(Integer, default=3, nullable=False)
    base_weight = Column(Integer, default=1000, nullable=False)
    forward_basis_ranges = Column(JSON, default=[], nullable=False)
    reverse_basis_ranges = Column(JSON, default=[], nullable=False)
    auto_trade_enabled = Column(Boolean, default=False, nullable=False)
    trading_times = Column(JSON, default=[], nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # 关系
    user = relationship("User", back_populates="settings")

    # 唯一约束
    __table_args__ = (
        {"sqlite_autoincrement": True},
    )

class OrderStatus(enum.Enum):
    """订单状态枚举"""
    OPEN = "open"
    CLOSED = "closed"
    CANCELLED = "cancelled"
    ERROR = "error"

class Order(Base):
    """订单表"""
    __tablename__ = "orders"

    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    direction = Column(Integer, nullable=False)  # 1: 正向, -1: 反向
    status = Column(Enum(OrderStatus), default=OrderStatus.OPEN, nullable=False)
    spot_order_id = Column(String(36), nullable=True)
    future_order_id = Column(String(64), nullable=True)  # 扩展长度以支持TqSDK订单ID
    volume = Column(Integer, default=1, nullable=False)
    base_weight = Column(Integer, default=1000, nullable=False)

    # 开仓信息
    spot_open_price = Column(Float, nullable=False)
    future_open_price = Column(Float, nullable=False)
    open_basis = Column(Float, nullable=False)
    target_basis = Column(Float, nullable=False)
    open_time = Column(DateTime, nullable=False)

    # 平仓信息
    spot_close_price = Column(Float, nullable=True)
    future_close_price = Column(Float, nullable=True)
    close_basis = Column(Float, nullable=True)
    close_time = Column(DateTime, nullable=True)
    spot_close_order_id = Column(String(36), nullable=True)
    future_close_order_id = Column(String(64), nullable=True)  # 扩展长度以支持TqSDK订单ID
    cancel_reason = Column(String(200), nullable=True)

    # 交易成本
    spot_cost = Column(Float, nullable=False)
    future_cost = Column(Float, nullable=False)
    total_cost = Column(Float, nullable=False)

    # 盈亏信息
    spot_pnl = Column(Float, nullable=True)
    future_pnl = Column(Float, nullable=True)
    total_pnl = Column(Float, nullable=True)
    net_profit = Column(Float, nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # 关系
    user = relationship("User", back_populates="orders")

    # 唯一约束
    __table_args__ = (
        {"sqlite_autoincrement": True},
    )
