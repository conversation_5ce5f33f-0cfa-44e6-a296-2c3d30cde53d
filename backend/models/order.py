"""
订单模型
"""
from datetime import datetime
from typing import Optional, List, Dict, Any, Literal
from pydantic import BaseModel, Field, field_validator

class OrderBase(BaseModel):
    """订单基础模型"""
    user_id: Optional[str] = Field(None, description="用户ID")
    direction: Literal[1, -1] = Field(..., description="交易方向: 1(正向:现货买跌-期货买涨), -1(反向:现货买涨-期货买跌)")
    status: str = Field("open", description="订单状态: 'open'(持仓中), 'closed'(已平仓), 'cancelled'(已取消), 'error'(错误)")
    volume: int = Field(1, description="开仓手数", ge=1)
    base_weight: int = Field(1000, description="基础克重(g/手)", ge=1)

    # 开仓信息
    spot_open_price: Optional[float] = Field(None, description="现货开仓价格", ge=0)
    future_open_price: Optional[float] = Field(None, description="期货开仓价格", ge=0)
    open_basis: Optional[float] = Field(None, description="开仓基差")
    target_basis: Optional[float] = Field(None, description="目标基差")

    # 交易成本
    spot_cost: Optional[float] = Field(None, description="现货交易成本", ge=0)
    future_cost: Optional[float] = Field(None, description="期货交易成本", ge=0)
    total_cost: Optional[float] = Field(None, description="总交易成本", ge=0)

    # 添加模型配置，允许额外字段
    model_config = {
        "extra": "ignore"
    }

    @field_validator('status')
    def validate_status(cls, v):
        allowed_statuses = ['open', 'closed', 'cancelled', 'error']
        if v not in allowed_statuses:
            raise ValueError(f'状态必须是以下之一: {", ".join(allowed_statuses)}')
        return v

    @field_validator('total_cost')
    def validate_total_cost(cls, v, info):
        if 'spot_cost' in info.data and 'future_cost' in info.data:
            expected_total = info.data['spot_cost'] + info.data['future_cost']
            if abs(v - expected_total) > 0.01:  # 允许0.01的误差
                raise ValueError(f'总成本({v})必须等于现货成本({info.data["spot_cost"]})加期货成本({info.data["future_cost"]})')
        return v

    @field_validator('open_basis')
    def validate_open_basis(cls, v, info):
        """
        开仓基差验证器 - 调试版本

        记录基差计算信息用于调试，但不阻止交易
        """
        if v is None:
            return v

        # 记录基差信息用于调试，但不进行严格验证
        if 'direction' in info.data and 'spot_open_price' in info.data and 'future_open_price' in info.data:
            spot_price = info.data['spot_open_price']
            future_price = info.data['future_open_price']
            direction = info.data['direction']

            # 如果价格数据不完整，跳过验证
            if spot_price is None or future_price is None:
                return v

            # 根据交易方向计算理论基差
            if direction == 1:
                # 正向套利：现货买入价 - 期货卖出价
                expected_basis = round(spot_price - future_price, 3)
            else:
                # 反向套利：期货买入价 - 现货卖出价
                expected_basis = round(future_price - spot_price, 3)

            difference = abs(v - expected_basis)

            # 记录基差信息但不阻止交易
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f'开仓基差调试: 方向={direction}, 实际基差={v}, 理论基差={expected_basis}, 差异={difference:.6f}, '
                       f'现货价格={spot_price}, 期货价格={future_price}')

            # 如果差异很大，记录警告但不阻止
            if difference > 1.0:
                logger.warning(f'开仓基差差异较大: 方向={direction}, 实际={v}, 理论={expected_basis}, 差异={difference:.6f}')

        # 直接返回基差值，不进行验证
        return v

class OrderCreate(OrderBase):
    """订单创建模型"""
    spot_order_id: Optional[str] = Field(None, description="现货订单ID")
    future_order_id: Optional[str] = Field(None, description="期货订单ID")

    # 兼容前端字段
    spot_price: Optional[float] = Field(None, description="现货价格", ge=0)
    future_price: Optional[float] = Field(None, description="期货价格", ge=0)

    # 添加模型配置，允许额外字段
    model_config = {
        "extra": "ignore"
    }

    def __init__(self, **data):
        # 处理字段映射
        if 'spot_price' in data and 'spot_open_price' not in data:
            data['spot_open_price'] = data['spot_price']

        if 'future_price' in data and 'future_open_price' not in data:
            data['future_open_price'] = data['future_price']

        # 如果没有提供target_basis但有open_basis，使用open_basis加上默认值
        if 'target_basis' not in data and 'open_basis' in data and 'direction' in data:
            direction = data['direction']
            data['target_basis'] = data['open_basis'] + (0.5 if direction == 1 else -0.5)

        # 确保所有必需字段都有值
        for field in ['spot_open_price', 'future_open_price', 'open_basis', 'target_basis', 'spot_cost', 'future_cost', 'total_cost']:
            if field not in data:
                # 如果缺少字段，尝试使用默认值
                if field == 'spot_cost' and 'base_weight' in data and 'volume' in data:
                    data[field] = 0.4 * data['base_weight'] * data['volume'] / 100
                elif field == 'future_cost' and 'base_weight' in data and 'volume' in data:
                    data[field] = 0.01 * data['base_weight'] * data['volume'] / 100
                elif field == 'total_cost' and 'spot_cost' in data and 'future_cost' in data:
                    data[field] = data['spot_cost'] + data['future_cost']
                elif field == 'open_basis' and 'spot_open_price' in data and 'future_open_price' in data and 'direction' in data:
                    direction = data['direction']
                    if direction == 1:
                        data[field] = data['spot_open_price'] - data['future_open_price']
                    else:
                        data[field] = data['future_open_price'] - data['spot_open_price']

        super().__init__(**data)

class OrderUpdate(BaseModel):
    """订单更新模型"""
    status: Optional[str] = Field(None, description="订单状态: 'open'(持仓中), 'closed'(已平仓), 'cancelled'(已取消), 'error'(错误)")

    # 平仓信息
    spot_close_price: Optional[float] = Field(None, description="现货平仓价格", ge=0)
    future_close_price: Optional[float] = Field(None, description="期货平仓价格", ge=0)
    close_basis: Optional[float] = Field(None, description="平仓基差")
    close_time: Optional[datetime] = Field(None, description="平仓时间")
    spot_close_order_id: Optional[str] = Field(None, description="现货平仓订单ID")
    future_close_order_id: Optional[str] = Field(None, description="期货平仓订单ID")

    # 盈亏信息
    spot_pnl: Optional[float] = Field(None, description="现货盈亏")
    future_pnl: Optional[float] = Field(None, description="期货盈亏")
    total_pnl: Optional[float] = Field(None, description="总盈亏")
    net_profit: Optional[float] = Field(None, description="净利润(总盈亏-总成本)")

    @field_validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ['open', 'closed', 'cancelled', 'error']
            if v not in allowed_statuses:
                raise ValueError(f'状态必须是以下之一: {", ".join(allowed_statuses)}')
        return v

    @field_validator('close_basis')
    def validate_close_basis(cls, v, info):
        """
        平仓基差验证器 - 调试版本

        记录基差计算信息用于调试，但不阻止交易
        """
        if v is None:
            return v

        # 记录基差信息用于调试，但不进行严格验证
        if ('spot_close_price' in info.data and info.data['spot_close_price'] is not None and
            'future_close_price' in info.data and info.data['future_close_price'] is not None and
            'direction' in info.data):

            spot_price = info.data['spot_close_price']
            future_price = info.data['future_close_price']
            direction = info.data['direction']

            # 根据交易方向计算理论基差
            if direction == 1:
                # 正向套利：现货卖出价 - 期货买入价
                expected_basis = round(spot_price - future_price, 3)
            else:
                # 反向套利：期货卖出价 - 现货买入价
                expected_basis = round(future_price - spot_price, 3)

            difference = abs(v - expected_basis)

            # 记录基差信息但不阻止交易
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f'平仓基差调试: 方向={direction}, 实际基差={v}, 理论基差={expected_basis}, 差异={difference:.6f}, '
                       f'现货价格={spot_price}, 期货价格={future_price}')

            # 如果差异很大，记录警告但不阻止
            if difference > 1.0:
                logger.warning(f'平仓基差差异较大: 方向={direction}, 实际={v}, 理论={expected_basis}, 差异={difference:.6f}')

        # 直接返回基差值，不进行验证
        return v

class OrderInDB(OrderBase):
    """数据库中的订单模型"""
    id: str = Field(..., description="订单ID")
    spot_order_id: Optional[str] = Field(None, description="现货订单ID")
    future_order_id: Optional[str] = Field(None, description="期货订单ID")

    # 开仓时间
    open_time: datetime = Field(..., description="开仓时间")

    # 平仓信息
    spot_close_price: Optional[float] = Field(None, description="现货平仓价格", ge=0)
    future_close_price: Optional[float] = Field(None, description="期货平仓价格", ge=0)
    close_basis: Optional[float] = Field(None, description="平仓基差")
    close_time: Optional[datetime] = Field(None, description="平仓时间")

    # 盈亏信息
    spot_pnl: Optional[float] = Field(None, description="现货盈亏")
    future_pnl: Optional[float] = Field(None, description="期货盈亏")
    total_pnl: Optional[float] = Field(None, description="总盈亏")
    net_profit: Optional[float] = Field(None, description="净利润(总盈亏-总成本)")

    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = {
        "from_attributes": True
    }

class Order(OrderBase):
    """订单模型（API响应）"""
    id: str = Field(..., description="订单ID")
    spot_order_id: Optional[str] = Field(None, description="现货订单ID")
    future_order_id: Optional[str] = Field(None, description="期货订单ID")

    # 开仓时间
    open_time: datetime = Field(..., description="开仓时间")

    # 平仓信息
    spot_close_price: Optional[float] = Field(None, description="现货平仓价格", ge=0)
    future_close_price: Optional[float] = Field(None, description="期货平仓价格", ge=0)
    close_basis: Optional[float] = Field(None, description="平仓基差")
    close_time: Optional[datetime] = Field(None, description="平仓时间")
    spot_close_order_id: Optional[str] = Field(None, description="现货平仓订单ID")
    future_close_order_id: Optional[str] = Field(None, description="期货平仓订单ID")

    # 当前价格（仅持仓订单）
    current_spot_price: Optional[float] = Field(None, description="当前现货价格", ge=0)
    current_future_price: Optional[float] = Field(None, description="当前期货价格", ge=0)
    current_basis: Optional[float] = Field(None, description="当前基差")

    # 盈亏信息
    spot_pnl: Optional[float] = Field(None, description="现货盈亏")
    future_pnl: Optional[float] = Field(None, description="期货盈亏")
    total_pnl: Optional[float] = Field(None, description="总盈亏")
    net_profit: Optional[float] = Field(None, description="净利润(总盈亏-总成本)")

    model_config = {
        "from_attributes": True,
        "json_schema_extra": {
            "example": {
                "id": "ORD202306260001",
                "user_id": "USR0001",
                "direction": 1,
                "status": "open",
                "volume": 1,
                "base_weight": 1000,
                "spot_open_price": 465.25,
                "future_open_price": 464.50,
                "open_basis": 0.75,
                "target_basis": 1.25,
                "open_time": "2023-06-26T07:30:45.123Z",
                "current_spot_price": 464.80,
                "current_future_price": 465.10,
                "current_basis": -0.30,
                "spot_pnl": 450.00,
                "future_pnl": 600.00,
                "total_pnl": 1050.00,
                "spot_cost": 400.00,
                "future_cost": 10.00,
                "total_cost": 410.00,
                "net_profit": 640.00
            }
        }
    }

class PositionsResponse(BaseModel):
    """持仓列表响应"""
    count: int = Field(..., description="持仓数量")
    total_pnl: float = Field(..., description="总盈亏")
    positions: List[Order] = Field(..., description="持仓列表")

    model_config = {
        "json_schema_extra": {
            "example": {
                "count": 2,
                "total_pnl": 1050.00,
                "positions": [
                    {
                        "id": "ORD202306260001",
                        "user_id": "USR0001",
                        "direction": 1,
                        "status": "open",
                        "volume": 1,
                        "base_weight": 1000,
                        "spot_open_price": 465.25,
                        "future_open_price": 464.50,
                        "open_basis": 0.75,
                        "target_basis": 1.25,
                        "open_time": "2023-06-26T07:30:45.123Z",
                        "current_spot_price": 464.80,
                        "current_future_price": 465.10,
                        "current_basis": -0.30,
                        "spot_pnl": 450.00,
                        "future_pnl": 600.00,
                        "total_pnl": 1050.00,
                        "spot_cost": 400.00,
                        "future_cost": 10.00,
                        "total_cost": 410.00,
                        "net_profit": 640.00
                    }
                ]
            }
        }
    }

class HistoryOrdersResponse(BaseModel):
    """历史订单列表响应"""
    total_count: int = Field(..., description="总订单数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    orders: List[Order] = Field(..., description="订单列表")

    model_config = {
        "json_schema_extra": {
            "example": {
                "total_count": 258,
                "page": 1,
                "page_size": 10,
                "orders": [
                    {
                        "id": "ORD202306250015",
                        "user_id": "USR0001",
                        "direction": 1,
                        "status": "closed",
                        "volume": 1,
                        "base_weight": 1000,
                        "spot_open_price": 464.25,
                        "future_open_price": 463.65,
                        "open_basis": 0.60,
                        "target_basis": 1.10,
                        "open_time": "2023-06-25T16:30:45.123Z",
                        "spot_close_price": 463.85,
                        "future_close_price": 464.35,
                        "close_basis": -0.50,
                        "close_time": "2023-06-25T17:45:22.543Z",
                        "spot_pnl": 400.00,
                        "future_pnl": 700.00,
                        "total_pnl": 1100.00,
                        "spot_cost": 400.00,
                        "future_cost": 10.00,
                        "total_cost": 410.00,
                        "net_profit": 690.00
                    }
                ]
            }
        }
    }

class TradeResponse(BaseModel):
    """交易操作响应"""
    success: bool = Field(..., description="是否成功")
    order_id: str = Field(..., description="订单ID")
    close_time: Optional[str] = Field(None, description="平仓时间，仅平仓操作返回")
    message: Optional[str] = Field(None, description="响应消息")
    details: Optional[Dict[str, Any]] = Field(None, description="交易详细信息")
    step: Optional[str] = Field(None, description="当前执行步骤")
    error_step: Optional[str] = Field(None, description="出错的步骤")

    model_config = {
        "json_schema_extra": {
            "example": {
                "success": True,
                "order_id": "ORD202306260001",
                "close_time": "2023-06-26T08:45:22.543Z",
                "message": "操作成功",
                "details": {
                    "spot_order_id": "SPOT123456",
                    "future_order_id": "FUTURE123456",
                    "spot_price": 465.25,
                    "future_price": 464.50,
                    "basis": 0.75
                },
                "step": "completed",
                "error_step": None
            }
        }
    }

class OpenPositionRequest(BaseModel):
    """开仓请求"""
    direction: Literal[1, -1] = Field(..., description="交易方向: 1(正向), -1(反向)")
    spot_price: float = Field(..., description="现货价格", ge=0)
    future_price: float = Field(..., description="期货价格", ge=0)
    volume: int = Field(1, description="手数", ge=1)
    base_weight: int = Field(1000, description="基础克重(g/手)", ge=1)
    target_basis: Optional[float] = Field(None, description="目标基差")
    user_id: str = Field(..., description="用户ID")  # 必须提供用户ID
    open_basis: float = Field(..., description="开仓基差")
    spot_cost: float = Field(..., description="现货交易成本", ge=0)
    future_cost: float = Field(..., description="期货交易成本", ge=0)
    total_cost: float = Field(..., description="总交易成本", ge=0)

    # 添加模型配置，允许额外字段
    model_config = {
        "extra": "ignore",
        "json_schema_extra": {
            "example": {
                "direction": 1,
                "spot_price": 465.25,
                "future_price": 464.50,
                "volume": 1,
                "base_weight": 1000,
                "target_basis": 1.25,
                "open_basis": 0.75,
                "spot_cost": 4.0,
                "future_cost": 0.1,
                "total_cost": 4.1
            }
        }
    }
