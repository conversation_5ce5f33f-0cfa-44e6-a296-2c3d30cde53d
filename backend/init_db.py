"""
初始化数据库
"""
import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def init_db():
    """初始化数据库"""
    # 从环境变量获取配置
    pg_host = os.getenv("POSTGRES_HOST", "localhost")
    pg_port = int(os.getenv("POSTGRES_PORT", "5432"))
    pg_user = os.getenv("POSTGRES_USER", "gold_user")
    pg_password = os.getenv("POSTGRES_PASSWORD", "gold_password")
    pg_dbname = os.getenv("POSTGRES_DB", "gold_arbitrage")

    # 连接到PostgreSQL服务器
    conn = psycopg2.connect(
        host=pg_host,
        port=pg_port,
        user=pg_user,
        password=pg_password
    )
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)

    # 创建数据库（如果不存在）
    cur = conn.cursor()
    try:
        cur.execute(f"CREATE DATABASE {pg_dbname}")
        print(f"数据库 {pg_dbname} 已创建")
    except psycopg2.errors.DuplicateDatabase:
        print(f"数据库 {pg_dbname} 已存在")
    cur.close()
    conn.close()

    # 连接到新创建的数据库
    conn = psycopg2.connect(
        host=pg_host,
        port=pg_port,
        user=pg_user,
        password=pg_password,
        dbname=pg_dbname
    )
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cur = conn.cursor()

    # 创建system_logs表
    cur.execute("""
    CREATE TABLE IF NOT EXISTS system_logs (
        id VARCHAR(36) PRIMARY KEY,
        level VARCHAR(20) NOT NULL,
        category VARCHAR(50) NOT NULL,
        message VARCHAR(500) NOT NULL,
        details TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
    """)
    print("system_logs表已创建")

    # 创建users表
    cur.execute("""
    CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        phone VARCHAR(20),
        role VARCHAR(20) NOT NULL DEFAULT 'user',
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        last_login TIMESTAMP,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
    """)
    print("users表已创建")

    # 创建accounts表
    cur.execute("""
    CREATE TABLE IF NOT EXISTS accounts (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        account_type VARCHAR(10) NOT NULL,
        platform_name VARCHAR(100) NOT NULL,
        platform_url VARCHAR(200),
        contract_code VARCHAR(20),
        username VARCHAR(50) NOT NULL,
        password_encrypted VARCHAR(200) NOT NULL,
        cost_per_gram FLOAT NOT NULL,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        connection_status VARCHAR(20) NOT NULL DEFAULT 'disconnected',
        last_connection_time TIMESTAMP,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
    """)
    print("accounts表已创建")

    # 创建settings表
    cur.execute("""
    CREATE TABLE IF NOT EXISTS settings (
        id SERIAL PRIMARY KEY,
        user_id VARCHAR(36) UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        target_profit_diff FLOAT NOT NULL DEFAULT 0.5,
        position_size INTEGER NOT NULL DEFAULT 1,
        max_positions INTEGER NOT NULL DEFAULT 5,
        max_daily_cycles INTEGER NOT NULL DEFAULT 10,
        spot_refresh_rate INTEGER NOT NULL DEFAULT 3,
        base_weight INTEGER NOT NULL DEFAULT 1000,
        forward_basis_ranges JSONB NOT NULL DEFAULT '[]'::JSONB,
        reverse_basis_ranges JSONB NOT NULL DEFAULT '[]'::JSONB,
        auto_trade_enabled BOOLEAN NOT NULL DEFAULT FALSE,
        trading_times JSONB NOT NULL DEFAULT '[]'::JSONB,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
    """)
    print("settings表已创建")

    # 创建orders表
    cur.execute("""
    CREATE TABLE IF NOT EXISTS orders (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        direction INTEGER NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'open',
        spot_order_id VARCHAR(36),
        future_order_id VARCHAR(64),  -- 扩展长度以支持TqSDK订单ID
        volume INTEGER NOT NULL DEFAULT 1,
        base_weight INTEGER NOT NULL DEFAULT 1000,

        -- 开仓信息
        spot_open_price FLOAT NOT NULL,
        future_open_price FLOAT NOT NULL,
        open_basis FLOAT NOT NULL,
        target_basis FLOAT NOT NULL,
        open_time TIMESTAMP NOT NULL,

        -- 平仓信息
        spot_close_price FLOAT,
        future_close_price FLOAT,
        close_basis FLOAT,
        close_time TIMESTAMP,

        -- 交易成本
        spot_cost FLOAT NOT NULL,
        future_cost FLOAT NOT NULL,
        total_cost FLOAT NOT NULL,

        -- 盈亏信息
        spot_pnl FLOAT,
        future_pnl FLOAT,
        total_pnl FLOAT,
        net_profit FLOAT,

        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
    """)
    print("orders表已创建")

    # 创建notifications表
    cur.execute("""
    CREATE TABLE IF NOT EXISTS notifications (
        id SERIAL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        type VARCHAR(20) NOT NULL,
        title VARCHAR(100) NOT NULL,
        content VARCHAR(500) NOT NULL,
        is_read BOOLEAN NOT NULL DEFAULT FALSE,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        read_at TIMESTAMP
    )
    """)
    print("notifications表已创建")

    # 创建trade_configs表
    cur.execute("""
    CREATE TABLE IF NOT EXISTS trade_configs (
        id SERIAL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(100) NOT NULL,
        target_profit_diff FLOAT NOT NULL DEFAULT 0.5,
        position_size INTEGER NOT NULL DEFAULT 1,
        base_weight INTEGER NOT NULL DEFAULT 1000,
        is_default BOOLEAN NOT NULL DEFAULT FALSE,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
    """)
    print("trade_configs表已创建")

    # 创建索引
    cur.execute("CREATE INDEX IF NOT EXISTS ix_accounts_user_id ON accounts(user_id)")
    cur.execute("CREATE INDEX IF NOT EXISTS ix_orders_user_id ON orders(user_id)")
    cur.execute("CREATE INDEX IF NOT EXISTS ix_orders_status ON orders(status)")
    cur.execute("CREATE INDEX IF NOT EXISTS ix_system_logs_level ON system_logs(level)")
    cur.execute("CREATE INDEX IF NOT EXISTS ix_system_logs_category ON system_logs(category)")
    cur.execute("CREATE INDEX IF NOT EXISTS ix_notifications_user_id ON notifications(user_id)")
    cur.execute("CREATE INDEX IF NOT EXISTS ix_notifications_is_read ON notifications(is_read)")
    cur.execute("CREATE INDEX IF NOT EXISTS ix_trade_configs_user_id ON trade_configs(user_id)")
    cur.execute("CREATE INDEX IF NOT EXISTS ix_trade_configs_is_default ON trade_configs(is_default)")
    print("索引已创建")

    # 创建触发器函数，用于自动更新 updated_at 字段
    cur.execute("""
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql
    """)

    # 为每个表创建触发器
    cur.execute("""
    CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column()
    """)

    cur.execute("""
    CREATE TRIGGER update_accounts_updated_at
    BEFORE UPDATE ON accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column()
    """)

    cur.execute("""
    CREATE TRIGGER update_settings_updated_at
    BEFORE UPDATE ON settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column()
    """)

    cur.execute("""
    CREATE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column()
    """)

    cur.execute("""
    CREATE TRIGGER update_trade_configs_updated_at
    BEFORE UPDATE ON trade_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column()
    """)
    print("触发器已创建")

    # 创建测试用户
    try:
        cur.execute("""
        INSERT INTO users (id, username, password_hash, email, role, is_active)
        VALUES ('USR0001', 'admin', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', '<EMAIL>', 'admin', TRUE)
        """)
        print("测试管理员用户已创建")
    except psycopg2.errors.UniqueViolation:
        print("测试管理员用户已存在")
        conn.rollback()

    try:
        cur.execute("""
        INSERT INTO users (id, username, password_hash, email, role, is_active)
        VALUES ('USR0002', 'trader001', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', '<EMAIL>', 'user', TRUE)
        """)
        print("测试交易员用户已创建")
    except psycopg2.errors.UniqueViolation:
        print("测试交易员用户已存在")
        conn.rollback()

    # 关闭连接
    cur.close()
    conn.close()

    print("数据库初始化完成")

if __name__ == "__main__":
    init_db()
