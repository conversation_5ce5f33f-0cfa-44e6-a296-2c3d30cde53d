#!/usr/bin/env python3
"""
测试平仓修复的脚本

验证平仓逻辑是否正确调用了现货平仓方法而不是开仓方法
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.trade_service import TradeService
from utils.logger import setup_logger

# 设置日志
os.makedirs("/gold/logs", exist_ok=True)
logger = setup_logger('test_close_fix', "/gold/logs/test_close_fix.log")

class ClosePositionFixTester:
    """平仓修复测试器"""

    def __init__(self):
        self.trade_service = TradeService()
        self.test_results = {}

    async def test_spot_close_method_exists(self):
        """测试现货平仓方法是否存在"""
        logger.info("测试现货平仓方法是否存在...")

        try:
            # 检查新增的平仓方法是否存在
            methods_to_check = [
                '_execute_spot_close_position',
                '_execute_spot_close_position_with_retry'
            ]

            for method_name in methods_to_check:
                if hasattr(self.trade_service, method_name):
                    logger.info(f"✅ {method_name}方法存在")
                    self.test_results[f"method_{method_name}"] = True
                else:
                    logger.error(f"❌ {method_name}方法不存在")
                    self.test_results[f"method_{method_name}"] = False

        except Exception as e:
            logger.error(f"测试现货平仓方法时发生异常: {e}")
            self.test_results["spot_close_methods"] = False

    async def test_method_signatures(self):
        """测试方法签名是否正确"""
        logger.info("测试方法签名是否正确...")

        try:
            import inspect

            # 检查_execute_spot_close_position方法签名
            if hasattr(self.trade_service, '_execute_spot_close_position'):
                method = getattr(self.trade_service, '_execute_spot_close_position')
                sig = inspect.signature(method)
                params = list(sig.parameters.keys())

                expected_params = ['account_id', 'order_id']
                if params == expected_params:
                    logger.info(f"✅ _execute_spot_close_position方法签名正确: {params}")
                    self.test_results["close_method_signature"] = True
                else:
                    logger.error(f"❌ _execute_spot_close_position方法签名错误: 期望{expected_params}, 实际{params}")
                    self.test_results["close_method_signature"] = False

            # 检查_execute_spot_close_position_with_retry方法签名
            if hasattr(self.trade_service, '_execute_spot_close_position_with_retry'):
                method = getattr(self.trade_service, '_execute_spot_close_position_with_retry')
                sig = inspect.signature(method)
                params = list(sig.parameters.keys())

                expected_params = ['account_id', 'order_id']
                if params == expected_params:
                    logger.info(f"✅ _execute_spot_close_position_with_retry方法签名正确: {params}")
                    self.test_results["close_retry_method_signature"] = True
                else:
                    logger.error(f"❌ _execute_spot_close_position_with_retry方法签名错误: 期望{expected_params}, 实际{params}")
                    self.test_results["close_retry_method_signature"] = False

        except Exception as e:
            logger.error(f"测试方法签名时发生异常: {e}")
            self.test_results["method_signatures"] = False

    async def test_close_position_logic_flow(self):
        """测试平仓逻辑流程"""
        logger.info("测试平仓逻辑流程...")

        try:
            # 检查close_position方法中是否调用了正确的现货平仓方法
            import inspect

            if hasattr(self.trade_service, 'close_position'):
                method = getattr(self.trade_service, 'close_position')
                source = inspect.getsource(method)

                # 检查是否调用了新的现货平仓方法
                if '_execute_spot_close_position_with_retry' in source:
                    logger.info("✅ close_position方法调用了正确的现货平仓方法")
                    self.test_results["close_logic_flow"] = True
                else:
                    logger.error("❌ close_position方法没有调用正确的现货平仓方法")
                    self.test_results["close_logic_flow"] = False

                # 检查是否不再调用错误的开仓方法
                if '_execute_spot_order_with_retry' not in source.split('_execute_spot_close_position_with_retry')[0]:
                    logger.info("✅ close_position方法没有调用错误的开仓方法")
                    self.test_results["no_wrong_method"] = True
                else:
                    logger.warning("⚠️ close_position方法可能仍在调用开仓方法")
                    self.test_results["no_wrong_method"] = False

        except Exception as e:
            logger.error(f"测试平仓逻辑流程时发生异常: {e}")
            self.test_results["close_logic_flow"] = False

    async def test_spot_service_integration(self):
        """测试现货服务集成"""
        logger.info("测试现货服务集成...")

        try:
            # 检查新的平仓方法是否正确调用spot_service.close_position
            import inspect

            if hasattr(self.trade_service, '_execute_spot_close_position'):
                method = getattr(self.trade_service, '_execute_spot_close_position')
                source = inspect.getsource(method)

                # 检查是否调用了spot_service.close_position而不是place_order
                if 'spot_service.close_position' in source:
                    logger.info("✅ _execute_spot_close_position方法调用了spot_service.close_position")
                    self.test_results["spot_service_close"] = True
                else:
                    logger.error("❌ _execute_spot_close_position方法没有调用spot_service.close_position")
                    self.test_results["spot_service_close"] = False

                # 检查是否不再调用place_order
                if 'spot_service.place_order' not in source:
                    logger.info("✅ _execute_spot_close_position方法没有调用spot_service.place_order")
                    self.test_results["no_place_order"] = True
                else:
                    logger.error("❌ _execute_spot_close_position方法仍在调用spot_service.place_order")
                    self.test_results["no_place_order"] = False

        except Exception as e:
            logger.error(f"测试现货服务集成时发生异常: {e}")
            self.test_results["spot_service_integration"] = False

    def generate_report(self):
        """生成测试报告"""
        logger.info("=== 平仓修复测试报告 ===")

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)

        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试数: {passed_tests}")
        logger.info(f"失败测试数: {total_tests - passed_tests}")
        logger.info(f"通过率: {(passed_tests / total_tests * 100):.1f}%")

        logger.info("\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  {test_name}: {status}")

        overall_success = passed_tests == total_tests
        if overall_success:
            logger.info("\n🎉 所有测试通过！平仓修复成功！")
        else:
            logger.warning(f"\n⚠️ 有 {total_tests - passed_tests} 个测试失败，需要进一步修复")

        return overall_success

async def main():
    """主函数"""
    logger.info("开始测试平仓修复...")

    tester = ClosePositionFixTester()

    try:
        # 执行各项测试
        await tester.test_spot_close_method_exists()
        await tester.test_method_signatures()
        await tester.test_close_position_logic_flow()
        await tester.test_spot_service_integration()

        # 生成测试报告
        overall_success = tester.generate_report()

        return overall_success

    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
