#!/usr/bin/env python3
"""
测试多订单查找功能

验证在多订单情况下能否准确定位特定订单
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.spot_service import SpotService
from services.multi_order_finder import MultiOrderFinder
from utils.logger import setup_logger

# 设置日志
os.makedirs("/gold/logs", exist_ok=True)
logger = setup_logger('test_multi_order_finder', "/gold/logs/test_multi_order_finder.log")


class MultiOrderTester:
    """多订单测试器"""
    
    def __init__(self):
        self.spot_service = None
        self.order_finder = None
        self.test_results = {}
    
    async def setup(self):
        """设置测试环境"""
        try:
            logger.info("设置测试环境...")
            
            # 初始化现货服务
            self.spot_service = SpotService()
            success = await self.spot_service.initialize()
            
            if not success:
                logger.error("现货服务初始化失败")
                return False
            
            logger.info("✅ 现货服务初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"设置测试环境失败: {e}")
            return False
    
    async def navigate_to_orders(self):
        """导航到订单页面"""
        logger.info("=" * 60)
        logger.info("导航到订单页面")
        logger.info("=" * 60)
        
        try:
            orders_url = "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"
            
            await self.spot_service.page.goto(orders_url, wait_until="domcontentloaded", timeout=45000)
            logger.info(f"页面加载完成: {self.spot_service.page.url}")
            await asyncio.sleep(3)
            
            current_url = self.spot_service.page.url
            if "myorder" in current_url:
                logger.info("✅ 导航成功")
                return True
            else:
                logger.error(f"❌ 导航失败: {current_url}")
                return False
                
        except Exception as e:
            logger.error(f"导航失败: {e}")
            return False
    
    async def test_find_all_orders(self):
        """测试查找所有订单"""
        logger.info("=" * 60)
        logger.info("测试查找所有订单")
        logger.info("=" * 60)
        
        try:
            # 创建多订单查找器（需要适配Playwright）
            # 这里我们直接使用Playwright的方法
            
            # 查找所有订单容器
            order_containers = await self.spot_service.page.query_selector_all(".puwidth")
            logger.info(f"找到 {len(order_containers)} 个订单容器")
            
            orders = []
            for i, container in enumerate(order_containers):
                try:
                    order_info = await self._extract_order_info_playwright(container, i + 1)
                    if order_info:
                        orders.append(order_info)
                        logger.info(f"✅ 第 {i + 1} 个订单: {order_info.get('order_id', 'Unknown')}")
                    else:
                        logger.warning(f"⚠️ 第 {i + 1} 个订单信息提取失败")
                except Exception as e:
                    logger.error(f"❌ 处理第 {i + 1} 个订单时出错: {e}")
                    continue
            
            self.test_results["total_orders"] = len(orders)
            self.test_results["extracted_orders"] = len([o for o in orders if o.get('order_id')])
            self.test_results["order_list"] = orders
            
            logger.info(f"📊 总订单数: {len(order_containers)}")
            logger.info(f"📊 成功提取: {len(orders)}")
            
            # 显示订单详情
            for order in orders:
                logger.info(f"📋 订单详情:")
                logger.info(f"   订单号: {order.get('order_id', 'N/A')}")
                logger.info(f"   开仓价格: {order.get('open_price', 'N/A')}")
                logger.info(f"   当前价格: {order.get('current_price', 'N/A')}")
                logger.info(f"   可用按钮: {order.get('available_buttons', [])}")
            
            return len(orders) > 0
            
        except Exception as e:
            logger.error(f"查找所有订单失败: {e}")
            self.test_results["total_orders"] = 0
            self.test_results["extracted_orders"] = 0
            return False
    
    async def test_find_specific_order(self):
        """测试查找特定订单"""
        logger.info("=" * 60)
        logger.info("测试查找特定订单")
        logger.info("=" * 60)
        
        # 从之前的结果中获取订单ID列表
        orders = self.test_results.get("order_list", [])
        if not orders:
            logger.error("没有可用的订单进行测试")
            return False
        
        # 测试查找每个订单
        success_count = 0
        for order in orders:
            order_id = order.get("order_id")
            if not order_id:
                continue
                
            try:
                logger.info(f"查找订单: {order_id}")
                
                # 使用订单ID查找特定订单
                found_order = await self._find_order_by_id_playwright(order_id)
                
                if found_order:
                    logger.info(f"✅ 成功找到订单: {order_id}")
                    logger.info(f"   开仓价格: {found_order.get('open_price', 'N/A')}")
                    logger.info(f"   当前价格: {found_order.get('current_price', 'N/A')}")
                    success_count += 1
                else:
                    logger.error(f"❌ 未找到订单: {order_id}")
                    
            except Exception as e:
                logger.error(f"❌ 查找订单 {order_id} 时出错: {e}")
        
        success_rate = (success_count / len(orders)) * 100 if orders else 0
        logger.info(f"📊 特定订单查找成功率: {success_rate:.1f}% ({success_count}/{len(orders)})")
        
        self.test_results["specific_order_success_rate"] = success_rate
        return success_rate >= 100  # 要求100%成功率
    
    async def test_order_uniqueness(self):
        """测试订单唯一性识别"""
        logger.info("=" * 60)
        logger.info("测试订单唯一性识别")
        logger.info("=" * 60)
        
        orders = self.test_results.get("order_list", [])
        if len(orders) < 2:
            logger.warning("订单数量少于2个，跳过唯一性测试")
            return True
        
        # 检查订单ID是否唯一
        order_ids = [order.get("order_id") for order in orders if order.get("order_id")]
        unique_ids = set(order_ids)
        
        if len(order_ids) == len(unique_ids):
            logger.info(f"✅ 订单ID唯一性验证通过: {len(order_ids)} 个唯一订单")
            for order_id in order_ids:
                logger.info(f"   📋 {order_id}")
            self.test_results["order_uniqueness"] = True
            return True
        else:
            logger.error(f"❌ 发现重复订单ID: 总数={len(order_ids)}, 唯一数={len(unique_ids)}")
            self.test_results["order_uniqueness"] = False
            return False
    
    async def test_button_detection(self):
        """测试按钮检测"""
        logger.info("=" * 60)
        logger.info("测试按钮检测")
        logger.info("=" * 60)
        
        orders = self.test_results.get("order_list", [])
        if not orders:
            logger.error("没有可用的订单进行按钮测试")
            return False
        
        button_stats = {
            "feed_button": 0,
            "default_settlement": 0,
            "no_buttons": 0
        }
        
        for order in orders:
            order_id = order.get("order_id", "Unknown")
            available_buttons = order.get("available_buttons", [])
            
            logger.info(f"📋 订单 {order_id} 的按钮:")
            
            if "feed_button" in available_buttons:
                logger.info("   ✅ 结料按钮可用")
                button_stats["feed_button"] += 1
            
            if "default_settlement" in available_buttons:
                logger.info("   ✅ 违约结算按钮可用")
                button_stats["default_settlement"] += 1
            
            if not available_buttons:
                logger.warning("   ⚠️ 未检测到可用按钮")
                button_stats["no_buttons"] += 1
        
        logger.info(f"📊 按钮统计:")
        logger.info(f"   结料按钮: {button_stats['feed_button']} 个订单")
        logger.info(f"   违约结算按钮: {button_stats['default_settlement']} 个订单")
        logger.info(f"   无按钮: {button_stats['no_buttons']} 个订单")
        
        self.test_results["button_stats"] = button_stats
        
        # 至少要有一些按钮被检测到
        total_buttons = button_stats["feed_button"] + button_stats["default_settlement"]
        return total_buttons > 0
    
    async def _extract_order_info_playwright(self, container, order_index: int):
        """使用Playwright提取订单信息"""
        try:
            order_info = {"order_index": order_index}
            
            # 提取订单号
            try:
                order_sn_element = await container.query_selector(".order-sn")
                if order_sn_element:
                    order_text = await order_sn_element.text_content()
                    import re
                    match = re.search(r'订单号[：:]\s*([A-Z0-9]+)', order_text)
                    if match:
                        order_info["order_id"] = match.group(1)
            except Exception as e:
                logger.debug(f"提取订单号失败: {e}")
            
            # 提取开仓价格
            try:
                open_price_element = await container.query_selector(".per-price")
                if open_price_element:
                    order_info["open_price"] = await open_price_element.text_content()
            except Exception:
                pass
            
            # 提取当前价格
            try:
                current_price_element = await container.query_selector(".new-price")
                if current_price_element:
                    order_info["current_price"] = await current_price_element.text_content()
            except Exception:
                pass
            
            # 检测可用按钮
            available_buttons = []
            
            # 检查结料按钮
            try:
                feed_button = await container.query_selector(".feedingback")
                if feed_button and await feed_button.is_visible():
                    available_buttons.append("feed_button")
            except Exception:
                pass
            
            # 检查违约结算按钮
            try:
                default_button = await container.query_selector(".back1")
                if default_button and await default_button.is_visible():
                    available_buttons.append("default_settlement")
            except Exception:
                pass
            
            order_info["available_buttons"] = available_buttons
            
            return order_info
            
        except Exception as e:
            logger.warning(f"提取第 {order_index} 个订单信息失败: {e}")
            return None
    
    async def _find_order_by_id_playwright(self, target_order_id: str):
        """使用Playwright查找特定订单"""
        try:
            # 查找所有订单容器
            order_containers = await self.spot_service.page.query_selector_all(".puwidth")
            
            for container in order_containers:
                try:
                    # 查找订单号元素
                    order_sn_element = await container.query_selector(".order-sn")
                    if order_sn_element:
                        order_text = await order_sn_element.text_content()
                        import re
                        match = re.search(r'订单号[：:]\s*([A-Z0-9]+)', order_text)
                        if match and match.group(1) == target_order_id:
                            # 找到匹配的订单，提取信息
                            return await self._extract_order_info_playwright(container, 0)
                except Exception:
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"查找订单 {target_order_id} 失败: {e}")
            return None
    
    def generate_report(self):
        """生成测试报告"""
        logger.info("=" * 80)
        logger.info("多订单查找功能测试报告")
        logger.info("=" * 80)
        
        total_orders = self.test_results.get("total_orders", 0)
        extracted_orders = self.test_results.get("extracted_orders", 0)
        success_rate = self.test_results.get("specific_order_success_rate", 0)
        uniqueness = self.test_results.get("order_uniqueness", False)
        button_stats = self.test_results.get("button_stats", {})
        
        logger.info(f"📊 测试结果摘要:")
        logger.info(f"   总订单数: {total_orders}")
        logger.info(f"   成功提取: {extracted_orders}")
        logger.info(f"   提取成功率: {(extracted_orders/total_orders*100) if total_orders > 0 else 0:.1f}%")
        logger.info(f"   特定查找成功率: {success_rate:.1f}%")
        logger.info(f"   订单唯一性: {'✅ 通过' if uniqueness else '❌ 失败'}")
        
        if button_stats:
            logger.info(f"   按钮检测:")
            logger.info(f"     结料按钮: {button_stats.get('feed_button', 0)} 个")
            logger.info(f"     违约结算: {button_stats.get('default_settlement', 0)} 个")
        
        # 评估结果
        if total_orders >= 2 and extracted_orders == total_orders and success_rate >= 100 and uniqueness:
            logger.info("\n🎉 多订单查找功能测试成功！")
            logger.info("✅ 能够准确识别和定位多个订单")
            logger.info("✅ 订单信息提取完整")
            logger.info("✅ 特定订单查找准确")
            logger.info("✅ 订单唯一性验证通过")
            return True
        else:
            logger.warning("\n⚠️ 多订单查找功能需要改进")
            if total_orders < 2:
                logger.warning("❌ 测试订单数量不足")
            if extracted_orders != total_orders:
                logger.warning("❌ 订单信息提取不完整")
            if success_rate < 100:
                logger.warning("❌ 特定订单查找准确率不足")
            if not uniqueness:
                logger.warning("❌ 订单唯一性验证失败")
            return False


async def main():
    """主函数"""
    logger.info("开始测试多订单查找功能...")
    
    tester = MultiOrderTester()
    
    try:
        # 设置测试环境
        setup_success = await tester.setup()
        if not setup_success:
            logger.error("测试环境设置失败")
            return False
        
        # 导航到订单页面
        nav_success = await tester.navigate_to_orders()
        if not nav_success:
            logger.error("导航到订单页面失败")
            return False
        
        # 测试查找所有订单
        find_all_success = await tester.test_find_all_orders()
        if not find_all_success:
            logger.error("查找所有订单测试失败")
            return False
        
        # 测试查找特定订单
        await tester.test_find_specific_order()
        
        # 测试订单唯一性
        await tester.test_order_uniqueness()
        
        # 测试按钮检测
        await tester.test_button_detection()
        
        # 生成报告
        success = tester.generate_report()
        
        return success
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
