#!/usr/bin/env python3
"""
测试优化后的选择器

验证基于HTML结构分析的稳定选择器策略
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.spot_service import SpotService
from services.spot_selectors_optimized import optimized_selectors, get_robust_selector_strategy
from services.spot_order_finder import SpotOrderFinder
from utils.logger import setup_logger

# 设置日志
os.makedirs("/gold/logs", exist_ok=True)
logger = setup_logger('test_optimized_selectors', "/gold/logs/test_optimized_selectors.log")


class OptimizedSelectorTester:
    """优化选择器测试器"""
    
    def __init__(self):
        self.spot_service = None
        self.order_finder = None
        self.test_results = {}
    
    async def setup(self):
        """设置测试环境"""
        try:
            logger.info("设置测试环境...")
            
            # 初始化现货服务
            self.spot_service = SpotService()
            success = await self.spot_service.initialize()
            
            if not success:
                logger.error("现货服务初始化失败")
                return False
            
            logger.info("✅ 现货服务初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"设置测试环境失败: {e}")
            return False
    
    async def test_navigation_to_orders(self):
        """测试导航到订单页面"""
        logger.info("=" * 60)
        logger.info("测试导航到订单页面")
        logger.info("=" * 60)
        
        try:
            orders_url = "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"
            
            await self.spot_service.page.goto(orders_url, wait_until="domcontentloaded", timeout=45000)
            logger.info(f"页面加载完成: {self.spot_service.page.url}")
            await asyncio.sleep(3)
            
            current_url = self.spot_service.page.url
            if "myorder" in current_url:
                logger.info("✅ 导航成功")
                self.test_results["navigation"] = True
                return True
            else:
                logger.error(f"❌ 导航失败: {current_url}")
                self.test_results["navigation"] = False
                return False
                
        except Exception as e:
            logger.error(f"导航测试失败: {e}")
            self.test_results["navigation"] = False
            return False
    
    async def test_optimized_selectors(self):
        """测试优化后的选择器"""
        logger.info("=" * 60)
        logger.info("测试优化后的选择器")
        logger.info("=" * 60)
        
        # 测试主要选择器
        main_selectors = {
            "order_container": ".puwidth",
            "order_detail": ".deposit-bottom", 
            "order_sn": ".order-sn",
            "open_price": ".per-price",
            "current_price": ".new-price",
            "price_red": ".color1",
            "price_green": ".color2",
            "feed_button": ".feedingback",
            "default_settlement": ".back1",
        }
        
        for name, selector in main_selectors.items():
            await self._test_single_optimized_selector(name, selector)
    
    async def _test_single_optimized_selector(self, name: str, selector: str):
        """测试单个优化选择器"""
        try:
            elements = await self.spot_service.page.query_selector_all(selector)
            visible_count = 0
            
            for element in elements:
                if await element.is_visible():
                    visible_count += 1
            
            if visible_count > 0:
                logger.info(f"✅ {name}: 找到 {len(elements)} 个元素，{visible_count} 个可见")
                logger.info(f"   选择器: {selector}")
                self.test_results[f"optimized_{name}"] = True
                
                # 获取第一个元素的文本内容
                if len(elements) > 0:
                    try:
                        text_content = await elements[0].text_content()
                        if text_content and text_content.strip():
                            logger.info(f"   内容: {text_content.strip()[:50]}...")
                    except:
                        pass
            else:
                logger.warning(f"⚠️ {name}: 找到 {len(elements)} 个元素，但都不可见")
                logger.info(f"   选择器: {selector}")
                self.test_results[f"optimized_{name}"] = False
                
        except Exception as e:
            logger.error(f"❌ {name}: 测试失败 - {e}")
            self.test_results[f"optimized_{name}"] = False
    
    async def test_fallback_strategies(self):
        """测试备用选择器策略"""
        logger.info("=" * 60)
        logger.info("测试备用选择器策略")
        logger.info("=" * 60)
        
        # 测试关键元素的备用策略
        key_elements = ["order_container", "order_sn", "open_price", "feed_button"]
        
        for element_type in key_elements:
            await self._test_fallback_strategy(element_type)
    
    async def _test_fallback_strategy(self, element_type: str):
        """测试单个元素的备用策略"""
        logger.info(f"测试 {element_type} 的备用策略:")
        
        fallbacks = optimized_selectors.get_all_fallbacks(element_type)
        success_count = 0
        
        for i, selector in enumerate(fallbacks):
            try:
                if selector.startswith("//"):
                    # XPath选择器 - 需要特殊处理
                    logger.info(f"  备用策略 {i+1}: {selector} (XPath)")
                    # 这里可以添加XPath测试逻辑
                    continue
                else:
                    elements = await self.spot_service.page.query_selector_all(selector)
                    visible_count = sum(1 for elem in elements if await elem.is_visible())
                    
                    if visible_count > 0:
                        logger.info(f"  ✅ 备用策略 {i+1}: {selector} - 找到 {visible_count} 个可见元素")
                        success_count += 1
                    else:
                        logger.info(f"  ❌ 备用策略 {i+1}: {selector} - 无可见元素")
                        
            except Exception as e:
                logger.info(f"  ❌ 备用策略 {i+1}: {selector} - 错误: {e}")
        
        success_rate = (success_count / len(fallbacks)) * 100 if fallbacks else 0
        logger.info(f"  {element_type} 备用策略成功率: {success_rate:.1f}% ({success_count}/{len(fallbacks)})")
        
        self.test_results[f"fallback_{element_type}"] = success_rate >= 50
    
    async def test_order_finder(self):
        """测试订单查找器"""
        logger.info("=" * 60)
        logger.info("测试订单查找器")
        logger.info("=" * 60)
        
        try:
            # 注意：这里需要实际的WebDriver，但我们使用Playwright
            # 所以这个测试主要是验证逻辑结构
            logger.info("订单查找器逻辑结构验证:")
            
            # 验证选择器配置
            selectors = optimized_selectors
            
            # 检查主要选择器是否存在
            required_selectors = ["order_container", "order_sn", "open_price", "feed_button"]
            missing_selectors = []
            
            for selector_name in required_selectors:
                selector = selectors.get_selector(selector_name)
                if not selector:
                    missing_selectors.append(selector_name)
                else:
                    logger.info(f"  ✅ {selector_name}: {selector}")
            
            if missing_selectors:
                logger.error(f"  ❌ 缺少选择器: {missing_selectors}")
                self.test_results["order_finder_config"] = False
            else:
                logger.info("  ✅ 所有必需选择器都已配置")
                self.test_results["order_finder_config"] = True
            
            # 检查备用策略
            fallback_coverage = 0
            for selector_name in required_selectors:
                fallbacks = selectors.get_all_fallbacks(selector_name)
                if fallbacks:
                    fallback_coverage += 1
                    logger.info(f"  ✅ {selector_name} 有 {len(fallbacks)} 个备用选择器")
                else:
                    logger.warning(f"  ⚠️ {selector_name} 没有备用选择器")
            
            fallback_rate = (fallback_coverage / len(required_selectors)) * 100
            logger.info(f"  备用选择器覆盖率: {fallback_rate:.1f}%")
            
            self.test_results["order_finder_fallbacks"] = fallback_rate >= 75
            
        except Exception as e:
            logger.error(f"订单查找器测试失败: {e}")
            self.test_results["order_finder_config"] = False
            self.test_results["order_finder_fallbacks"] = False
    
    async def test_robust_strategies(self):
        """测试健壮性策略"""
        logger.info("=" * 60)
        logger.info("测试健壮性策略")
        logger.info("=" * 60)
        
        # 测试关键元素的健壮性策略
        key_elements = ["order_container", "order_id", "open_price", "feed_button"]
        
        for element_type in key_elements:
            strategy = get_robust_selector_strategy(element_type)
            if strategy:
                logger.info(f"{element_type} 策略:")
                logger.info(f"  主选择器: {strategy.get('primary', 'N/A')}")
                logger.info(f"  备用选择器: {len(strategy.get('fallbacks', []))} 个")
                logger.info(f"  文本选择器: {strategy.get('text_based', 'N/A')}")
                logger.info(f"  描述: {strategy.get('description', 'N/A')}")
                self.test_results[f"strategy_{element_type}"] = True
            else:
                logger.warning(f"❌ {element_type} 没有健壮性策略")
                self.test_results[f"strategy_{element_type}"] = False
    
    def generate_report(self):
        """生成测试报告"""
        logger.info("=" * 80)
        logger.info("优化选择器测试报告")
        logger.info("=" * 80)
        
        # 按类别分组
        categories = {
            "导航测试": [k for k in self.test_results.keys() if "navigation" in k],
            "优化选择器": [k for k in self.test_results.keys() if "optimized_" in k],
            "备用策略": [k for k in self.test_results.keys() if "fallback_" in k],
            "订单查找器": [k for k in self.test_results.keys() if "order_finder" in k],
            "健壮性策略": [k for k in self.test_results.keys() if "strategy_" in k],
        }
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result is True)
        
        # 按类别显示结果
        for category, test_keys in categories.items():
            if not test_keys:
                continue
                
            category_passed = sum(1 for k in test_keys if self.test_results.get(k, False))
            category_total = len(test_keys)
            
            logger.info(f"\n📋 {category} ({category_passed}/{category_total}):")
            
            for test_key in test_keys:
                result = self.test_results.get(test_key, False)
                if result:
                    logger.info(f"  ✅ {test_key}")
                else:
                    logger.error(f"  ❌ {test_key}")
        
        # 总体统计
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        logger.info(f"\n📊 总体统计:")
        logger.info(f"  总测试数: {total_tests}")
        logger.info(f"  通过: {passed_tests}")
        logger.info(f"  失败: {total_tests - passed_tests}")
        logger.info(f"  成功率: {success_rate:.1f}%")
        
        # 关键发现
        logger.info(f"\n🔍 关键发现:")
        
        # 检查优化选择器效果
        optimized_tests = [k for k in self.test_results.keys() if "optimized_" in k]
        optimized_passed = sum(1 for k in optimized_tests if self.test_results.get(k, False))
        
        if optimized_passed > 0:
            logger.info(f"  ✅ 优化选择器有效 ({optimized_passed}/{len(optimized_tests)})")
        else:
            logger.warning("  ⚠️ 优化选择器可能需要进一步调整")
        
        # 检查备用策略覆盖
        fallback_tests = [k for k in self.test_results.keys() if "fallback_" in k]
        fallback_passed = sum(1 for k in fallback_tests if self.test_results.get(k, False))
        
        if fallback_passed > 0:
            logger.info(f"  ✅ 备用策略有效 ({fallback_passed}/{len(fallback_tests)})")
        else:
            logger.warning("  ⚠️ 备用策略需要改进")
        
        # 总体评估
        if success_rate >= 80:
            logger.info("\n🎉 优化选择器测试成功！")
            logger.info("✅ 选择器策略稳定可靠")
            logger.info("✅ 备用机制完善")
            logger.info("✅ 可以投入生产使用")
        elif success_rate >= 60:
            logger.info("\n⚠️ 优化选择器基本可用")
            logger.info("✅ 主要功能正常")
            logger.info("⚠️ 部分备用策略需要改进")
        else:
            logger.error("\n❌ 优化选择器需要重新设计")
            logger.error("❌ 成功率过低")
            logger.error("❌ 需要重新分析HTML结构")
        
        return success_rate >= 60


async def main():
    """主函数"""
    logger.info("开始测试优化后的选择器...")
    
    tester = OptimizedSelectorTester()
    
    try:
        # 设置测试环境
        setup_success = await tester.setup()
        if not setup_success:
            logger.error("测试环境设置失败")
            return False
        
        # 导航到订单页面
        nav_success = await tester.test_navigation_to_orders()
        if not nav_success:
            logger.error("导航测试失败")
            return False
        
        # 测试优化选择器
        await tester.test_optimized_selectors()
        
        # 测试备用策略
        await tester.test_fallback_strategies()
        
        # 测试订单查找器
        await tester.test_order_finder()
        
        # 测试健壮性策略
        await tester.test_robust_strategies()
        
        # 生成报告
        success = tester.generate_report()
        
        return success
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
