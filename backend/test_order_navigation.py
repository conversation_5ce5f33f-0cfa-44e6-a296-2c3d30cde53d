#!/usr/bin/env python3
"""
订单页面导航验证脚本

验证订单页面的导航和关键选择器是否正确
"""

import os
import sys
import asyncio
import logging
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.spot_service import SpotService
from utils.logger import setup_logger

# 设置日志
os.makedirs("/gold/logs", exist_ok=True)
logger = setup_logger('test_order_nav', "/gold/logs/test_order_nav.log")


class OrderNavigationTester:
    """订单页面导航测试器"""

    def __init__(self):
        self.spot_service = None
        self.test_results = {}

    async def setup(self):
        """设置测试环境"""
        try:
            logger.info("设置测试环境...")

            # 初始化现货服务
            self.spot_service = SpotService()
            success = await self.spot_service.initialize()

            if not success:
                logger.error("现货服务初始化失败")
                return False

            logger.info("✅ 现货服务初始化成功")
            return True

        except Exception as e:
            logger.error(f"设置测试环境失败: {e}")
            return False

    async def test_order_page_navigation(self):
        """测试订单页面导航"""
        logger.info("=" * 60)
        logger.info("测试订单页面导航")
        logger.info("=" * 60)

        try:
            # 直接导航到订单页面URL，不依赖特定元素
            logger.info("直接导航到订单页面URL...")
            orders_url = "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"

            await self.spot_service.page.goto(orders_url, wait_until="domcontentloaded", timeout=45000)
            logger.info(f"页面加载完成: {self.spot_service.page.url}")
            await asyncio.sleep(3)  # 等待页面完全加载

            # 获取当前页面URL
            current_url = self.spot_service.page.url
            logger.info(f"当前页面URL: {current_url}")

            # 验证URL是否正确
            if "myorder" in current_url:
                logger.info("✅ URL验证通过，确认在订单页面")
                self.test_results["navigation"] = True
                self.test_results["url_verification"] = True
                return True
            else:
                logger.warning(f"⚠️ URL验证失败，当前URL: {current_url}")
                self.test_results["navigation"] = False
                self.test_results["url_verification"] = False
                return False

        except Exception as e:
            logger.error(f"测试订单页面导航时发生异常: {e}")
            self.test_results["navigation"] = False
            return False

    async def test_key_selectors(self):
        """测试关键选择器"""
        logger.info("=" * 60)
        logger.info("测试订单页关键选择器")
        logger.info("=" * 60)

        # 定义所有需要测试的选择器（基于文档和实际页面）
        selectors_to_test = {
            # 订单容器选择器
            "order_container_v9a81d21c": ".puwidth[data-v-9a81d21c]",
            "order_container_v14224f5e": ".puwidth[data-v-14224f5e]",
            "order_container_fallback": ".puwidth",

            # 订单项选择器
            "order_item_v9a81d21c": ".deposit-bottom[data-v-9a81d21c]",
            "order_item_v14224f5e": ".deposit-bottom[data-v-14224f5e]",

            # 平仓操作按钮选择器
            "feed_button_v9a81d21c": ".feedingback[data-v-9a81d21c]",
            "feed_button_v14224f5e": ".feedingback[data-v-14224f5e]",
            "default_settlement_v9a81d21c": ".back1[data-v-9a81d21c]",
            "default_settlement_v14224f5e": ".back1[data-v-14224f5e]",
            "close_confirm_v9a81d21c": ".back2[data-v-9a81d21c]",
            "close_confirm_v14224f5e": ".back2[data-v-14224f5e]",
            "close_cancel_v9a81d21c": ".cancelbtn[data-v-9a81d21c]",
            "close_cancel_v14224f5e": ".cancelbtn[data-v-14224f5e]",

            # 价格选择器
            "open_price_v9a81d21c": ".per-price[data-v-9a81d21c], .new-price[data-v-9a81d21c]",
            "open_price_v14224f5e": ".per-price[data-v-14224f5e], .new-price[data-v-14224f5e]",
            "close_price_red_v9a81d21c": ".color1[data-v-9a81d21c]",
            "close_price_red_v14224f5e": ".color1[data-v-14224f5e]",
            "close_price_green_v9a81d21c": ".color2[data-v-9a81d21c]",
            "close_price_green_v14224f5e": ".color2[data-v-14224f5e]",

            # 弹窗选择器
            "popup_order_id_v9a81d21c": ".popcont .boxinfo[data-v-9a81d21c]",
            "popup_order_id_v14224f5e": ".popcont .boxinfo[data-v-14224f5e]",

            # 账户操作按钮
            "deposit_button_v1ffc035e": ".nav-user .auth-button[data-v-1ffc035e]",
            "batch_refund_v14224f5e": ".batch-button[data-v-14224f5e]",

            # 结算按钮
            "settlement_button_v9a81d21c": ".settle-btn .sttlebutton[data-v-9a81d21c]",
            "settlement_button_v14224f5e": ".settle-btn .sttlebutton[data-v-14224f5e]"
        }

        # 测试每个选择器
        for selector_name, selector in selectors_to_test.items():
            await self._test_single_selector(selector_name, selector)

        # 测试文本选择器
        text_selectors = {
            "order_text_selector": "uni-view:has-text('订单号')",
            "deposit_text_selector": "uni-text:has-text('定金')",
            "weight_text_selector": "uni-text:has-text('持单重')",
            "refund_text_selector": "uni-text:has-text('退款')"
        }

        for selector_name, selector in text_selectors.items():
            await self._test_single_selector(selector_name, selector)

        # 专门测试订单开仓价选择器（文档新增）
        await self._test_order_open_price_selectors()

    async def _test_single_selector(self, selector_name: str, selector: str):
        """测试单个选择器"""
        try:
            elements = await self.spot_service.page.query_selector_all(selector)
            visible_count = 0

            for element in elements:
                if await element.is_visible():
                    visible_count += 1

            if visible_count > 0:
                logger.info(f"✅ {selector_name}: 找到 {len(elements)} 个元素，{visible_count} 个可见")
                logger.info(f"   选择器: {selector}")
                self.test_results[selector_name] = True

                # 如果找到元素，尝试获取文本内容
                if len(elements) > 0:
                    try:
                        text_content = await elements[0].text_content()
                        if text_content and text_content.strip():
                            logger.info(f"   文本内容: {text_content.strip()[:50]}...")
                    except:
                        pass
            else:
                logger.warning(f"⚠️ {selector_name}: 找到 {len(elements)} 个元素，但都不可见")
                logger.info(f"   选择器: {selector}")
                self.test_results[selector_name] = False

        except Exception as e:
            logger.error(f"❌ {selector_name}: 测试失败 - {e}")
            logger.info(f"   选择器: {selector}")
            self.test_results[selector_name] = False

    async def _test_order_open_price_selectors(self):
        """专门测试订单开仓价选择器（根据文档新增内容）"""
        logger.info("=" * 60)
        logger.info("测试订单开仓价选择器（文档新增）")
        logger.info("=" * 60)

        # 根据文档，开仓价格选择器为：.per-price[data-v-9a81d21c], .new-price[data-v-9a81d21c]
        open_price_selectors = {
            "open_price_per_v9a81d21c": ".per-price[data-v-9a81d21c]",
            "open_price_new_v9a81d21c": ".new-price[data-v-9a81d21c]",
            "open_price_per_v14224f5e": ".per-price[data-v-14224f5e]",
            "open_price_new_v14224f5e": ".new-price[data-v-14224f5e]",
            "open_price_combined_v9a81d21c": ".per-price[data-v-9a81d21c], .new-price[data-v-9a81d21c]",
            "open_price_combined_v14224f5e": ".per-price[data-v-14224f5e], .new-price[data-v-14224f5e]",
            "open_price_fallback_per": ".per-price",
            "open_price_fallback_new": ".new-price"
        }

        for selector_name, selector in open_price_selectors.items():
            await self._test_price_selector(selector_name, selector)

        # 测试实时平仓价格选择器
        close_price_selectors = {
            "close_price_red_v9a81d21c": ".color1[data-v-9a81d21c]",
            "close_price_green_v9a81d21c": ".color2[data-v-9a81d21c]",
            "close_price_red_v14224f5e": ".color1[data-v-14224f5e]",
            "close_price_green_v14224f5e": ".color2[data-v-14224f5e]",
            "close_price_red_fallback": ".color1",
            "close_price_green_fallback": ".color2"
        }

        for selector_name, selector in close_price_selectors.items():
            await self._test_price_selector(selector_name, selector)

    async def _test_price_selector(self, selector_name: str, selector: str):
        """测试价格选择器并尝试提取价格数值"""
        try:
            elements = await self.spot_service.page.query_selector_all(selector)
            visible_count = 0
            price_values = []

            for element in elements:
                if await element.is_visible():
                    visible_count += 1
                    try:
                        text_content = await element.text_content()
                        if text_content and text_content.strip():
                            # 尝试提取数字价格
                            import re
                            price_match = re.search(r'(\d+\.?\d*)', text_content.strip())
                            if price_match:
                                price_values.append(price_match.group(1))
                    except:
                        pass

            if visible_count > 0:
                logger.info(f"✅ {selector_name}: 找到 {len(elements)} 个元素，{visible_count} 个可见")
                logger.info(f"   选择器: {selector}")
                if price_values:
                    logger.info(f"   提取的价格: {price_values}")
                self.test_results[selector_name] = True
            else:
                logger.warning(f"⚠️ {selector_name}: 找到 {len(elements)} 个元素，但都不可见")
                logger.info(f"   选择器: {selector}")
                self.test_results[selector_name] = False

        except Exception as e:
            logger.error(f"❌ {selector_name}: 测试失败 - {e}")
            logger.info(f"   选择器: {selector}")
            self.test_results[selector_name] = False

    async def test_page_elements(self):
        """测试页面元素"""
        logger.info("测试页面元素...")

        try:
            # 获取页面标题
            page_title = await self.spot_service.page.title()
            logger.info(f"页面标题: {page_title}")

            # 检查页面内容
            page_content = await self.spot_service.page.evaluate("() => document.body.textContent")

            # 检查是否包含订单相关内容
            order_keywords = ["订单", "定金", "持单", "退款"]
            found_keywords = []

            for keyword in order_keywords:
                if keyword in page_content:
                    found_keywords.append(keyword)

            if found_keywords:
                logger.info(f"✅ 页面包含订单相关关键词: {found_keywords}")
                self.test_results["page_content"] = True
            else:
                logger.warning("⚠️ 页面未包含预期的订单相关关键词")
                self.test_results["page_content"] = False

            # 检查页面是否有错误信息
            error_selectors = [
                ".error",
                ".error-message",
                "uni-view:has-text('错误')",
                "uni-view:has-text('失败')"
            ]

            has_error = False
            for selector in error_selectors:
                try:
                    error_element = await self.spot_service.page.query_selector(selector)
                    if error_element and await error_element.is_visible():
                        error_text = await error_element.text_content()
                        logger.warning(f"⚠️ 检测到错误信息: {error_text}")
                        has_error = True
                        break
                except:
                    continue

            if not has_error:
                logger.info("✅ 页面无错误信息")
                self.test_results["no_errors"] = True
            else:
                self.test_results["no_errors"] = False

        except Exception as e:
            logger.error(f"测试页面元素时发生异常: {e}")
            self.test_results["page_content"] = False
            self.test_results["no_errors"] = False

    def generate_report(self):
        """生成测试报告"""
        logger.info("=" * 80)
        logger.info("订单页面选择器验证报告")
        logger.info("=" * 80)

        # 按类别分组测试结果
        categories = {
            "导航测试": ["navigation", "url_verification"],
            "订单容器选择器": [k for k in self.test_results.keys() if "order_container" in k or "order_item" in k],
            "平仓操作按钮": [k for k in self.test_results.keys() if any(x in k for x in ["feed_button", "default_settlement", "close_confirm", "close_cancel"])],
            "价格选择器": [k for k in self.test_results.keys() if any(x in k for x in ["open_price", "close_price"])],
            "弹窗选择器": [k for k in self.test_results.keys() if "popup" in k],
            "账户操作按钮": [k for k in self.test_results.keys() if any(x in k for x in ["deposit_button", "batch_refund"])],
            "结算按钮": [k for k in self.test_results.keys() if "settlement_button" in k],
            "文本选择器": [k for k in self.test_results.keys() if "text_selector" in k],
            "页面元素": [k for k in self.test_results.keys() if any(x in k for x in ["page_content", "no_errors"])]
        }

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result is True)
        failed_tests = sum(1 for result in self.test_results.values() if result is False)

        # 按类别显示结果
        for category, test_keys in categories.items():
            if not test_keys:
                continue

            category_tests = [k for k in test_keys if k in self.test_results]
            if not category_tests:
                continue

            category_passed = sum(1 for k in category_tests if self.test_results[k] is True)
            category_total = len(category_tests)

            logger.info(f"\n📋 {category} ({category_passed}/{category_total}):")

            for test_key in category_tests:
                result = self.test_results[test_key]
                if result is True:
                    logger.info(f"  ✅ {test_key}")
                elif result is False:
                    logger.error(f"  ❌ {test_key}")
                else:
                    logger.info(f"  ⚠️ {test_key}: 跳过")

        # 总体统计
        logger.info(f"\n📊 总体统计:")
        logger.info(f"  总测试数: {total_tests}")
        logger.info(f"  通过: {passed_tests}")
        logger.info(f"  失败: {failed_tests}")

        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        logger.info(f"  成功率: {success_rate:.1f}%")

        # 关键发现
        logger.info(f"\n🔍 关键发现:")

        # 检查哪个data-v版本有效
        v9a81d21c_selectors = [k for k in self.test_results.keys() if "v9a81d21c" in k and self.test_results[k]]
        v14224f5e_selectors = [k for k in self.test_results.keys() if "v14224f5e" in k and self.test_results[k]]

        if v9a81d21c_selectors:
            logger.info(f"  ✅ data-v-9a81d21c 版本有效 ({len(v9a81d21c_selectors)} 个选择器)")
        if v14224f5e_selectors:
            logger.info(f"  ✅ data-v-14224f5e 版本有效 ({len(v14224f5e_selectors)} 个选择器)")

        if not v9a81d21c_selectors and not v14224f5e_selectors:
            logger.warning("  ⚠️ 两个data-v版本都无效，可能需要更新选择器")

        # 价格选择器测试结果
        price_selectors = [k for k in self.test_results.keys() if any(x in k for x in ["open_price", "close_price"]) and self.test_results[k]]
        if price_selectors:
            logger.info(f"  ✅ 价格选择器有效 ({len(price_selectors)} 个)")
        else:
            logger.warning("  ⚠️ 价格选择器可能需要更新")

        # 判断总体结果
        critical_tests = ["navigation", "url_verification"]
        critical_passed = all(self.test_results.get(test, False) for test in critical_tests)

        overall_success = critical_passed and success_rate >= 50  # 至少50%的选择器有效

        if overall_success:
            logger.info("\n🎉 订单页面选择器验证成功！")
            logger.info("✅ 页面导航正常")
            logger.info("✅ 关键选择器可用")
            if success_rate >= 80:
                logger.info("✅ 选择器兼容性良好")
            else:
                logger.info("⚠️ 部分选择器需要更新，但基本功能可用")
        else:
            logger.error("\n❌ 订单页面选择器验证失败！")
            if not critical_passed:
                logger.error("❌ 页面导航失败")
            if success_rate < 50:
                logger.error("❌ 大部分选择器失效，需要全面更新")

        return overall_success


async def main():
    """主函数"""
    logger.info("开始验证订单页面导航...")

    tester = OrderNavigationTester()

    try:
        # 设置测试环境
        setup_success = await tester.setup()
        if not setup_success:
            logger.error("测试环境设置失败")
            return False

        # 测试订单页面导航
        navigation_success = await tester.test_order_page_navigation()

        if navigation_success:
            # 测试关键选择器
            await tester.test_key_selectors()

            # 测试页面元素
            await tester.test_page_elements()

        # 生成测试报告
        overall_success = tester.generate_report()

        return overall_success

    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


if __name__ == "__main__":
    # 运行测试
    test_result = asyncio.run(main())

    if test_result:
        logger.info("\n🎉 订单页面导航验证全部通过！")
        sys.exit(0)
    else:
        logger.error("\n💥 订单页面导航验证失败！")
        sys.exit(1)
