#!/usr/bin/env python3
"""
测试违约结算按钮查找和点击功能

验证违约结算按钮能够100%被找到并成功点击
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.spot_service import SpotService
from services.default_settlement_service import DefaultSettlementService
from utils.logger import setup_logger

# 设置日志
os.makedirs("/gold/logs", exist_ok=True)
logger = setup_logger('test_default_settlement', "/gold/logs/test_default_settlement.log")


class DefaultSettlementTester:
    """违约结算测试器"""
    
    def __init__(self):
        self.spot_service = None
        self.settlement_service = None
        self.test_results = {}
    
    async def setup(self):
        """设置测试环境"""
        try:
            logger.info("设置测试环境...")
            
            # 初始化现货服务
            self.spot_service = SpotService()
            success = await self.spot_service.initialize()
            
            if not success:
                logger.error("现货服务初始化失败")
                return False
            
            # 初始化违约结算服务
            self.settlement_service = DefaultSettlementService(self.spot_service.page)
            
            logger.info("✅ 测试环境设置成功")
            return True
            
        except Exception as e:
            logger.error(f"设置测试环境失败: {e}")
            return False
    
    async def test_find_all_orders_with_settlement_buttons(self):
        """测试查找所有有违约结算按钮的订单"""
        logger.info("=" * 60)
        logger.info("测试查找所有有违约结算按钮的订单")
        logger.info("=" * 60)
        
        try:
            # 导航到订单页面
            nav_result = await self.settlement_service._navigate_to_orders()
            if not nav_result["success"]:
                logger.error(f"导航失败: {nav_result['message']}")
                return False
            
            # 获取所有订单容器
            order_containers = await self.spot_service.page.query_selector_all(".puwidth")
            logger.info(f"找到 {len(order_containers)} 个订单容器")
            
            orders_with_settlement = []
            
            for i, container in enumerate(order_containers):
                try:
                    # 提取订单ID
                    order_sn_element = await container.query_selector(".order-sn")
                    if order_sn_element:
                        order_text = await order_sn_element.text_content()
                        import re
                        match = re.search(r'订单号[：:]\s*([A-Z0-9]+)', order_text)
                        if match:
                            order_id = match.group(1)
                            
                            # 检查是否有违约结算按钮
                            has_settlement = await self._check_settlement_button_in_container(container)
                            
                            if has_settlement:
                                orders_with_settlement.append({
                                    "order_id": order_id,
                                    "container_index": i + 1,
                                    "settlement_strategies": has_settlement
                                })
                                logger.info(f"✅ 订单 {order_id} 有违约结算按钮 (策略: {len(has_settlement)} 种)")
                            else:
                                logger.info(f"❌ 订单 {order_id} 无违约结算按钮")
                
                except Exception as e:
                    logger.warning(f"处理订单容器 {i+1} 时出错: {e}")
                    continue
            
            self.test_results["orders_with_settlement"] = orders_with_settlement
            self.test_results["total_orders"] = len(order_containers)
            
            logger.info(f"📊 结果统计:")
            logger.info(f"   总订单数: {len(order_containers)}")
            logger.info(f"   有违约结算按钮: {len(orders_with_settlement)}")
            
            return len(orders_with_settlement) > 0
            
        except Exception as e:
            logger.error(f"测试查找违约结算按钮失败: {e}")
            return False
    
    async def _check_settlement_button_in_container(self, container):
        """检查容器中的违约结算按钮"""
        available_strategies = []
        
        # 测试所有策略
        selectors = {
            "primary": ".back1",
            "with_data_v": ".back1[data-v-14224f5e]",
            "full_path": ".deposit-right .back1",
            "text_based": "//uni-view[contains(text(), '违约结算')]",
            "class_text_combo": "//uni-view[contains(@class, 'back1') and contains(text(), '违约结算')]",
            "in_button_container": ".order-button .back1",
            "fallback_text": "//uni-view[text()='违约结算']",
        }
        
        for strategy, selector in selectors.items():
            try:
                if selector.startswith("//"):
                    # XPath选择器
                    button = await container.query_selector(f"xpath={selector}")
                else:
                    # CSS选择器
                    button = await container.query_selector(selector)
                
                if button and await button.is_visible():
                    # 验证按钮文本
                    button_text = await button.text_content()
                    if "违约结算" in button_text:
                        available_strategies.append(strategy)
                        
            except Exception:
                continue
        
        return available_strategies if available_strategies else None
    
    async def test_settlement_button_availability(self):
        """测试违约结算按钮可用性检查"""
        logger.info("=" * 60)
        logger.info("测试违约结算按钮可用性检查")
        logger.info("=" * 60)
        
        orders_with_settlement = self.test_results.get("orders_with_settlement", [])
        if not orders_with_settlement:
            logger.warning("没有找到有违约结算按钮的订单")
            return False
        
        success_count = 0
        
        for order_info in orders_with_settlement:
            order_id = order_info["order_id"]
            
            try:
                logger.info(f"检查订单 {order_id} 的违约结算按钮可用性...")
                
                check_result = await self.settlement_service.check_settlement_button_availability(order_id)
                
                if check_result["success"]:
                    logger.info(f"✅ 订单 {order_id}: 违约结算按钮可用")
                    logger.info(f"   可用策略: {check_result['available_strategies']}")
                    success_count += 1
                else:
                    logger.error(f"❌ 订单 {order_id}: 违约结算按钮不可用")
                    logger.error(f"   错误信息: {check_result['message']}")
                
            except Exception as e:
                logger.error(f"❌ 检查订单 {order_id} 时出错: {e}")
        
        success_rate = (success_count / len(orders_with_settlement)) * 100 if orders_with_settlement else 0
        logger.info(f"📊 违约结算按钮可用性检查成功率: {success_rate:.1f}% ({success_count}/{len(orders_with_settlement)})")
        
        self.test_results["availability_success_rate"] = success_rate
        return success_rate >= 100  # 要求100%成功率
    
    async def test_settlement_button_click_simulation(self):
        """测试违约结算按钮点击模拟（不实际执行）"""
        logger.info("=" * 60)
        logger.info("测试违约结算按钮点击模拟")
        logger.info("=" * 60)
        
        orders_with_settlement = self.test_results.get("orders_with_settlement", [])
        if not orders_with_settlement:
            logger.warning("没有找到有违约结算按钮的订单")
            return False
        
        # 选择第一个有违约结算按钮的订单进行测试
        test_order = orders_with_settlement[0]
        order_id = test_order["order_id"]
        
        try:
            logger.info(f"模拟点击订单 {order_id} 的违约结算按钮...")
            
            # 导航到订单页面
            nav_result = await self.settlement_service._navigate_to_orders()
            if not nav_result["success"]:
                logger.error(f"导航失败: {nav_result['message']}")
                return False
            
            # 查找订单容器
            order_container = await self.settlement_service._find_order_container(order_id)
            if not order_container:
                logger.error(f"未找到订单容器: {order_id}")
                return False
            
            # 查找违约结算按钮（不点击）
            settlement_button = None
            for strategy in ["primary", "with_data_v", "full_path", "text_based"]:
                button = await self.settlement_service._try_find_settlement_button(order_container, strategy)
                if button:
                    settlement_button = button
                    logger.info(f"✅ 使用 {strategy} 策略找到违约结算按钮")
                    break
            
            if settlement_button:
                # 验证按钮属性
                button_text = await settlement_button.text_content()
                is_visible = await settlement_button.is_visible()
                is_enabled = await settlement_button.is_enabled()
                
                logger.info(f"📋 违约结算按钮属性:")
                logger.info(f"   文本: {button_text}")
                logger.info(f"   可见: {is_visible}")
                logger.info(f"   可用: {is_enabled}")
                
                if "违约结算" in button_text and is_visible and is_enabled:
                    logger.info(f"🎉 违约结算按钮验证成功: 订单 {order_id}")
                    self.test_results["button_click_simulation"] = True
                    return True
                else:
                    logger.error(f"❌ 违约结算按钮验证失败: 订单 {order_id}")
                    self.test_results["button_click_simulation"] = False
                    return False
            else:
                logger.error(f"❌ 未找到违约结算按钮: 订单 {order_id}")
                self.test_results["button_click_simulation"] = False
                return False
                
        except Exception as e:
            logger.error(f"违约结算按钮点击模拟失败: {e}")
            self.test_results["button_click_simulation"] = False
            return False
    
    def generate_report(self):
        """生成测试报告"""
        logger.info("=" * 80)
        logger.info("违约结算按钮测试报告")
        logger.info("=" * 80)
        
        total_orders = self.test_results.get("total_orders", 0)
        orders_with_settlement = self.test_results.get("orders_with_settlement", [])
        availability_rate = self.test_results.get("availability_success_rate", 0)
        click_simulation = self.test_results.get("button_click_simulation", False)
        
        logger.info(f"📊 测试结果摘要:")
        logger.info(f"   总订单数: {total_orders}")
        logger.info(f"   有违约结算按钮的订单: {len(orders_with_settlement)}")
        logger.info(f"   违约结算按钮覆盖率: {(len(orders_with_settlement)/total_orders*100) if total_orders > 0 else 0:.1f}%")
        logger.info(f"   按钮可用性检查成功率: {availability_rate:.1f}%")
        logger.info(f"   按钮点击模拟: {'✅ 成功' if click_simulation else '❌ 失败'}")
        
        # 详细信息
        if orders_with_settlement:
            logger.info(f"\n📋 有违约结算按钮的订单详情:")
            for order_info in orders_with_settlement:
                order_id = order_info["order_id"]
                strategies = order_info["settlement_strategies"]
                logger.info(f"   订单 {order_id}: {len(strategies)} 种策略可用")
                for strategy in strategies:
                    logger.info(f"     - {strategy}")
        
        # 评估结果
        if (len(orders_with_settlement) > 0 and 
            availability_rate >= 100 and 
            click_simulation):
            logger.info("\n🎉 违约结算按钮测试成功！")
            logger.info("✅ 能够100%找到违约结算按钮")
            logger.info("✅ 按钮可用性检查100%成功")
            logger.info("✅ 按钮点击模拟成功")
            return True
        else:
            logger.warning("\n⚠️ 违约结算按钮测试需要改进")
            if len(orders_with_settlement) == 0:
                logger.warning("❌ 未找到有违约结算按钮的订单")
            if availability_rate < 100:
                logger.warning("❌ 按钮可用性检查成功率不足100%")
            if not click_simulation:
                logger.warning("❌ 按钮点击模拟失败")
            return False


async def main():
    """主函数"""
    logger.info("开始测试违约结算按钮功能...")
    
    tester = DefaultSettlementTester()
    
    try:
        # 设置测试环境
        setup_success = await tester.setup()
        if not setup_success:
            logger.error("测试环境设置失败")
            return False
        
        # 测试查找所有有违约结算按钮的订单
        find_success = await tester.test_find_all_orders_with_settlement_buttons()
        if not find_success:
            logger.error("查找违约结算按钮测试失败")
            return False
        
        # 测试违约结算按钮可用性
        availability_success = await tester.test_settlement_button_availability()
        
        # 测试违约结算按钮点击模拟
        click_success = await tester.test_settlement_button_click_simulation()
        
        # 生成报告
        success = tester.generate_report()
        
        return success
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
