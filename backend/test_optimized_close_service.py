#!/usr/bin/env python3
"""
测试优化的平仓服务

验证仅使用违约结算按钮的平仓功能
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.spot_service import SpotService
from services.optimized_spot_close_service import OptimizedSpotCloseService
from utils.logger import setup_logger

# 设置日志
os.makedirs("/gold/logs", exist_ok=True)
logger = setup_logger('test_optimized_close', "/gold/logs/test_optimized_close.log")


class OptimizedCloseServiceTester:
    """优化平仓服务测试器"""
    
    def __init__(self):
        self.spot_service = None
        self.close_service = None
        self.test_results = {}
    
    async def setup(self):
        """设置测试环境"""
        try:
            logger.info("设置测试环境...")
            
            # 初始化现货服务
            self.spot_service = SpotService()
            success = await self.spot_service.initialize()
            
            if not success:
                logger.error("现货服务初始化失败")
                return False
            
            # 初始化优化平仓服务
            self.close_service = OptimizedSpotCloseService(self.spot_service.page)
            
            logger.info("✅ 测试环境设置成功")
            return True
            
        except Exception as e:
            logger.error(f"设置测试环境失败: {e}")
            return False
    
    async def test_get_closable_orders(self):
        """测试获取可平仓订单"""
        logger.info("=" * 60)
        logger.info("测试获取可平仓订单")
        logger.info("=" * 60)
        
        try:
            closable_result = await self.close_service.get_closable_orders()
            
            if closable_result["success"]:
                logger.info(f"✅ 成功获取可平仓订单")
                logger.info(f"📊 统计信息:")
                logger.info(f"   总订单数: {closable_result['total_orders']}")
                logger.info(f"   可平仓订单数: {closable_result['closable_orders']}")
                logger.info(f"   可平仓率: {(closable_result['closable_orders']/closable_result['total_orders']*100) if closable_result['total_orders'] > 0 else 0:.1f}%")
                
                # 显示可平仓订单详情
                for order in closable_result["orders"]:
                    logger.info(f"📋 可平仓订单: {order['order_id']}")
                    logger.info(f"   开仓价格: {order.get('open_price', 'N/A')}")
                    logger.info(f"   当前价格: {order.get('current_price', 'N/A')}")
                
                self.test_results["closable_orders"] = closable_result
                return True
            else:
                logger.error(f"❌ 获取可平仓订单失败: {closable_result['message']}")
                self.test_results["closable_orders"] = closable_result
                return False
                
        except Exception as e:
            logger.error(f"测试获取可平仓订单失败: {e}")
            return False
    
    async def test_close_position_simulation(self):
        """测试平仓操作模拟（不实际执行）"""
        logger.info("=" * 60)
        logger.info("测试平仓操作模拟")
        logger.info("=" * 60)
        
        closable_result = self.test_results.get("closable_orders")
        if not closable_result or not closable_result.get("success") or not closable_result.get("orders"):
            logger.warning("没有可平仓的订单进行测试")
            return False
        
        # 选择第一个可平仓订单进行模拟测试
        test_order = closable_result["orders"][0]
        order_id = test_order["order_id"]
        
        try:
            logger.info(f"模拟平仓操作: 订单 {order_id}")
            
            # 检查违约结算按钮可用性（这是实际检查，不会执行平仓）
            availability_result = await self.close_service.settlement_service.check_settlement_button_availability(order_id)
            
            if availability_result["success"]:
                logger.info(f"✅ 违约结算按钮可用性验证通过")
                logger.info(f"📋 可用策略: {availability_result['available_strategies']}")
                logger.info(f"📋 策略数量: {len(availability_result['available_strategies'])}")
                
                # 模拟平仓流程检查
                logger.info("🔍 模拟平仓流程检查:")
                logger.info("   1. ✅ 导航到订单页面")
                logger.info("   2. ✅ 查找目标订单")
                logger.info("   3. ✅ 找到违约结算按钮")
                logger.info("   4. 🚫 跳过实际点击（模拟模式）")
                logger.info("   5. 🚫 跳过确认弹窗处理（模拟模式）")
                
                self.test_results["close_simulation"] = {
                    "success": True,
                    "order_id": order_id,
                    "available_strategies": availability_result['available_strategies'],
                    "simulation_passed": True
                }
                
                logger.info(f"🎉 平仓操作模拟成功: 订单 {order_id}")
                return True
            else:
                logger.error(f"❌ 违约结算按钮不可用: {availability_result['message']}")
                self.test_results["close_simulation"] = {
                    "success": False,
                    "order_id": order_id,
                    "error": availability_result['message']
                }
                return False
                
        except Exception as e:
            logger.error(f"平仓操作模拟失败: {e}")
            self.test_results["close_simulation"] = {
                "success": False,
                "order_id": order_id,
                "error": str(e)
            }
            return False
    
    async def test_batch_close_simulation(self):
        """测试批量平仓模拟"""
        logger.info("=" * 60)
        logger.info("测试批量平仓模拟")
        logger.info("=" * 60)
        
        closable_result = self.test_results.get("closable_orders")
        if not closable_result or not closable_result.get("success") or not closable_result.get("orders"):
            logger.warning("没有可平仓的订单进行批量测试")
            return False
        
        # 获取所有可平仓订单ID
        order_ids = [order["order_id"] for order in closable_result["orders"]]
        
        try:
            logger.info(f"模拟批量平仓: {len(order_ids)} 个订单")
            
            batch_simulation_results = {
                "total_orders": len(order_ids),
                "simulation_results": {},
                "success_count": 0,
                "fail_count": 0
            }
            
            for i, order_id in enumerate(order_ids, 1):
                logger.info(f"📋 模拟处理订单 {i}/{len(order_ids)}: {order_id}")
                
                # 检查每个订单的违约结算按钮可用性
                availability_result = await self.close_service.settlement_service.check_settlement_button_availability(order_id)
                
                if availability_result["success"]:
                    logger.info(f"   ✅ 订单 {order_id}: 违约结算按钮可用")
                    batch_simulation_results["simulation_results"][order_id] = {
                        "success": True,
                        "strategies": availability_result['available_strategies']
                    }
                    batch_simulation_results["success_count"] += 1
                else:
                    logger.error(f"   ❌ 订单 {order_id}: 违约结算按钮不可用")
                    batch_simulation_results["simulation_results"][order_id] = {
                        "success": False,
                        "error": availability_result['message']
                    }
                    batch_simulation_results["fail_count"] += 1
            
            batch_simulation_results["success_rate"] = (batch_simulation_results["success_count"] / len(order_ids)) * 100
            
            logger.info(f"📊 批量平仓模拟结果:")
            logger.info(f"   总订单数: {batch_simulation_results['total_orders']}")
            logger.info(f"   模拟成功: {batch_simulation_results['success_count']}")
            logger.info(f"   模拟失败: {batch_simulation_results['fail_count']}")
            logger.info(f"   成功率: {batch_simulation_results['success_rate']:.1f}%")
            
            self.test_results["batch_simulation"] = batch_simulation_results
            
            return batch_simulation_results["success_rate"] >= 100  # 要求100%成功率
            
        except Exception as e:
            logger.error(f"批量平仓模拟失败: {e}")
            return False
    
    async def test_operation_stats(self):
        """测试操作统计功能"""
        logger.info("=" * 60)
        logger.info("测试操作统计功能")
        logger.info("=" * 60)
        
        try:
            # 获取初始统计
            initial_stats = self.close_service.get_operation_stats()
            logger.info(f"📊 初始统计: {initial_stats}")
            
            # 重置统计
            self.close_service.reset_stats()
            reset_stats = self.close_service.get_operation_stats()
            logger.info(f"📊 重置后统计: {reset_stats}")
            
            # 验证重置是否成功
            if (reset_stats["total_attempts"] == 0 and 
                reset_stats["successful_closures"] == 0 and 
                reset_stats["failed_closures"] == 0):
                logger.info("✅ 统计重置功能正常")
                self.test_results["stats_test"] = True
                return True
            else:
                logger.error("❌ 统计重置功能异常")
                self.test_results["stats_test"] = False
                return False
                
        except Exception as e:
            logger.error(f"测试操作统计功能失败: {e}")
            self.test_results["stats_test"] = False
            return False
    
    def generate_report(self):
        """生成测试报告"""
        logger.info("=" * 80)
        logger.info("优化平仓服务测试报告")
        logger.info("=" * 80)
        
        closable_result = self.test_results.get("closable_orders", {})
        close_simulation = self.test_results.get("close_simulation", {})
        batch_simulation = self.test_results.get("batch_simulation", {})
        stats_test = self.test_results.get("stats_test", False)
        
        logger.info(f"📊 测试结果摘要:")
        
        # 可平仓订单测试
        if closable_result.get("success"):
            total_orders = closable_result.get("total_orders", 0)
            closable_orders = closable_result.get("closable_orders", 0)
            closable_rate = (closable_orders / total_orders * 100) if total_orders > 0 else 0
            logger.info(f"   可平仓订单检测: ✅ 成功")
            logger.info(f"     总订单数: {total_orders}")
            logger.info(f"     可平仓: {closable_orders}")
            logger.info(f"     可平仓率: {closable_rate:.1f}%")
        else:
            logger.info(f"   可平仓订单检测: ❌ 失败")
        
        # 单个平仓模拟测试
        if close_simulation.get("success"):
            logger.info(f"   单个平仓模拟: ✅ 成功")
            logger.info(f"     测试订单: {close_simulation.get('order_id')}")
            logger.info(f"     可用策略数: {len(close_simulation.get('available_strategies', []))}")
        else:
            logger.info(f"   单个平仓模拟: ❌ 失败")
        
        # 批量平仓模拟测试
        if batch_simulation.get("success_rate", 0) >= 100:
            logger.info(f"   批量平仓模拟: ✅ 成功")
            logger.info(f"     成功率: {batch_simulation.get('success_rate', 0):.1f}%")
            logger.info(f"     测试订单数: {batch_simulation.get('total_orders', 0)}")
        else:
            logger.info(f"   批量平仓模拟: ❌ 失败")
            logger.info(f"     成功率: {batch_simulation.get('success_rate', 0):.1f}%")
        
        # 统计功能测试
        logger.info(f"   操作统计功能: {'✅ 成功' if stats_test else '❌ 失败'}")
        
        # 评估总体结果
        all_tests_passed = (
            closable_result.get("success", False) and
            close_simulation.get("success", False) and
            batch_simulation.get("success_rate", 0) >= 100 and
            stats_test
        )
        
        if all_tests_passed:
            logger.info("\n🎉 优化平仓服务测试全部通过！")
            logger.info("✅ 可平仓订单检测正常")
            logger.info("✅ 违约结算按钮100%可用")
            logger.info("✅ 单个平仓模拟成功")
            logger.info("✅ 批量平仓模拟100%成功")
            logger.info("✅ 操作统计功能正常")
            logger.info("🚀 服务已准备好用于实际平仓操作")
            return True
        else:
            logger.warning("\n⚠️ 优化平仓服务测试存在问题")
            if not closable_result.get("success"):
                logger.warning("❌ 可平仓订单检测失败")
            if not close_simulation.get("success"):
                logger.warning("❌ 单个平仓模拟失败")
            if batch_simulation.get("success_rate", 0) < 100:
                logger.warning("❌ 批量平仓模拟成功率不足100%")
            if not stats_test:
                logger.warning("❌ 操作统计功能异常")
            return False


async def main():
    """主函数"""
    logger.info("开始测试优化平仓服务...")
    
    tester = OptimizedCloseServiceTester()
    
    try:
        # 设置测试环境
        setup_success = await tester.setup()
        if not setup_success:
            logger.error("测试环境设置失败")
            return False
        
        # 测试获取可平仓订单
        closable_success = await tester.test_get_closable_orders()
        
        # 测试单个平仓模拟
        close_success = await tester.test_close_position_simulation()
        
        # 测试批量平仓模拟
        batch_success = await tester.test_batch_close_simulation()
        
        # 测试操作统计功能
        stats_success = await tester.test_operation_stats()
        
        # 生成报告
        success = tester.generate_report()
        
        return success
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
