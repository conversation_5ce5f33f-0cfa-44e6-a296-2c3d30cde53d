#!/usr/bin/env python3
"""
现货订单查找器

基于优化的选择器策略，提供稳定的订单查找功能
"""

import re
import time
import logging
from typing import Optional, Dict, Any, List
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from .spot_selectors_optimized import optimized_selectors, get_robust_selector_strategy

logger = logging.getLogger(__name__)


class SpotOrderFinder:
    """现货订单查找器"""
    
    def __init__(self, driver, timeout: int = 10):
        """
        初始化订单查找器
        
        Args:
            driver: WebDriver实例
            timeout: 超时时间（秒）
        """
        self.driver = driver
        self.timeout = timeout
        self.selectors = optimized_selectors
    
    def find_order_by_id(self, order_id: str) -> Optional[Dict[str, Any]]:
        """
        根据订单ID查找订单（使用优化的选择器策略）
        
        Args:
            order_id: 订单ID
            
        Returns:
            订单信息字典，如果未找到则返回None
        """
        logger.info(f"使用优化选择器查找订单: {order_id}")
        
        try:
            # 1. 等待页面加载
            if not self._wait_for_page_load():
                logger.error("页面加载超时")
                return None
            
            # 2. 查找所有订单容器
            order_containers = self._find_order_containers()
            if not order_containers:
                logger.warning("未找到任何订单容器")
                return None
            
            logger.info(f"找到 {len(order_containers)} 个订单容器")
            
            # 3. 遍历订单容器，查找匹配的订单ID
            for i, container in enumerate(order_containers):
                try:
                    order_info = self._extract_order_info(container, order_id)
                    if order_info:
                        logger.info(f"在第 {i+1} 个容器中找到匹配订单: {order_id}")
                        return order_info
                except Exception as e:
                    logger.warning(f"处理第 {i+1} 个订单容器时出错: {e}")
                    continue
            
            logger.warning(f"未找到匹配的订单: {order_id}")
            return None
            
        except Exception as e:
            logger.error(f"查找订单失败: {e}")
            return None
    
    def _wait_for_page_load(self) -> bool:
        """等待页面加载完成"""
        try:
            # 使用多种策略等待页面加载
            strategies = [
                (".puwidth", "订单容器"),
                (".order-sn", "订单号"),
                ("//uni-view[contains(text(), '订单号')]", "订单号文本"),
            ]
            
            for selector, desc in strategies:
                try:
                    if selector.startswith("//"):
                        # XPath选择器
                        WebDriverWait(self.driver, self.timeout).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                    else:
                        # CSS选择器
                        WebDriverWait(self.driver, self.timeout).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                    logger.info(f"页面加载完成，检测到: {desc}")
                    return True
                except TimeoutException:
                    logger.debug(f"等待 {desc} 超时，尝试下一个策略")
                    continue
            
            logger.warning("所有页面加载检测策略都超时")
            return False
            
        except Exception as e:
            logger.error(f"等待页面加载失败: {e}")
            return False
    
    def _find_order_containers(self) -> List:
        """查找所有订单容器"""
        containers = []
        
        # 使用备用选择器策略
        fallback_selectors = self.selectors.get_all_fallbacks("order_container")
        
        for selector in fallback_selectors:
            try:
                if selector.startswith("//"):
                    # XPath选择器
                    elements = self.driver.find_elements(By.XPATH, selector)
                else:
                    # CSS选择器
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                
                if elements:
                    logger.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个订单容器")
                    containers = elements
                    break
                    
            except Exception as e:
                logger.debug(f"选择器 '{selector}' 查找失败: {e}")
                continue
        
        return containers
    
    def _extract_order_info(self, container, target_order_id: str) -> Optional[Dict[str, Any]]:
        """
        从订单容器中提取订单信息
        
        Args:
            container: 订单容器元素
            target_order_id: 目标订单ID
            
        Returns:
            订单信息字典，如果不匹配则返回None
        """
        try:
            # 1. 提取订单ID
            order_id = self._extract_order_id(container)
            if not order_id:
                logger.debug("未能提取订单ID")
                return None
            
            # 2. 检查是否匹配目标订单ID
            if order_id != target_order_id:
                logger.debug(f"订单ID不匹配: {order_id} != {target_order_id}")
                return None
            
            # 3. 提取详细信息
            order_info = {
                "id": order_id,
                "element": container,
                "container_html": container.get_attribute("outerHTML")[:200] + "..."  # 截取部分HTML用于调试
            }
            
            # 4. 提取价格信息
            price_info = self._extract_price_info(container)
            order_info.update(price_info)
            
            # 5. 提取其他信息
            other_info = self._extract_other_info(container)
            order_info.update(other_info)
            
            logger.info(f"成功提取订单信息: {order_id}")
            return order_info
            
        except Exception as e:
            logger.warning(f"提取订单信息失败: {e}")
            return None
    
    def _extract_order_id(self, container) -> Optional[str]:
        """提取订单ID"""
        # 使用多种策略提取订单ID
        strategies = [
            # 策略1: 使用语义化类名
            (".order-sn", "class"),
            # 策略2: 使用文本内容匹配
            ("//uni-view[contains(text(), '订单号')]", "xpath"),
            # 策略3: 使用备用选择器
            (".deposit-box", "class"),
        ]
        
        for selector, method in strategies:
            try:
                if method == "xpath":
                    element = container.find_element(By.XPATH, selector)
                else:
                    element = container.find_element(By.CSS_SELECTOR, selector)
                
                text = element.text.strip()
                logger.debug(f"使用 {method} 选择器 '{selector}' 获取文本: {text}")
                
                # 提取订单ID
                match = re.search(r'订单号[：:]\s*([A-Z0-9]+)', text)
                if match:
                    order_id = match.group(1)
                    logger.debug(f"提取到订单ID: {order_id}")
                    return order_id
                    
            except NoSuchElementException:
                logger.debug(f"选择器 '{selector}' 未找到元素")
                continue
            except Exception as e:
                logger.debug(f"使用选择器 '{selector}' 提取订单ID失败: {e}")
                continue
        
        logger.debug("所有策略都未能提取到订单ID")
        return None
    
    def _extract_price_info(self, container) -> Dict[str, Any]:
        """提取价格信息"""
        price_info = {}
        
        # 提取开仓价格
        try:
            open_price_element = container.find_element(By.CSS_SELECTOR, ".per-price")
            price_info["open_price"] = open_price_element.text.strip()
            logger.debug(f"提取开仓价格: {price_info['open_price']}")
        except NoSuchElementException:
            logger.debug("未找到开仓价格元素")
        
        # 提取当前价格
        try:
            current_price_element = container.find_element(By.CSS_SELECTOR, ".new-price")
            price_info["current_price"] = current_price_element.text.strip()
            logger.debug(f"提取当前价格: {price_info['current_price']}")
        except NoSuchElementException:
            logger.debug("未找到当前价格元素")
        
        # 提取彩色价格（盈亏）
        try:
            red_price_elements = container.find_elements(By.CSS_SELECTOR, ".color1")
            green_price_elements = container.find_elements(By.CSS_SELECTOR, ".color2")
            
            if red_price_elements:
                price_info["red_prices"] = [elem.text.strip() for elem in red_price_elements]
                logger.debug(f"提取红色价格: {price_info['red_prices']}")
            
            if green_price_elements:
                price_info["green_prices"] = [elem.text.strip() for elem in green_price_elements]
                logger.debug(f"提取绿色价格: {price_info['green_prices']}")
                
        except Exception as e:
            logger.debug(f"提取彩色价格失败: {e}")
        
        return price_info
    
    def _extract_other_info(self, container) -> Dict[str, Any]:
        """提取其他信息"""
        other_info = {}
        
        # 提取货款信息
        try:
            payment_element = container.find_element(By.CSS_SELECTOR, ".order-payment")
            other_info["payment"] = payment_element.text.strip()
            logger.debug(f"提取货款信息: {other_info['payment']}")
        except NoSuchElementException:
            logger.debug("未找到货款信息元素")
        
        # 提取按钮信息
        try:
            button_container = container.find_element(By.CSS_SELECTOR, ".order-button")
            buttons = button_container.find_elements(By.TAG_NAME, "uni-view")
            other_info["available_buttons"] = [btn.text.strip() for btn in buttons if btn.text.strip()]
            logger.debug(f"可用按钮: {other_info['available_buttons']}")
        except NoSuchElementException:
            logger.debug("未找到按钮容器")
        
        return other_info
    
    def find_order_button(self, container, button_type: str):
        """
        在订单容器中查找特定按钮
        
        Args:
            container: 订单容器元素
            button_type: 按钮类型 ('feed', 'default_settlement', 'close_confirm', 'close_cancel')
            
        Returns:
            按钮元素，如果未找到则返回None
        """
        button_selectors = {
            "feed": [".feedingback", "//uni-view[contains(text(), '结料')]"],
            "default_settlement": [".back1", "//uni-view[contains(text(), '违约结算')]"],
            "close_confirm": [".back2", "//uni-view[contains(text(), '确认')]"],
            "close_cancel": [".cancelbtn", "//uni-view[contains(text(), '取消')]"],
        }
        
        selectors = button_selectors.get(button_type, [])
        
        for selector in selectors:
            try:
                if selector.startswith("//"):
                    button = container.find_element(By.XPATH, selector)
                else:
                    button = container.find_element(By.CSS_SELECTOR, selector)
                
                if button and button.is_displayed():
                    logger.info(f"找到 {button_type} 按钮: {selector}")
                    return button
                    
            except NoSuchElementException:
                continue
            except Exception as e:
                logger.debug(f"查找 {button_type} 按钮失败: {e}")
                continue
        
        logger.warning(f"未找到 {button_type} 按钮")
        return None


def test_order_finder():
    """测试订单查找器"""
    print("=== 订单查找器测试 ===")
    
    # 这里需要实际的WebDriver实例进行测试
    # finder = SpotOrderFinder(driver)
    # result = finder.find_order_by_id("S2025053002173230988")
    # print(f"查找结果: {result}")
    
    print("测试完成")


if __name__ == "__main__":
    test_order_finder()
