#!/usr/bin/env python3
"""
违约结算平仓服务

专门处理违约结算按钮的平仓操作，确保100%能找到并点击违约结算按钮
"""

import re
import logging
import asyncio
from typing import Optional, Dict, Any, List
from datetime import datetime

logger = logging.getLogger(__name__)


class DefaultSettlementService:
    """违约结算平仓服务"""

    def __init__(self, page, timeout: int = 15):
        """
        初始化违约结算服务

        Args:
            page: Playwright页面实例
            timeout: 超时时间（秒）
        """
        self.page = page
        self.timeout = timeout

        # 违约结算按钮的多重选择器策略（确保100%找到）
        self.SETTLEMENT_SELECTORS = {
            # 主选择器（基于HTML结构）
            "primary": ".back1",

            # 带data-v属性的选择器
            "with_data_v": ".back1[data-v-14224f5e]",

            # 完整路径选择器
            "full_path": ".deposit-right .back1",

            # 文本匹配选择器
            "text_based": "//uni-view[contains(text(), '违约结算')]",

            # 类名和文本组合
            "class_text_combo": "//uni-view[contains(@class, 'back1') and contains(text(), '违约结算')]",

            # 按钮容器内的选择器
            "in_button_container": ".order-button .back1",

            # 备用文本选择器
            "fallback_text": "//uni-view[text()='违约结算']",
        }

        # 订单容器选择器
        self.ORDER_CONTAINER = ".puwidth"
        self.ORDER_ID_SELECTOR = ".order-sn"
        self.ORDER_ID_PATTERN = r'订单号[：:]\s*([A-Z0-9]+)'

        # 弹窗相关选择器
        self.POPUP_SELECTORS = {
            "container": ".popcont",
            "confirm_button": ".back2",  # 主确认按钮选择器
            "confirm_button_alt": ".back1",  # 备用确认按钮选择器（某些订单使用back1）
            "cancel_button": ".cancelbtn",
            "info_box": ".boxinfo"
        }

        # 确认按钮的多重选择器策略
        self.CONFIRM_BUTTON_SELECTORS = [
            ".back2",  # 主选择器（某些订单使用back2）
            ".back1",  # 备用选择器（某些订单使用back1）
            ".back2[data-v-14224f5e]",  # 带data-v属性的back2
            ".back1[data-v-14224f5e]",  # 带data-v属性的back1
            "//uni-button[contains(@class, 'back2')]",  # XPath选择器back2
            "//uni-button[contains(@class, 'back1')]",  # XPath选择器back1
            "//uni-button[contains(text(), '结算')]",  # 文本匹配"结算"
            "//uni-button[contains(text(), '确认')]",  # 文本匹配"确认"
        ]

    async def close_position_by_default_settlement(self, order_id: str) -> Dict[str, Any]:
        """
        通过违约结算关闭持仓

        Args:
            order_id: 订单ID

        Returns:
            操作结果
        """
        try:
            logger.info(f"开始违约结算平仓: 订单ID={order_id}")

            # 1. 导航到订单页面
            nav_result = await self._navigate_to_orders()
            if not nav_result["success"]:
                return nav_result

            # 2. 查找目标订单
            order_container = await self._find_order_container(order_id)
            if not order_container:
                return {
                    "success": False,
                    "message": f"未找到订单: {order_id}"
                }

            # 3. 查找并点击违约结算按钮
            click_result = await self._click_settlement_button(order_container, order_id)
            if not click_result["success"]:
                return click_result

            # 4. 处理确认弹窗
            confirm_result = await self._handle_confirmation_popup(order_id)

            return confirm_result

        except Exception as e:
            logger.error(f"违约结算平仓失败: {e}")
            return {
                "success": False,
                "message": f"违约结算平仓失败: {str(e)}"
            }

    async def _navigate_to_orders(self) -> Dict[str, Any]:
        """导航到订单页面"""
        try:
            orders_url = "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"

            logger.info(f"导航到订单页面: {orders_url}")
            await self.page.goto(orders_url, wait_until="domcontentloaded", timeout=30000)

            # 等待页面加载
            await asyncio.sleep(3)

            # 验证页面加载
            current_url = self.page.url
            if "myorder" in current_url:
                logger.info("✅ 成功导航到订单页面")
                return {"success": True, "message": "导航成功"}
            else:
                logger.error(f"❌ 导航失败: {current_url}")
                return {"success": False, "message": f"导航失败: {current_url}"}

        except Exception as e:
            logger.error(f"导航到订单页面失败: {e}")
            return {"success": False, "message": f"导航失败: {str(e)}"}

    async def _find_order_container(self, order_id: str):
        """查找订单容器"""
        try:
            logger.info(f"查找订单容器: {order_id}")

            # 等待订单列表加载
            await self.page.wait_for_selector(self.ORDER_CONTAINER, timeout=self.timeout * 1000)

            # 获取所有订单容器
            order_containers = await self.page.query_selector_all(self.ORDER_CONTAINER)
            logger.info(f"找到 {len(order_containers)} 个订单容器")

            # 遍历查找匹配的订单
            for i, container in enumerate(order_containers):
                try:
                    # 提取订单ID
                    order_sn_element = await container.query_selector(self.ORDER_ID_SELECTOR)
                    if order_sn_element:
                        order_text = await order_sn_element.text_content()
                        match = re.search(self.ORDER_ID_PATTERN, order_text)

                        if match and match.group(1) == order_id:
                            logger.info(f"✅ 找到匹配订单容器: {order_id} (第{i+1}个)")
                            return container

                except Exception as e:
                    logger.debug(f"处理订单容器 {i+1} 时出错: {e}")
                    continue

            logger.warning(f"❌ 未找到订单容器: {order_id}")
            return None

        except Exception as e:
            logger.error(f"查找订单容器失败: {e}")
            return None

    async def _click_settlement_button(self, order_container, order_id: str) -> Dict[str, Any]:
        """
        点击违约结算按钮（使用多重策略确保100%找到）

        Args:
            order_container: 订单容器元素
            order_id: 订单ID

        Returns:
            点击结果
        """
        try:
            logger.info(f"查找违约结算按钮: 订单 {order_id}")

            # 策略1: 主选择器
            settlement_button = await self._try_find_settlement_button(order_container, "primary")
            if settlement_button:
                return await self._execute_button_click(settlement_button, order_id, "主选择器")

            # 策略2: 带data-v属性的选择器
            settlement_button = await self._try_find_settlement_button(order_container, "with_data_v")
            if settlement_button:
                return await self._execute_button_click(settlement_button, order_id, "data-v选择器")

            # 策略3: 完整路径选择器
            settlement_button = await self._try_find_settlement_button(order_container, "full_path")
            if settlement_button:
                return await self._execute_button_click(settlement_button, order_id, "完整路径选择器")

            # 策略4: 文本匹配选择器
            settlement_button = await self._try_find_settlement_button(order_container, "text_based")
            if settlement_button:
                return await self._execute_button_click(settlement_button, order_id, "文本匹配选择器")

            # 策略5: 类名和文本组合
            settlement_button = await self._try_find_settlement_button(order_container, "class_text_combo")
            if settlement_button:
                return await self._execute_button_click(settlement_button, order_id, "类名文本组合选择器")

            # 策略6: 在按钮容器内查找
            settlement_button = await self._try_find_settlement_button(order_container, "in_button_container")
            if settlement_button:
                return await self._execute_button_click(settlement_button, order_id, "按钮容器选择器")

            # 策略7: 备用文本选择器
            settlement_button = await self._try_find_settlement_button(order_container, "fallback_text")
            if settlement_button:
                return await self._execute_button_click(settlement_button, order_id, "备用文本选择器")

            # 所有策略都失败
            logger.error(f"❌ 所有策略都无法找到违约结算按钮: 订单 {order_id}")

            # 记录调试信息
            await self._log_debug_info(order_container, order_id)

            return {
                "success": False,
                "message": f"无法找到订单 {order_id} 的违约结算按钮"
            }

        except Exception as e:
            logger.error(f"点击违约结算按钮失败: {e}")
            return {
                "success": False,
                "message": f"点击违约结算按钮失败: {str(e)}"
            }

    async def _try_find_settlement_button(self, container, strategy: str):
        """尝试使用特定策略查找违约结算按钮"""
        try:
            selector = self.SETTLEMENT_SELECTORS[strategy]

            if selector.startswith("//"):
                # XPath选择器
                button = await container.query_selector(f"xpath={selector}")
            else:
                # CSS选择器
                button = await container.query_selector(selector)

            if button and await button.is_visible():
                logger.info(f"✅ 使用 {strategy} 策略找到违约结算按钮")
                return button
            else:
                logger.debug(f"❌ {strategy} 策略未找到可见的违约结算按钮")
                return None

        except Exception as e:
            logger.debug(f"❌ {strategy} 策略查找失败: {e}")
            return None

    async def _execute_button_click(self, button, order_id: str, strategy: str) -> Dict[str, Any]:
        """执行按钮点击"""
        try:
            # 验证按钮文本
            button_text = await button.text_content()
            if "违约结算" not in button_text:
                logger.warning(f"⚠️ 按钮文本不匹配: {button_text}")
                return {
                    "success": False,
                    "message": f"按钮文本不匹配: {button_text}"
                }

            # 点击按钮
            await button.click()
            logger.info(f"✅ 成功点击违约结算按钮: 订单 {order_id} (使用{strategy})")

            # 等待页面响应
            await asyncio.sleep(2)

            return {
                "success": True,
                "message": f"成功点击违约结算按钮 (使用{strategy})",
                "strategy": strategy,
                "click_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"执行按钮点击失败: {e}")
            return {
                "success": False,
                "message": f"执行按钮点击失败: {str(e)}"
            }

    async def _handle_confirmation_popup(self, order_id: str) -> Dict[str, Any]:
        """处理确认弹窗"""
        try:
            logger.info(f"处理确认弹窗: 订单 {order_id}")

            # 等待弹窗出现
            try:
                await self.page.wait_for_selector(self.POPUP_SELECTORS["container"], timeout=10000)
                logger.info("✅ 确认弹窗已出现")
            except Exception:
                logger.warning("⚠️ 未检测到确认弹窗，可能直接完成了")
                return {
                    "success": True,
                    "message": "操作完成（未检测到确认弹窗）"
                }

            # 查找确认按钮 - 使用多重策略支持back1和back2
            confirm_button = None
            used_selector = None

            for selector in self.CONFIRM_BUTTON_SELECTORS:
                try:
                    if selector.startswith("//"):
                        # XPath选择器
                        button = await self.page.query_selector(f"xpath={selector}")
                    else:
                        # CSS选择器
                        button = await self.page.query_selector(selector)

                    if button and await button.is_visible():
                        # 验证按钮文本
                        button_text = await button.text_content()
                        if "结算" in button_text or "确认" in button_text:
                            confirm_button = button
                            used_selector = selector
                            logger.info(f"✅ 找到确认按钮: {selector} (文本: {button_text})")
                            break
                except Exception as e:
                    logger.debug(f"确认按钮选择器失败: {selector} - {e}")
                    continue

            if not confirm_button:
                logger.error("❌ 所有确认按钮选择器都失败")
                return {
                    "success": False,
                    "message": "未找到确认按钮（所有策略都失败）"
                }

            # 点击确认按钮
            await confirm_button.click()
            logger.info("✅ 已点击确认按钮")

            # 等待操作完成
            await asyncio.sleep(3)

            # 验证操作结果
            success = await self._verify_settlement_success(order_id)

            if success:
                logger.info(f"🎉 违约结算成功: 订单 {order_id}")
                return {
                    "success": True,
                    "message": "违约结算成功",
                    "order_id": order_id,
                    "settlement_time": datetime.now().isoformat()
                }
            else:
                logger.warning(f"⚠️ 违约结算结果未确认: 订单 {order_id}")
                return {
                    "success": False,
                    "message": "违约结算结果未确认"
                }

        except Exception as e:
            logger.error(f"处理确认弹窗失败: {e}")
            return {
                "success": False,
                "message": f"处理确认弹窗失败: {str(e)}"
            }

    async def _verify_settlement_success(self, order_id: str) -> bool:
        """验证违约结算是否成功"""
        try:
            # 检查是否有成功提示
            success_indicators = [
                "//uni-view[contains(text(), '成功')]",
                "//uni-view[contains(text(), '已结算')]",
                "//uni-view[contains(text(), '完成')]"
            ]

            for indicator in success_indicators:
                try:
                    element = await self.page.wait_for_selector(f"xpath={indicator}", timeout=3000)
                    if element:
                        text = await element.text_content()
                        logger.info(f"✅ 找到成功提示: {text}")
                        return True
                except Exception:
                    continue

            # 检查订单是否从列表中消失
            await asyncio.sleep(2)
            await self._navigate_to_orders()

            order_container = await self._find_order_container(order_id)
            if not order_container:
                logger.info(f"✅ 订单 {order_id} 已从列表中消失，违约结算成功")
                return True

            logger.warning(f"⚠️ 订单 {order_id} 仍在列表中")
            return False

        except Exception as e:
            logger.warning(f"验证违约结算结果失败: {e}")
            return False

    async def _log_debug_info(self, container, order_id: str):
        """记录调试信息"""
        try:
            logger.debug(f"=== 调试信息: 订单 {order_id} ===")

            # 记录容器HTML
            container_html = await container.get_attribute("outerHTML")
            logger.debug(f"容器HTML: {container_html[:500]}...")

            # 查找所有按钮
            all_buttons = await container.query_selector_all("uni-view")
            logger.debug(f"找到 {len(all_buttons)} 个uni-view元素")

            for i, button in enumerate(all_buttons):
                try:
                    text = await button.text_content()
                    class_name = await button.get_attribute("class")
                    if text and ("结算" in text or "back" in str(class_name)):
                        logger.debug(f"按钮 {i+1}: 文本='{text}', 类名='{class_name}'")
                except Exception:
                    pass

        except Exception as e:
            logger.debug(f"记录调试信息失败: {e}")

    async def check_settlement_button_availability(self, order_id: str) -> Dict[str, Any]:
        """
        检查违约结算按钮的可用性

        Args:
            order_id: 订单ID

        Returns:
            检查结果
        """
        try:
            logger.info(f"检查违约结算按钮可用性: 订单 {order_id}")

            # 导航到订单页面
            nav_result = await self._navigate_to_orders()
            if not nav_result["success"]:
                return nav_result

            # 查找订单容器
            order_container = await self._find_order_container(order_id)
            if not order_container:
                return {
                    "success": False,
                    "message": f"未找到订单: {order_id}"
                }

            # 检查各种策略
            available_strategies = []
            for strategy, selector in self.SETTLEMENT_SELECTORS.items():
                button = await self._try_find_settlement_button(order_container, strategy)
                if button:
                    available_strategies.append(strategy)

            result = {
                "success": len(available_strategies) > 0,
                "order_id": order_id,
                "available_strategies": available_strategies,
                "total_strategies": len(self.SETTLEMENT_SELECTORS),
                "check_time": datetime.now().isoformat()
            }

            if available_strategies:
                logger.info(f"✅ 违约结算按钮可用: {len(available_strategies)} 种策略")
                result["message"] = f"违约结算按钮可用 ({len(available_strategies)} 种策略)"
            else:
                logger.warning(f"❌ 违约结算按钮不可用")
                result["message"] = "违约结算按钮不可用"
                # 记录调试信息
                await self._log_debug_info(order_container, order_id)

            return result

        except Exception as e:
            logger.error(f"检查违约结算按钮可用性失败: {e}")
            return {
                "success": False,
                "message": f"检查失败: {str(e)}"
            }


# 使用示例
async def example_usage():
    """使用示例"""
    # 假设已有page实例
    # service = DefaultSettlementService(page)
    #
    # # 检查按钮可用性
    # check_result = await service.check_settlement_button_availability("S2025053002173230988")
    # print(f"按钮检查结果: {check_result}")
    #
    # # 执行违约结算
    # if check_result["success"]:
    #     settlement_result = await service.close_position_by_default_settlement("S2025053002173230988")
    #     print(f"违约结算结果: {settlement_result}")

    print("示例代码完成")


if __name__ == "__main__":
    import asyncio
    asyncio.run(example_usage())
