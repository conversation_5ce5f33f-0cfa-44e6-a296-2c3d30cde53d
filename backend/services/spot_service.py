"""
现货平台服务类

该服务类负责与现货交易平台进行交互，包括登录、下单、查询订单等操作。
"""

import os
import re
import json
import time
import uuid  # 添加uuid导入
import asyncio
import logging
import traceback
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from contextlib import asynccontextmanager
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page, Playwright
from playwright._impl._errors import TimeoutError as PlaywrightTimeoutError

# 调整导入路径
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from data_storage.db_manager import db_manager
from utils.logger import setup_logger
from utils.crypto import decrypt_password

# 设置日志
# 确保日志目录存在
os.makedirs("/gold/logs", exist_ok=True)
logger = setup_logger('spot_service', "/gold/logs/spot_service.log")

# 现货平台服务管理器
spot_service_manager = None


class SpotService:
    """现货平台服务类"""

    # 页面元素选择器常量 - 根据选择器文档更新
    # 平仓操作按钮选择器 - 使用经过100%验证的选择器
    FEEDBACK_BUTTON_SELECTOR = ".feedingback"  # 结料按钮（橙色背景 #f29100）
    DEFAULT_SETTLEMENT_BUTTON_SELECTOR = ".back1"  # 违约结算按钮（红色背景 #de1a1a）- 100%验证通过
    CLOSE_CONFIRM_BUTTON_SELECTOR = ".back2"  # 平仓确认按钮（绿色背景）
    CLOSE_CANCEL_BUTTON_SELECTOR = ".cancelbtn"  # 平仓取消按钮（灰色背景 #ccc）

    # 平仓结算按钮选择器（文档提供的详细选择器）
    SETTLEMENT_BUTTON_SELECTOR = ".settle-btn .sttlebutton[data-v-14224f5e]"  # 结算按钮容器

    # 交易页面按钮选择器
    BUY_BUTTON_SELECTOR = ".amountbox .rgbtn[data-v-0fa31b49]"  # 我要买料按钮
    SELL_BUTTON_SELECTOR = ".amountbox .lfbtn[data-v-0fa31b49]"  # 我要卖料按钮

    # 确认按钮选择器（根据背景色区分）
    CONFIRM_BUY_BUTTON_SELECTOR = "uni-button.custom-class[data-v-0fa31b49][style*='background-color: rgb(225, 0, 0)']"  # 买入确认（红色）
    CONFIRM_SELL_BUTTON_SELECTOR = "uni-button.custom-class[data-v-0fa31b49][style*='background-color: rgb(47, 201, 89)']"  # 卖出确认（绿色）

    # 价格选择器
    BUY_PRICE_SELECTOR = ".amountsetbox .greecolor[data-v-0fa31b49]"  # 回购（买入）价格（绿色）
    SELL_PRICE_SELECTOR = ".amountsetbox .redcolor[data-v-0fa31b49]"  # 销售（卖出）价格（红色）

    # 数量设置选择器
    AMOUNT_INPUT_SELECTOR = ".uni-numbox__value[data-v-2449fa78]"  # 数量输入框
    PLUS_BUTTON_SELECTOR = ".uni-numbox__plus[data-v-2449fa78]"   # 加号按钮
    MINUS_BUTTON_SELECTOR = ".uni-numbox__minus[data-v-2449fa78]" # 减号按钮

    # 订单页面选择器 - 根据选择器文档更新
    # 账户信息区域
    ACCOUNT_ID_SELECTOR = "uni-text:has-text('ID:')"  # ID显示区域
    DEPOSIT_BUTTON_SELECTOR = ".nav-box .nav-back .nav-user .auth-button[data-v-1ffc035e]"  # 退/付定金按钮（文档提供）

    # 资金信息选择器
    USED_DEPOSIT_SELECTOR = "uni-text:has-text('已用定金')"  # 已用定金
    AVAILABLE_DEPOSIT_SELECTOR = "uni-text:has-text('可用定金')"  # 可用定金
    AVAILABLE_POINTS_SELECTOR = "uni-text:has-text('可用积分')"  # 可用积分
    BORROWED_AMOUNT_SELECTOR = "uni-text:has-text('已借额度')"  # 已借额度
    BORROWABLE_AMOUNT_SELECTOR = "uni-text:has-text('可借额度')"  # 可借额度

    # 订单状态区域
    TOTAL_WEIGHT_SELECTOR = "uni-text:has-text('总持单重')"  # 总持单重
    REQUIRED_DEPOSIT_SELECTOR = "uni-text:has-text('需补定金')"  # 需补定金
    MULTI_SELECT_SELECTOR = "uni-radio:has-text('多选')"  # 多选选项
    BATCH_REFUND_SELECTOR = ".batch-into .batch-button[data-v-14224f5e]"  # 批量退款按钮（文档提供）

    # 订单列表选择器 - 根据实际页面结构更新
    ORDER_LIST_SELECTOR = "uni-view:has-text('已结清订单')"  # 订单列表区域
    ORDER_ITEM_SELECTOR = ".puwidth[data-v-14224f5e]"  # 订单项容器（修复：使用正确的data-v属性）
    ORDER_ID_SELECTOR = ".popcont .boxinfo[data-v-14224f5e]"  # 订单ID（平仓弹窗中的订单号）
    OPEN_PRICE_SELECTOR = ".per-price[data-v-14224f5e], .new-price[data-v-14224f5e]"  # 开仓价格
    CLOSE_PRICE_SELECTOR = ".color2[data-v-14224f5e]"  # 平仓价格（实时平仓价格，绿色）

    # 最大重试次数和间隔
    MAX_RETRIES = 3
    RETRY_INTERVAL = 1.0  # 秒
    SESSION_TIMEOUT = 1800  # 会话超时时间，单位为秒

    # 登录页面URL
    LOGIN_PAGE_URL = "https://j.jtd9999.vip/h5/#/pages/login/login?demp_code=944440b68743bdaaf6e4cf7b5745893e"
    # 登录成功后的主页URL
    HOME_PAGE_URL = "https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e"
    # 交易页面URL
    TRADE_PAGE_URL = "https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e"
    # 我的订单页面URL
    ORDERS_PAGE_URL = "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"

    def __init__(self):
        """初始化现货平台服务"""
        # 基本信息
        self.account_id = None
        self.account_info = None
        self.platform_url = None
        self.username = None
        self.password = None

        # Playwright相关
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None

        # 状态标志
        self.logged_in = False
        self.last_activity_time = time.time()

        # 测试模式标志
        self.test_mode = False

        # 会话锁，用于保证同一时间只有一个请求在操作浏览器
        self._session_lock = asyncio.Lock()

        # 性能优化缓存
        self._cached_selectors = {
            "buy_button": None,
            "sell_button": None,
            "confirm_button": None,
            "amount_input": None
        }
        self._is_on_trade_page = False
        self._last_page_check = 0
        self._page_check_interval = 30  # 30秒检查一次页面状态
        self._successful_selectors = {}  # 记录成功的选择器

        # 初始化时间
        self.init_time = datetime.now()
        logger.info(f"现货服务实例创建: {self.init_time}")

    async def _wait_for_element(self, selector: str, timeout: int = 10) -> Optional[Any]:
        """
        等待元素出现

        Args:
            selector: 元素选择器
            timeout: 超时时间，单位为秒

        Returns:
            元素对象，如果超时则返回None
        """
        try:
            logger.debug(f"等待元素: {selector}, 超时: {timeout}秒")
            return await self.page.wait_for_selector(selector, timeout=timeout*1000, state="visible")
        except PlaywrightTimeoutError:
            logger.warning(f"等待元素超时: {selector}, 超时: {timeout}秒")
            return None
        except Exception as e:
            logger.error(f"等待元素异常: {selector}, 错误: {e}")
            return None

    async def _find_element_optimized(self, element_type: str, selectors: list, timeout: int = 5) -> Optional[Any]:
        """
        优化的元素查找方法，使用缓存和优先级

        Args:
            element_type: 元素类型（用于缓存）
            selectors: 选择器列表
            timeout: 超时时间

        Returns:
            元素对象，如果未找到则返回None
        """
        # 首先尝试使用缓存的成功选择器
        if element_type in self._successful_selectors:
            cached_selector = self._successful_selectors[element_type]
            try:
                element = await self.page.query_selector(cached_selector)
                if element and await element.is_visible():
                    logger.debug(f"使用缓存选择器找到元素: {cached_selector}")
                    return element
            except Exception:
                # 缓存的选择器失效，清除缓存
                del self._successful_selectors[element_type]

        # 尝试所有选择器
        for selector in selectors:
            try:
                element = await self.page.query_selector(selector)
                if element and await element.is_visible():
                    # 缓存成功的选择器
                    self._successful_selectors[element_type] = selector
                    logger.debug(f"找到元素并缓存选择器: {selector}")
                    return element
            except Exception as e:
                logger.debug(f"选择器失败: {selector}, 错误: {e}")
                continue

        logger.warning(f"未找到{element_type}元素，尝试了{len(selectors)}个选择器")
        return None

    async def _get_element_text(self, selector: str, default: str = "") -> str:
        """
        获取元素文本

        Args:
            selector: 元素选择器
            default: 默认值，当元素不存在时返回

        Returns:
            元素文本，如果元素不存在则返回默认值
        """
        try:
            element = await self.page.query_selector(selector)
            if element:
                return await element.text_content() or default
            return default
        except Exception as e:
            logger.error(f"获取元素文本异常: {selector}, 错误: {e}")
            return default

    async def _find_order_item(self, order_id: str) -> Optional[Any]:
        """
        查找订单项

        Args:
            order_id: 订单ID

        Returns:
            订单项元素，如果未找到则返回None
        """
        try:
            # 获取所有订单项
            order_items = await self.page.query_selector_all(self.ORDER_ITEM_SELECTOR)

            # 逐个检查订单ID
            for item in order_items:
                id_element = await item.query_selector(self.ORDER_ID_SELECTOR)
                if id_element:
                    item_id = await id_element.text_content()
                    if item_id and order_id in item_id:
                        return item

            return None
        except Exception as e:
            logger.error(f"查找订单项异常: {order_id}, 错误: {e}")
            return None

    async def _handle_error(self, method_name: str, error: Exception) -> Dict[str, Any]:
        """处理通用错误并返回统一格式的错误响应"""
        error_msg = f"{method_name}失败: {str(error)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        return {"success": False, "message": error_msg, "order_id": None}

    async def _log_operation(self, operation: str, **kwargs):
        """记录操作日志"""
        log_data = {
            "operation": operation,
            "timestamp": datetime.now().isoformat(),
            "account_id": self.account_id,
            **kwargs
        }
        logger.info(f"操作日志: {log_data}")

    async def _ensure_on_trade_page(self) -> bool:
        """
        确保当前在交易页面，优化版本

        Returns:
            是否在交易页面
        """
        current_time = time.time()

        # 如果最近检查过且确认在交易页面，跳过检查
        if (self._is_on_trade_page and
            current_time - self._last_page_check < self._page_check_interval):
            logger.debug("使用缓存的页面状态，跳过检查")
            return True

        try:
            # 快速检查当前URL
            current_url = self.page.url
            if "bookingorder" in current_url:
                self._is_on_trade_page = True
                self._last_page_check = current_time
                logger.debug("通过URL确认在交易页面")
                return True

            # 检查是否存在交易按钮（使用缓存的选择器）
            buy_sell_selectors = [
                ".amountbox .rgbtn[data-v-0fa31b49]",  # 买入按钮
                ".amountbox .lfbtn[data-v-0fa31b49]",  # 卖出按钮
                "uni-view:has-text('我要买料')",
                "uni-view:has-text('我要卖料')"
            ]

            trade_button = await self._find_element_optimized("trade_button", buy_sell_selectors, timeout=2)
            if trade_button:
                self._is_on_trade_page = True
                self._last_page_check = current_time
                logger.debug("通过交易按钮确认在交易页面")
                return True

            # 如果不在交易页面，快速导航
            logger.info("不在交易页面，快速导航到交易页面")
            await self.page.goto(self.TRADE_PAGE_URL, wait_until="domcontentloaded", timeout=30000)
            await asyncio.sleep(1)  # 减少等待时间

            # 再次检查
            trade_button = await self._find_element_optimized("trade_button", buy_sell_selectors, timeout=3)
            if trade_button:
                self._is_on_trade_page = True
                self._last_page_check = current_time
                logger.info("导航后确认在交易页面")
                return True

            logger.warning("导航后仍未在交易页面")
            self._is_on_trade_page = False
            return False

        except Exception as e:
            logger.error(f"检查交易页面状态异常: {e}")
            self._is_on_trade_page = False
            return False

    async def initialize(self, account_id: str = None) -> bool:
        """
        初始化服务，使用硬编码配置

        Args:
            account_id: 账户ID（可选，使用默认值）

        Returns:
            初始化是否成功
        """
        # 使用固定的账户ID
        self.account_id = "SPOT337855A0"

        try:
            logger.info(f"正在初始化现货平台服务，使用硬编码配置")

            # 硬编码的账户信息
            self.account_info = {
                "id": self.account_id,
                "platform_name": "黄金交易平台",
                "platform_url": "https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e"
            }

            self.platform_url = self.account_info["platform_url"]

            # 使用硬编码的账号和密码
            self.username = "***********"
            self.password = "***********"

            logger.info(f"现货平台服务初始化成功: {self.account_info['platform_name']}")
            logger.info(f"现货平台URL: {self.platform_url}")

            # 初始化后立即登录，保持会话状态
            login_success = await self.login()
            if not login_success:
                logger.error("初始化时登录失败")
                return False

            logger.info("初始化时登录成功，会话已准备就绪")
            return True

        except Exception as e:
            logger.error(f"初始化现货平台服务异常: {e}")
            logger.error(traceback.format_exc())
            return False

    async def login(self) -> bool:
        """登录现货平台"""
        logger.info(f"开始登录现货平台: {self.platform_url}")

        # 检查是否已经有有效的页面对象并且已登录
        if self.page and not self.page.is_closed() and self.logged_in:
            logger.info("页面已存在并且已登录，验证登录状态")
            if await self._check_if_already_logged_in():
                logger.info("当前会话已登录且有效，无需重新登录")
                self.last_activity_time = time.time()
                return True
            logger.info("当前会话登录状态已失效，需要重新登录")

        browser_args = [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--disable-gpu',
            '--mute-audio',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-breakpad',
            '--disable-component-extensions-with-background-pages',
            '--disable-extensions',
            '--disable-features=TranslateUI,BlinkGenPropertyTrees',
            '--disable-ipc-flooding-protection',
            '--disable-renderer-backgrounding',
            '--enable-automation',
            '--hide-scrollbars',
            '--metrics-recording-only',
            '--window-size=1200,900'
        ]

        try:
            # 如果页面或浏览器不存在或已关闭，需要重新创建
            need_new_browser = self.browser is None or self.context is None or self.page is None
            need_new_browser = need_new_browser or (self.browser and self.browser.is_connected() == False)
            need_new_browser = need_new_browser or (self.page and self.page.is_closed())

            # 如果需要新的浏览器实例
            if need_new_browser:
                logger.info("创建新的浏览器实例和页面")
                # 先清理现有资源
                await self._cleanup_browser_quiet()

                # 创建新的浏览器实例
                playwright_instance = await async_playwright().start()
                self.playwright = playwright_instance

                # 智能检测Chrome浏览器路径
                chrome_paths = [
                    "/opt/google/chrome/chrome",
                    "/usr/bin/google-chrome",
                    "/usr/bin/google-chrome-stable",
                    "/usr/bin/chromium-browser",
                    "/usr/bin/chromium",
                    "/snap/bin/chromium"
                ]

                chrome_executable = None
                for path in chrome_paths:
                    if os.path.exists(path):
                        chrome_executable = path
                        logger.info(f"找到Chrome浏览器: {path}")
                        break

                if chrome_executable:
                    browser = await playwright_instance.chromium.launch(
                        headless=True,
                        executable_path=chrome_executable,
                        args=browser_args
                    )
                else:
                    # 使用Playwright自带的Chromium
                    logger.warning("未找到系统Chrome，使用Playwright自带的Chromium")
                    browser = await playwright_instance.chromium.launch(
                        headless=True,
                        args=browser_args
                    )
                context = await browser.new_context(
                    viewport={'width': 1200, 'height': 900},
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                )
                self.browser = browser
                self.context = context
                self.page = await context.new_page()

                # 设置长一点的超时时间
                self.page.set_default_navigation_timeout(90000)  # 90秒
                self.page.set_default_timeout(30000)



            # 优化：直接导航到交易页面，如果需要登录会自动跳转到登录页
            target_url = self.TRADE_PAGE_URL
            logger.info(f"直接导航到交易页面: {target_url}")

            # 设置更长的导航超时时间，确保页面能够完全加载
            try:
                await self.page.goto(target_url, wait_until="domcontentloaded", timeout=60000)
                logger.info(f"页面加载完成: {self.page.url}")
            except Exception as e:
                logger.error(f"页面加载失败: {e}")
                logger.info("尝试继续操作...")

            # 等待页面稳定
            await asyncio.sleep(3)

            # 检查是否处于交易页面 - 通过判断是否存在买卖按钮
            logger.info("检查是否已在交易页面...")
            buy_sell_selectors = [
                ".amountbox .rgbtn[data-v-0fa31b49]", # 买入按钮
                ".amountbox .lfbtn[data-v-0fa31b49]", # 卖出按钮
                "uni-view:has-text('我要买料')",
                "uni-view:has-text('我要卖料')"
            ]

            is_on_trade_page = False
            for selector in buy_sell_selectors:
                button = await self.page.query_selector(selector)
                if button and await button.is_visible():
                    logger.info(f"检测到交易按钮: {selector}，确认已在交易页面")
                    is_on_trade_page = True
                    break

            # 如果不在交易页面，检查是否在登录页面
            if not is_on_trade_page:
                logger.info("未检测到交易按钮，检查是否需要登录...")

                # 判断是否在登录页
                current_url = self.page.url
                login_indicators = [
                    "/pages/login/login" in current_url,
                    "login" in current_url.lower()
                ]

                # 检查登录表单元素
                login_form_selectors = [
                    "input[placeholder*='账号']",
                    "input[placeholder*='用户名']",
                    "input[placeholder*='手机']",
                    ".tell-rule .uni-form-item .uni-input-input",
                    "input[type='password']",
                    "input[placeholder*='密码']",
                    "button:has-text('登录')",
                    "uni-button:has-text('登录')"
                ]

                is_on_login_page = any(login_indicators)
                for selector in login_form_selectors:
                    element = await self.page.query_selector(selector)
                    if element and await element.is_visible():
                        is_on_login_page = True
                        logger.info(f"检测到登录表单元素: {selector}，确认在登录页面")
                        break

                if is_on_login_page:
                    logger.info("已在登录页面，开始执行登录...")

                    # 输入用户名/手机号
                    username_selectors = [
                        "input[placeholder*='账号']",
                        "input[placeholder*='用户名']",
                        "input[placeholder*='手机']",
                        ".tell-rule .uni-form-item .uni-input-input",
                        "[type='text']"
                    ]

                    username_input = None
                    for selector in username_selectors:
                        element = await self.page.query_selector(selector)
                        if element and await element.is_visible():
                            username_input = element
                            logger.info(f"找到用户名输入框: {selector}")
                            break

                    if not username_input:
                        logger.error("未找到用户名输入框")
                        return False

                    # 固定使用硬编码账号
                    hardcoded_username = "***********"

                    # 先清空输入框
                    await username_input.fill("")
                    await username_input.type(hardcoded_username, delay=100)
                    logger.info(f"已输入硬编码用户名: {hardcoded_username}")

                    # 输入密码
                    password_selectors = [
                        "input[type='password']",
                        "input[placeholder*='密码']",
                        ".password-rule .uni-form-item .uni-input-input",
                        "[type='password']"
                    ]

                    password_input = None
                    for selector in password_selectors:
                        element = await self.page.query_selector(selector)
                        if element and await element.is_visible():
                            password_input = element
                            logger.info(f"找到密码输入框: {selector}")
                            break

                    if not password_input:
                        logger.error("未找到密码输入框")
                        return False

                    # 固定使用硬编码密码
                    hardcoded_password = "***********"

                    # 先清空输入框
                    await password_input.fill("")
                    await password_input.type(hardcoded_password, delay=100)
                    logger.info("已输入硬编码密码")

                    # 检查是否存在验证码输入框
                    captcha_selectors = [
                        "input[placeholder*='验证码']",
                        "input[name*='captcha']",
                        "input[class*='captcha']",
                        "input[class*='verify']"
                    ]

                    # 尝试查找验证码输入框
                    for selector in captcha_selectors:
                        captcha_input = await self.page.query_selector(selector)
                        if captcha_input and await captcha_input.is_visible():
                            logger.info(f"检测到验证码输入框: {selector}")
                            # 尝试查找验证码图片
                            captcha_img_selectors = [
                                "img[src*='captcha']",
                                "img[src*='verify']",
                                "img[alt*='验证码']",
                                "img[class*='captcha']",
                                "canvas[class*='captcha']"
                            ]

                            captcha_img = None
                            for img_selector in captcha_img_selectors:
                                img = await self.page.query_selector(img_selector)
                                if img and await img.is_visible():
                                    captcha_img = img
                                    logger.info(f"找到验证码图片: {img_selector}")
                                    break

                            if captcha_img:
                                logger.warning("检测到需要验证码，但自动识别超出当前功能范围")
                                # 暂时使用默认验证码
                                await captcha_input.fill("1234")
                                logger.info("已输入默认验证码: 1234")
                            break

                    # 点击登录按钮
                    login_button_selectors = [
                        "uni-button[data-v-06e88858]",
                        ".login-reg .login[data-v-06e88858]",
                        ".login-reg .uni-btn[data-v-06e88858]",
                        "uni-button[style*='background-color: #de1a1a']",
                        "uni-button.login",
                        "button:has-text('登录')",
                        "uni-button:has-text('登录')",
                        "[type='submit']",
                        ".login-btn"
                    ]

                    login_button = None
                    for selector in login_button_selectors:
                        element = await self.page.query_selector(selector)
                        if element and await element.is_visible():
                            login_button = element
                            logger.info(f"找到登录按钮: {selector}")
                            break

                    if not login_button:
                        logger.error("未找到登录按钮")
                        return False

                    # 点击登录按钮
                    logger.info("尝试点击登录按钮")
                    for attempt in range(1, 4):  # 最多尝试3次
                        try:
                            await login_button.click()
                            logger.info(f"已点击登录按钮 (尝试 #{attempt})")
                            break
                        except Exception as e:
                            logger.warning(f"点击登录按钮失败 (尝试 #{attempt}): {e}")
                            if attempt < 3:  # 如果不是最后一次尝试，等待后重试
                                await asyncio.sleep(1)
                            else:
                                logger.error("所有点击尝试都失败")
                                return False

                    # 登录后等待页面加载和响应
                    logger.info("登录后等待页面加载和响应...")
                    await asyncio.sleep(10)  # 等待足够长的时间确保页面加载完成

                    # 直接导航到交易页面检查登录状态
                    logger.info("直接导航到交易页面检查登录状态...")
                    await self.page.goto(self.TRADE_PAGE_URL, wait_until="domcontentloaded", timeout=60000)
                    await asyncio.sleep(5)  # 等待页面加载

                    # 验证是否登录成功 - 检查URL和页面元素
                    if "/login/" not in self.page.url.lower():
                        logger.info(f"导航后URL不包含登录页路径: {self.page.url}")

                        # 检查是否存在交易页面元素
                        for selector in buy_sell_selectors:
                            button = await self.page.query_selector(selector)
                            if button and await button.is_visible():
                                logger.info(f"找到交易页面元素: {selector}")
                                logger.info("导航验证通过，登录成功")
                                self.logged_in = True
                                self.last_activity_time = time.time()

                                # 更新账户连接状态
                                await self._update_account_connection_status(True)

                                return True

                    logger.error("登录失败，未能导航到交易页面或未找到交易元素")
                    return False
                else:
                    logger.warning("未在登录页面，但也未在交易页面，尝试直接导航到登录页")
                    await self.page.goto(self.LOGIN_PAGE_URL, wait_until="domcontentloaded")
                    await asyncio.sleep(3)
                    # 递归调用自身重试登录流程
                    return await self.login()
            else:
                # 已在交易页面，说明已登录
                logger.info("已在交易页面，无需登录")
                self.logged_in = True
                self.last_activity_time = time.time()

                # 更新账户连接状态
                await self._update_account_connection_status(True)

                return True

        except Exception as e:
            logger.error(f"登录过程中发生异常: {e}")
            logger.error(traceback.format_exc())
            return False

    async def _cleanup_browser_quiet(self):
        """关闭和清理浏览器资源，但不报告错误"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
        except Exception as e:
            # 只记录但不抛出异常
            logger.debug(f"清理浏览器资源时发生异常: {e}")
        finally:
            # 无论成功与否，都重置这些属性
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None
            self.logged_in = False

    async def _check_if_already_logged_in(self) -> bool:
        """检查是否已经登录到现货平台"""
        logger.info("检查是否已经登录")
        try:
            # 1. 最精确的验证方式：检查当前URL是否匹配登录成功后的主页URL
            current_url = self.page.url
            expected_home_url = "https://j.jtd9999.vip/h5/#/?demp_code="

            # 在URL中搜索关键部分，忽略可能的额外参数
            if expected_home_url in current_url:
                logger.info(f"当前URL ({current_url}) 匹配登录成功后的主页URL，确认已登录")
                return True

            # 2. 备用方法：检查平台LOGO是否存在（次可靠的方法）
            # 根据文档提供的LOGO选择器
            logo_selector = "uni-image[style*='background-image: url(https://img.em9999.com/images/20250113/IMG20250113302065.jpg)'], div[style*='background-image: url(https://img.em9999.com/images/20250113/IMG20250113302065.jpg)']"

            # 尝试其他可能的LOGO选择器
            other_logo_selectors = [
                "[style*='background-image: url(']",
                "img[src*='em9999.com']",
                ".logo",
                "#logo",
                "[class*='logo']"
            ]

            # 先尝试精确的LOGO选择器
            logo_element = await self.page.query_selector(logo_selector)
            if logo_element:
                logger.info("检测到平台LOGO，可能已登录")
                # 但URL不匹配主页，可能是其他页面
                logger.warning(f"检测到LOGO但URL不匹配主页: {current_url}")
                return False # URL不对就不算真正登录成功

            # 如果当前URL是登录页，则明确未登录
            if "/pages/login/login" in current_url:
                logger.info("当前URL是登录页面，确认未登录")
                return False

            logger.info("无法确定登录状态，假设未登录")
            return False

        except Exception as e:
            logger.error(f"检查登录状态时出错: {e}")
            return False

    async def logout(self) -> bool:
        """
        登出现货平台

        Returns:
            是否成功
        """
        async with self._session_lock:
            try:
                # 清理浏览器资源
                await self._cleanup_browser_quiet()

                # 更新登录状态
                self.logged_in = False
                self.last_activity_time = None

                # 更新账户连接状态
                await self._update_account_connection_status(False)

                logger.info(f"登出成功: {self.account_info['username'] if self.account_info else 'unknown'}")
                return True
            except Exception as e:
                logger.error(f"登出过程中发生错误: {e}")
                return False

    async def _update_account_connection_status(self, is_connected: bool):
        """
        更新账户连接状态（跳过数据库更新，使用硬编码配置）

        Args:
            is_connected: 是否已连接
        """
        try:
            # 跳过数据库更新，使用硬编码配置
            status = "connected" if is_connected else "disconnected"
            logger.info(f"账户连接状态: {status} (硬编码配置，跳过数据库更新)")
        except Exception as e:
            logger.error(f"更新账户连接状态失败: {e}")

    async def test_connection_simple(self) -> bool:
        """
        简单测试连接，不启动浏览器

        Returns:
            是否成功
        """
        try:
            # 确保账户信息已加载
            if not self.account_info:
                logger.error("账户信息未加载，请先调用initialize方法")
                return False

            logger.info(f"简单测试连接成功: {self.account_info['username']}")

            # 更新账户连接状态
            await self._update_account_connection_status(True)

            return True
        except Exception as e:
            logger.error(f"简单测试连接失败: {e}")
            return False

    async def _navigate_to_orders_page(self) -> bool:
        """
        导航到我的订单页面 - 使用硬编码URL直接进入

        Returns:
            是否成功导航到订单页面
        """
        try:
            # 确保已登录
            if not self.page or self.page.is_closed() or not self.logged_in:
                logger.warning("页面对象无效、已关闭或未登录，尝试重新登录以导航到订单页面。")
                login_success = await self.login()
                if not login_success:
                    logger.error("登录失败，无法导航到订单页面")
                    return False

            # 订单页面特有的关键元素选择器
            orders_page_critical_element_selector = "uni-view.order-item, .order-list"

            # 直接使用硬编码的订单页URL
            orders_url = "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"
            logger.info(f"直接导航到订单页面: {orders_url}")

            try:
                # 使用更可靠的导航方式
                await self.page.goto(orders_url, wait_until="domcontentloaded", timeout=45000)
                logger.info(f"页面加载完成: {self.page.url}")
                await asyncio.sleep(3)  # 等待页面完全加载


            except Exception as e_goto:
                logger.error(f"导航到订单页面失败: {e_goto}")
                return False

            # 检查是否被重定向回登录页
            if await self._check_if_on_login_page_instead():
                logger.error("导航后停留在登录页，尝试重新登录")
                # 尝试重新登录
                if not await self.login():
                    logger.error("重新登录失败")
                return False

                # 登录成功后直接使用硬编码URL导航一次
                orders_url = "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"
                await self.page.goto(orders_url, wait_until="domcontentloaded", timeout=45000)
                await asyncio.sleep(3)

                # 再次检查
                if await self._check_if_on_login_page_instead():
                    logger.error("二次导航后仍在登录页，无法访问订单页面")
                    return False

            # 验证关键元素是否存在且可见
            logger.info(f"等待订单页面关键元素 ('{orders_page_critical_element_selector}') 加载... Timeout: 15秒")
            critical_element = await self._wait_for_element(orders_page_critical_element_selector, timeout=15)

            if critical_element:
                logger.info("成功导航到订单页面")
                return True
            else:
                logger.warning("导航到订单页面，但未找到关键元素")
                return False

        except Exception as e:
            logger.error(f"导航到订单页面异常: {e}")
            logger.error(traceback.format_exc())
            return False

    async def _check_if_on_login_page_instead(self) -> bool:
        """检查当前页面是否意外地停留在登录页。"""
        try:
            if not self.page or self.page.is_closed():
                logger.warning("页面已关闭，无法检查是否在登录页。")
                return False # 或者 True，取决于如何处理这种情况

            current_url = self.page.url
            if "login/login" in current_url:
                logger.warning(f"当前URL ({current_url}) 表明仍在登录页。")
                # 尝试查找登录页的特征元素以进一步确认
                username_input_selectors = [
                    "input[placeholder*='账号']",
                    ".tell-rule .uni-form-item .uni-input-input" # 之前登录逻辑用到的
                ]
                for selector in username_input_selectors:
                    element = await self.page.query_selector(selector)
                    if element and await element.is_visible():
                        logger.warning(f"在当前页面找到登录页特征元素 ('{selector}')，确认停留在登录页。")
                        return True
                logger.debug("当前URL包含login，但未找到明确的登录页特征元素。")
                return False # 如果URL包含login但找不到特征元素，可能只是URL相似
            return False
        except Exception as e:
            logger.error(f"检查是否在登录页时发生异常: {e}")
            return False # 出错时保守假设不在登录页

    async def _navigate_to_trade_page(self) -> bool:
        """
        导航到交易页面并验证

        Returns:
            是否成功导航到交易页面并验证通过
        """
        logger.info("开始导航到交易页面并进行验证...")

        # 确保登录状态有效
        if not self.page or self.page.is_closed():
            logger.warning("页面对象无效或已关闭，尝试重新登录。")
            login_success = await self.login()
            if not login_success:
                logger.error("登录失败，无法导航到交易页面。")
                return False

        # 买卖按钮选择器，用于验证是否在交易页面
        buy_sell_selectors = [
            ".amountbox .rgbtn[data-v-0fa31b49]", # 买入按钮(文档中提供)
            ".amountbox .lfbtn[data-v-0fa31b49]", # 卖出按钮(文档中提供)
            "uni-view:has-text('我要买料')",
            "uni-view:has-text('我要卖料')"
        ]

        # 首先检查当前是否已在交易页面
        logger.info("检查当前是否已在交易页面...")
        already_on_trade_page = False
        for selector in buy_sell_selectors:
            button = await self.page.query_selector(selector)
            if button and await button.is_visible():
                logger.info(f"检测到交易按钮: {selector}，确认已在交易页面")
                already_on_trade_page = True
                break

        if already_on_trade_page:
            logger.info("已在交易页面，无需导航")
            return True

        # 直接使用硬编码URL导航到交易页面，不点击元素
        logger.info("直接使用硬编码URL导航到交易页面")
        trade_url = "https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e"

        try:
            logger.info(f"直接导航到交易页URL: {trade_url}")
            await self.page.goto(trade_url, wait_until="domcontentloaded", timeout=45000)
            logger.info(f"页面加载完成: {self.page.url}")
            await asyncio.sleep(3)  # 等待页面加载
        except Exception as e_goto:
            logger.error(f"直接导航到交易页面失败: {e_goto}")
            return False

        # 检查是否需要登录
        current_url = self.page.url
        if "/pages/login/login" in current_url or "login" in current_url.lower():
            logger.info("导航后发现需要登录，尝试登录...")
            login_success = await self.login()
            if not login_success:
                logger.error("登录失败，无法访问交易页面")
                return False

            # 登录成功，应该已在交易页面(login方法会导航到交易页)

        # 最终验证是否在交易页面
        logger.info("验证是否成功到达交易页面...")
        on_trade_page = False
        for selector in buy_sell_selectors:
            button = await self.page.query_selector(selector)
            if button and await button.is_visible():
                logger.info(f"导航后成功到达交易页面，检测到交易按钮: {selector}")
                on_trade_page = True
                break

        if on_trade_page:
            return True
        else:
            logger.error("导航失败，未能到达交易页面或未检测到交易按钮")
            return False

    async def _get_latest_order(self) -> Optional[Dict[str, Any]]:
        """
        获取最新订单信息

        Returns:
            最新订单信息
        """
        try:
            # 等待订单列表加载
            logger.info("等待订单列表加载")
            await asyncio.sleep(2)

            # 尝试多种选择器查找订单项 - 修复：使用正确的data-v属性
            order_selectors = [
                ".puwidth[data-v-14224f5e]",  # 修复：使用正确的data-v属性
                ".deposit-bottom[data-v-14224f5e]",  # 订单容器（修复）
                ".puwidth[data-v-9a81d21c]",  # 备用：旧的data-v属性
                ".deposit-bottom[data-v-9a81d21c]",  # 备用：旧的订单容器
                "uni-view.order-item",  # 通用订单项
                ".order-item",  # 简化版本
                "[class*='order']",  # 包含order的class
                ".puwidth"  # 不带data-v属性的版本
            ]

            order_items = []
            for selector in order_selectors:
                try:
                    items = await self.page.query_selector_all(selector)
                    if items and len(items) > 0:
                        logger.info(f"使用选择器 '{selector}' 找到 {len(items)} 个订单项")
                        order_items = items
                        break
                except Exception as e:
                    logger.debug(f"选择器 '{selector}' 查找失败: {e}")
                    continue

            if not order_items or len(order_items) == 0:
                logger.warning("未找到任何订单项")
                # 尝试获取页面内容进行调试
                try:
                    page_content = await self.page.evaluate("() => document.body.innerHTML")
                    logger.debug(f"页面内容片段: {page_content[:500]}...")
                except:
                    pass
                return None

            # 获取第一个（最新的）订单
            latest_item = order_items[0]

            # 尝试从订单项中提取信息
            order_info = await self._extract_order_info_from_element(latest_item)

            if order_info:
                logger.info(f"获取到最新订单: ID={order_info.get('id')}, 状态={order_info.get('status')}, 价格={order_info.get('price')}, 数量={order_info.get('amount')}")
                return order_info
            else:
                logger.warning("无法从订单元素中提取信息")
                return None

        except Exception as e:
            logger.error(f"获取最新订单信息异常: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _extract_order_info_from_element(self, element) -> Optional[Dict[str, Any]]:
        """
        从订单元素中提取订单信息

        Args:
            element: 订单元素

        Returns:
            订单信息字典
        """
        try:
            # 获取元素的文本内容
            element_text = await element.text_content()
            logger.debug(f"订单元素文本内容: {element_text}")

            # 使用正则表达式提取订单ID
            import re
            order_id_match = re.search(r'订单号[：:]\s*([A-Za-z0-9]+)', element_text)
            if order_id_match:
                order_id = order_id_match.group(1)
            else:
                # 尝试提取任何看起来像订单ID的字符串
                id_match = re.search(r'([A-Za-z0-9]{10,})', element_text)
                order_id = id_match.group(1) if id_match else f"EXTRACTED_{int(time.time())}"

            # 提取价格信息
            price_match = re.search(r'(\d+\.?\d*)', element_text)
            price = float(price_match.group(1)) if price_match else 0.0

            # 提取数量信息（通常以克为单位）
            amount_match = re.search(r'(\d+)g', element_text)
            amount = float(amount_match.group(1)) if amount_match else 0.0

            # 提取状态信息
            status = "已成交"  # 默认状态
            if "待" in element_text:
                status = "待处理"
            elif "已" in element_text:
                status = "已完成"

            return {
                "id": order_id,
                "status": status,
                "price": price,
                "amount": amount,
                "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            logger.error(f"从订单元素提取信息失败: {e}")
            return None

    def _extract_number(self, text: str) -> float:
        """
        从文本中提取数字

        Args:
            text: 包含数字的文本

        Returns:
            提取的数字，如果未找到则返回0
        """
        if not text:
            return 0

        # 使用正则表达式提取数字
        import re
        numbers = re.findall(r'\d+\.?\d*', text)
        if numbers:
            return float(numbers[0])
        return 0

    async def _place_buy_order(self, price: float, amount: float) -> Dict[str, Any]:
        """
        下买单

        Args:
            price: 价格
            amount: 数量（克）

        Returns:
            下单结果
        """
        logger.info(f"下买单: 价格={price}, 数量={amount}")

        try:
            # 根据文档和实际情况优化的买入按钮选择器列表
            # 1. 文档中明确的 '我要买料' 选择器 (假设 rgbtn 代表买料)
            primary_buy_selector_doc = ".amountbox .rgbtn[data-v-0fa31b49]"
            # 2. Playwright CSS 扩展，更精确地匹配文本内容
            text_based_buy_selector_precise = "uni-view.rgbtn:has-text('我要买料')" # 更精确
            text_based_buy_selector_general = "uni-view:has-text('我要买料')"
            # 3. 备用 XPath，更灵活
            xpath_buy_selector_class_and_text = "//uni-view[contains(@class, 'rgbtn') and (contains(., '我要买料') or .='我要买料')]"
            xpath_buy_selector_text_only = "//uni-view[(contains(., '我要买料') or .='我要买料')]"
            # 4. 仅基于 class (如果data-v属性变化)
            class_buy_selector = ".amountbox .rgbtn"

            buy_button_selectors = [
                primary_buy_selector_doc,
                text_based_buy_selector_precise,
                text_based_buy_selector_general,
                xpath_buy_selector_class_and_text,
                xpath_buy_selector_text_only,
                class_buy_selector,
                "//uni-view[contains(@class, 'btnbox') and (contains(., '我要买料') or .='我要买料')]", # 旧有 XPath
                "//uni-view[contains(text(), '买') or contains(text(), '购买')]" # 非常通用的备用
            ]

            # 使用优化的元素查找方法
            buy_button = await self._find_element_optimized("buy_button", buy_button_selectors, timeout=3)
            idx = 0  # 用于日志记录

            if not buy_button:
                logger.error(f"所有选择器都未能找到可用的买入按钮")
                return {
                    "success": False,
                    "message": "未找到可用的买入按钮",
                    "order_id": None
                }

            logger.info(f"最终选择的买入按钮选择器: '{buy_button_selectors[idx]}'")
            await buy_button.click()
            logger.info("已点击买入按钮")

            confirm_result = await self._handle_order_confirmation(1)
            return confirm_result
        except Exception as e:
            return self._handle_error("下买单", e)

    async def _place_sell_order(self, price: float, amount: float) -> Dict[str, Any]:
        """
        下卖单

        Args:
            price: 价格
            amount: 数量（克）

        Returns:
            下单结果
        """
        logger.info(f"下卖单: 价格={price}, 数量={amount}")

        try:
            # 根据文档和实际情况优化的卖出按钮选择器列表
            # 1. 文档中明确的 '我要卖料' 选择器
            primary_sell_selector_doc = ".amountbox .lfbtn[data-v-0fa31b49]"
            # 2. Playwright CSS 扩展，更精确地匹配文本内容
            text_based_sell_selector_precise = "uni-view.lfbtn:has-text('我要卖料')" # 更精确
            text_based_sell_selector_general = "uni-view:has-text('我要卖料')"
            # 3. 备用 XPath，更灵活
            xpath_sell_selector_class_and_text = "//uni-view[contains(@class, 'lfbtn') and (contains(., '我要卖料') or .='我要卖料')]"
            xpath_sell_selector_text_only = "//uni-view[(contains(., '我要卖料') or .='我要卖料')]"
            # 4. 仅基于 class (如果data-v属性变化)
            class_sell_selector = ".amountbox .lfbtn"


            sell_button_selectors = [
                primary_sell_selector_doc,
                text_based_sell_selector_precise,
                text_based_sell_selector_general,
                xpath_sell_selector_class_and_text,
                xpath_sell_selector_text_only,
                class_sell_selector,
                "//uni-view[contains(@class, 'btnbox') and (contains(., '我要卖料') or .='我要卖料')]", # 旧有 XPath
                "//uni-view[contains(text(), '卖') or contains(text(), '出售')]" # 非常通用的备用
            ]

            # 使用优化的元素查找方法
            sell_button = await self._find_element_optimized("sell_button", sell_button_selectors, timeout=3)
            idx = 0  # 用于日志记录

            if not sell_button:
                logger.error(f"所有选择器都未能找到可用的卖出按钮")
                return {
                    "success": False,
                    "message": "未找到可用的卖出按钮",
                    "order_id": None
                }

            logger.info(f"最终选择的卖出按钮选择器: '{sell_button_selectors[idx]}'")
            await sell_button.click()
            logger.info("已点击卖出按钮")

            confirm_result = await self._handle_order_confirmation(-1)
            return confirm_result
        except Exception as e:
            return self._handle_error("下卖单", e)

    async def _handle_order_confirmation(self, direction: int) -> Dict[str, Any]:
        """
        处理下单确认弹窗

        Args:
            direction: 交易方向，1为买入，-1为卖出

        Returns:
            处理结果
        """
        try:
            action_text = "买入" if direction == 1 else "卖出"
            logger.info(f"处理{action_text}确认弹窗")

            await asyncio.sleep(0.5) # 等待弹窗加载

            # 根据选择器文档优化的确认按钮选择器
            # 买入确认按钮背景色为红色 (rgb(225, 0, 0))
            # 卖出确认按钮背景色为绿色 (rgb(47, 201, 89))

            if direction == 1:  # 买入
                primary_confirm_selector = self.CONFIRM_BUY_BUTTON_SELECTOR
                backup_color = "225, 0, 0"
            else:  # 卖出
                primary_confirm_selector = self.CONFIRM_SELL_BUTTON_SELECTOR
                backup_color = "47, 201, 89"

            confirm_button_selectors = [
                primary_confirm_selector,
                # 备用选择器：不带data-v属性，仅颜色和class
                f"uni-button.custom-class[style*=\"background-color: rgb({backup_color})\"]",
                # 更通用的选择器
                f"uni-button[style*=\"background-color: rgb({backup_color})\"]",
                # 最通用的文本选择器
                "uni-button:has-text('确认')",
                "//uni-button[contains(text(), '确认')]"
            ]

            # 使用优化的元素查找方法
            confirm_button = await self._find_element_optimized(f"confirm_button_{direction}", confirm_button_selectors, timeout=3)
            selected_selector_info = f"缓存的确认按钮选择器"

            if confirm_button:
                logger.info(f"最终选择的{action_text}确认按钮: {selected_selector_info}")
                await confirm_button.click()
                logger.info(f"已点击{action_text}确认按钮")
                await asyncio.sleep(1) # 等待操作完成

                # 尝试提取订单ID
                order_id = await self._extract_order_id_from_confirmation_or_list()

                return {
                    "success": True,
                    "message": f"{action_text}订单已提交",
                    "order_id": order_id
                }
            else:
                logger.error(f"所有选择器都未能找到可用的{action_text}确认按钮")
                return {
                    "success": False,
                    "message": f"未能找到{action_text}确认按钮",
                    "order_id": None
                }

        except Exception as e:
            logger.error(f"处理{action_text}确认弹窗异常: {e}")
            return {
                "success": False,
                "message": f"处理{action_text}确认弹窗失败: {str(e)}",
                "order_id": None
            }

    async def _extract_order_id_from_confirmation_or_list(self) -> Optional[str]:
        """尝试从确认弹窗消息或订单列表提取订单ID。"""
        logger.info("尝试提取订单ID...")

        # 优先尝试从可能的弹窗成功消息中提取 (如果平台会显示订单号)
        # 例如： "订单提交成功，订单号：XXXXX"
        # 这里假设弹窗消息的选择器 (需要根据实际情况调整或添加)
        possible_success_message_selectors = [
            ".uni-toast__content", # 通用toast提示
            "uni-view[class*='message']",
            "div[class*='success']"
        ]
        for msg_selector in possible_success_message_selectors:
            try:
                msg_element = await self.page.query_selector(msg_selector)
                if msg_element:
                    msg_text = await msg_element.text_content()
                    if msg_text:
                        logger.debug(f"检测到消息文本: {msg_text}")
                        # 常见的订单号格式 (字母和数字组合，长度通常大于8)
                        match = re.search(r'(订单(号|ID)?[:：\s]*)?([A-Za-z0-9]{8,})\b', msg_text, re.IGNORECASE)
                        if match:
                            order_id = match.group(3)
                            if len(order_id) > 5: # 简单校验长度，避免匹配到无关数字
                                logger.info(f"从确认消息 ('{msg_selector}') 中提取到订单ID: {order_id}")
                            return order_id
            except Exception as e_msg_extract:
                                logger.debug(f"从消息选择器 '{msg_selector}' 提取订单ID失败: {e_msg_extract}")
                                continue

        # 尝试导航到订单页面获取最新订单ID
        logger.info("未能从确认消息中提取订单ID，尝试导航到订单页面获取最新订单。")

        try:
            # 导航到订单页面
            nav_success = await self._navigate_to_orders_page()
            if nav_success:
                # 获取最新订单信息
                latest_order = await self._get_latest_order()
                if latest_order and latest_order.get("id"):
                    order_id = latest_order["id"]
                    logger.info(f"从订单页面获取到最新订单ID: {order_id}")
                    return order_id
                else:
                    logger.warning("导航到订单页面成功，但未能获取到最新订单信息")
            else:
                logger.warning("导航到订单页面失败")
        except Exception as e:
            logger.error(f"导航到订单页面获取订单ID失败: {e}")

        # 如果都失败，生成一个临时ID
        temp_id = f"TEMP_SPOT_{int(time.time())}_{uuid.uuid4().hex[:6]}"
        logger.warning(f"无法从任何来源确认订单ID，生成临时ID: {temp_id}")
        return temp_id

    async def place_order(self, direction: int, price: float, amount: float, test_mode: bool = False, max_slippage: float = 0.2) -> Dict[str, Any]:
        """
        下单

        Args:
            direction: 交易方向，1为买入，-1为卖出
            price: 价格
            amount: 数量（克）
            test_mode: 测试模式，为True时不会实际点击确认按钮，已弃用，保留参数兼容性
            max_slippage: 最大可接受价格滑点，默认0.2元

        Returns:
            订单信息
        """
        # 使用会话锁确保同一时间只有一个交易操作
        async with self._session_lock:
            order_start_time = time.time()
            logger.info(f"开始下单: 方向={'买入' if direction == 1 else '卖出'}, 价格={price}, 数量={amount}克, 最大可接受滑点={max_slippage}")

            # 始终使用实际执行模式，忽略test_mode参数
            self.test_mode = False

            try:
                # 确保账户信息已加载
                if not self.account_info:
                    error_msg = "账户信息未加载，请先调用initialize方法"
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "message": error_msg,
                        "order_id": None
                    }

                # 检查交易方向
                if direction not in [1, -1]:
                    error_msg = f"不支持的交易方向: {direction}"
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "message": error_msg,
                        "order_id": None
                    }

                # 确保已登录
                if not self.logged_in or not self.page:
                    logger.warning("未登录或会话已过期，尝试重新登录")
                    login_success = await self.login()
                    if not login_success:
                        error_msg = "登录失败，无法下单"
                        logger.error(error_msg)
                        return {
                            "success": False,
                            "message": error_msg,
                            "order_id": None
                        }

                # 确保在交易页面（使用优化的方法）
                logger.info("确保在交易页面")
                trade_page_success = await self._ensure_on_trade_page()
                if not trade_page_success:
                    error_msg = "无法进入交易页面"
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "message": error_msg,
                        "order_id": None
                    }

                # 获取当前页面显示的实时价格，并使用实时价格进行交易
                try:
                    current_price = await self._get_current_price(direction)

                    if current_price:
                        # 使用当前实时价格替换传入的历史价格
                        logger.info(f"获取到当前实时价格: {current_price}, 传入的历史价格: {price}")

                        # 计算价格差异（仅用于日志记录）
                        price_diff = abs(current_price - price)
                        logger.info(f"实时价格与历史价格差异: {price_diff}")

                        # 使用实时价格进行交易，不再检查滑点
                        price = current_price
                        logger.info(f"使用实时价格进行交易: {price}")
                    else:
                        logger.warning(f"无法获取当前实时价格，使用传入的历史价格: {price}")

                except Exception as e:
                    logger.warning(f"获取当前价格失败: {e}, 使用传入的历史价格: {price}")
                    # 获取价格失败不中断流程，使用传入的价格继续执行

                # 设置交易数量（优化版本）
                logger.info(f"设置交易数量: {amount}克")
                try:
                    # 根据选择器文档更新克重快捷按钮选择器
                    weight_buttons = {
                        1000: ".tradeinfo .weigthbox .weigthnumber[data-v-0fa31b49]:has-text('1000g')",
                        2000: ".tradeinfo .weigthbox .weigthnumber[data-v-0fa31b49]:has-text('2000g')",
                        3000: ".tradeinfo .weigthbox .weigthnumber[data-v-0fa31b49]:has-text('3000g')",
                        5000: ".tradeinfo .weigthbox .weigthnumber[data-v-0fa31b49]:has-text('5000g')",
                        # 备用选择器（不带data-v属性）
                        100: ".weigthnumber:has-text('100g')",
                        200: ".weigthnumber:has-text('200g')",
                        300: ".weigthnumber:has-text('300g')",
                        500: ".weigthnumber:has-text('500g')"
                    }

                    # 如果数量匹配预设按钮，直接点击（优先使用缓存）
                    amount_set = False
                    if amount in weight_buttons:
                        selectors = [weight_buttons[amount]]
                        weight_button = await self._find_element_optimized(f"weight_{amount}", selectors, timeout=2)
                        if weight_button:
                            await weight_button.click()
                            logger.info(f"已点击{amount}g按钮")
                            amount_set = True
                            # 快速验证设置是否成功
                            await asyncio.sleep(0.3)  # 减少等待时间

                    # 如果没有匹配的按钮或点击失败，使用加减按钮调整
                    if not amount_set:
                        # 获取当前显示的克重（使用正确的选择器）
                        amount_input = await self.page.query_selector(self.AMOUNT_INPUT_SELECTOR)
                        current_amount = 0

                        if amount_input:
                            current_value = await amount_input.get_attribute("value")
                            try:
                                current_amount = int(current_value or "0")
                                logger.info(f"当前显示的克重: {current_amount}克")
                            except ValueError:
                                logger.warning(f"无法解析当前克重: {current_value}")

                        # 如果当前克重为0或无法读取，先点击1000g按钮作为基础
                        if current_amount == 0:
                            base_button = await self.page.query_selector(weight_buttons[1000])
                            if base_button:
                                await base_button.click()
                                logger.info("已点击1000g按钮作为基础")
                                current_amount = 1000

                        # 计算需要点击加号或减号的次数
                        target_clicks = (amount - current_amount) // 1000  # 整数除法，得到需要点击的次数

                        if target_clicks > 0:
                            # 需要增加数量（使用正确的选择器）
                            plus_button = await self.page.query_selector(self.PLUS_BUTTON_SELECTOR)
                            if plus_button:
                                for _ in range(target_clicks):
                                    await plus_button.click()
                                await asyncio.sleep(0.2)  # 短暂延迟确保点击生效
                                logger.info(f"点击加号{target_clicks}次，增加到{current_amount + target_clicks * 1000}克")
                        elif target_clicks < 0:
                            # 需要减少数量（使用正确的选择器）
                            minus_button = await self.page.query_selector(self.MINUS_BUTTON_SELECTOR)
                            if minus_button:
                                for _ in range(abs(target_clicks)):
                                    await minus_button.click()
                                await asyncio.sleep(0.2)  # 短暂延迟确保点击生效
                                logger.info(f"点击减号{abs(target_clicks)}次，减少到{current_amount + target_clicks * 1000}克")

                    # 验证最终克重
                    await asyncio.sleep(0.5)  # 等待UI更新
                    amount_input = await self.page.query_selector(self.AMOUNT_INPUT_SELECTOR)
                    if amount_input:
                        final_value = await amount_input.get_attribute("value")
                        try:
                            final_amount = int(final_value or "0")
                            logger.info(f"最终设置的克重: {final_amount}克")

                            if final_amount != amount:
                                logger.warning(f"设置的克重({final_amount})与目标克重({amount})不一致")
                        except ValueError:
                            logger.warning(f"无法解析最终克重: {final_value}")

                    # 验证完成，继续执行下一步操作
                except Exception as e:
                    error_msg = f"设置交易数量失败: {e}"
                    logger.error(error_msg)
                    logger.error(traceback.format_exc())
                    return {
                        "success": False,
                        "message": error_msg,
                        "order_id": None
                    }

                # 执行买入或卖出操作
                order_result = None
                try:
                    if direction == 1:  # 买入
                        logger.info("执行买入操作")
                        order_result = await self._place_buy_order(price, amount)
                    else:  # 卖出
                        logger.info("执行卖出操作")
                        order_result = await self._place_sell_order(price, amount)

                    if not order_result or not order_result.get("success", False):
                        error_msg = order_result.get("message", "交易执行失败，无错误详情")
                        logger.error(f"交易失败: {error_msg}")
                        return order_result

                    # 交易成功，记录订单ID
                    order_id = order_result.get("order_id")
                    logger.info(f"交易成功，订单ID: {order_id}")

                    # 记录耗时
                    execution_time = time.time() - order_start_time
                    logger.info(f"下单总耗时: {execution_time:.2f}秒")

                    # 添加额外信息到结果中
                    result = {
                        "success": True,
                        "message": "下单成功",
                        "order_id": order_id,
                        "direction": direction,
                        "price": price,
                        "amount": amount,
                        "execution_time": execution_time
                    }

                    return result
                except Exception as e:
                    error_msg = f"交易执行异常: {e}"
                    logger.error(error_msg)
                    logger.error(traceback.format_exc())
                    return {
                        "success": False,
                        "message": error_msg,
                        "order_id": None
                    }
            except Exception as e:
                error_msg = f"下单过程中发生未捕获的异常: {e}"
                logger.error(error_msg)
                logger.error(traceback.format_exc())
                return {
                    "success": False,
                    "message": error_msg,
                    "order_id": None
                }



    async def _get_current_price(self, direction: int) -> Optional[float]:
        """获取当前页面显示的价格

        Args:
            direction: 交易方向，1为买入，-1为卖出

        Returns:
            当前价格，如果获取失败则返回None
        """
        try:
            logger.info(f"开始获取当前页面价格，交易方向: {'买入' if direction == 1 else '卖出'}")

            # 使用配置文件中的选择器
            from services.spot_selectors_config import get_selector, get_backup_selectors

            # 根据交易方向获取相应的价格选择器
            if direction == 1:  # 买入，需要获取卖出价(ask)，红色显示
                primary_selector = get_selector("TRADE", "sell_price")
                backup_selectors = get_backup_selectors("sell_price")
                logger.info(f"买入操作，获取卖出价(红色)，主选择器: {primary_selector}")
            else:  # 卖出，需要获取买入价(bid)，绿色显示
                primary_selector = get_selector("TRADE", "buy_price")
                backup_selectors = get_backup_selectors("buy_price")
                logger.info(f"卖出操作，获取买入价(绿色)，主选择器: {primary_selector}")

            # 构建完整的选择器列表
            all_selectors = [primary_selector] + backup_selectors if backup_selectors else [primary_selector]

            # 添加通用备用选择器
            all_selectors.extend([
                ".current-price",
                ".trade-price",
                "[class*='price']:not([class*='input'])"
            ])

            # 首先尝试直接查询所有选择器
            for selector in all_selectors:
                try:
                    if not selector:  # 跳过空选择器
                        continue

                    price_element = await self.page.query_selector(selector)
                    if price_element:
                        price_text = await price_element.text_content()
                        if price_text:
                            # 提取数字
                            import re
                            price_match = re.search(r'(\d+\.?\d*)', price_text)
                            if price_match:
                                price = float(price_match.group(1))
                                # 验证价格是否在合理范围内（扩大价格范围）
                                if 600 <= price <= 1000:
                                    logger.info(f"通过选择器'{selector}'获取到当前{'卖出' if direction == 1 else '买入'}价: {price}")
                                    return price
                                else:
                                    logger.debug(f"价格{price}不在合理范围内，继续尝试其他选择器")
                except Exception as e:
                    logger.debug(f"使用选择器'{selector}'获取价格失败: {e}")
                    continue

            # 如果直接选择器方法失败，使用更智能的JavaScript方法
            # 根据文档中的颜色特征增强JavaScript查询
            color_keyword = "red" if direction == 1 else "green"
            # 将条件逻辑移到f-string外部
            max_or_min_valid = "Math.max(...validPrices)" if direction == 1 else "Math.min(...validPrices)"
            max_or_min_prices = "Math.max(...prices)" if direction == 1 else "Math.min(...prices)"

            price_script = f"""
            () => {{
                // 根据现货创富平台文档使用正确的选择器
                const specificSelectors = [
                    '.amountsetbox .amountlist[data-v-0fa31b49] .{color_keyword}color[data-v-0fa31b49]',
                    '.amountsetbox .amountlist .{color_keyword}color[data-v-0fa31b49]',
                    '.amountlist[data-v-0fa31b49] .{color_keyword}color[data-v-0fa31b49]',
                    '.{color_keyword}color[data-v-0fa31b49]',
                    '.amountsetbox .{color_keyword}color',
                    '.{color_keyword}color'
                ];

                // 尝试使用精确选择器
                for (const selector of specificSelectors) {{
                    const elements = Array.from(document.querySelectorAll(selector));
                    if (elements.length > 0) {{
                        for (const el of elements) {{
                            const text = el.textContent.trim();
                            const match = text.match(/(\\d+\\.?\\d*)/);
                            if (match) {{
                                const price = parseFloat(match[1]);
                                if (price > 600 && price < 1000) {{
                                    console.log(`通过选择器 ${{selector}} 找到价格: ${{price}}`);
                                    return price;
                                }}
                            }}
                        }}
                    }}
                }}

                // 备用方法：通过颜色特征查找价格元素
                let colorElements = Array.from(document.querySelectorAll('[class*="{color_keyword}"], [style*="{color_keyword}"]'));

                // 过滤出可能包含数字的元素
                let colorPrices = colorElements
                    .map(el => el.textContent.trim())
                    .filter(text => /\\d+\\.?\\d*/.test(text))
                    .map(text => {{
                        const match = text.match(/(\\d+\\.?\\d*)/);
                        return match ? parseFloat(match[1]) : null;
                    }})
                    .filter(price => price !== null);

                if (colorPrices.length > 0) {{
                    // 返回最接近合理价格范围的值（黄金价格通常在600-1000元左右）
                    const validPrices = colorPrices.filter(p => p > 600 && p < 1000);
                    if (validPrices.length > 0) {{
                        return {max_or_min_valid};
                    }}
                    return colorPrices[0]; // 如果没有在合理范围内的价格，返回第一个
                }}

                // 最后备用方法：查找所有价格相关元素
                const priceElements = Array.from(document.querySelectorAll('[class*="price"], [class*="amount"]'));

                // 过滤出可能包含数字的元素
                const priceTexts = priceElements
                    .map(el => el.textContent.trim())
                    .filter(text => /\\d+\\.?\\d*/.test(text));

                // 提取价格数字
                const prices = priceTexts
                    .map(text => {{
                        const match = text.match(/(\\d+\\.?\\d*)/);
                        return match ? parseFloat(match[1]) : null;
                    }})
                    .filter(price => price !== null && price > 600 && price < 1000);

                // 返回适当的价格值
                if (prices.length > 0) {{
                    return {max_or_min_prices};
                }}

                return null;
            }}
            """

            price = await self.page.evaluate(price_script)

            if price:
                logger.info(f"通过JavaScript获取到当前{'卖出' if direction == 1 else '买入'}价: {price}")
                return float(price)

            # 最后一种方法：尝试获取任何可能的价格值
            try:
                # 通过OCR或其他方式分析页面中的价格（简化实现）
                all_text = await self.page.evaluate("() => document.body.textContent")

                # 查找所有可能的价格数字
                import re
                price_matches = re.findall(r'(\d+\.\d+)', all_text)

                # 过滤出可能是黄金价格的值（通常在600-1000元范围内）
                valid_prices = [float(p) for p in price_matches if 600 <= float(p) <= 1000]

                if valid_prices:
                    # 根据交易方向选择合适的价格
                    if direction == 1:  # 买入使用较高价格
                        price = max(valid_prices)
                    else:  # 卖出使用较低价格
                        price = min(valid_prices)

                    logger.info(f"通过文本分析获取到当前价格: {price}")
                    return price
            except Exception as text_error:
                logger.error(f"通过文本分析获取价格失败: {text_error}")

            logger.warning(f"未能获取到当前{'卖出' if direction == 1 else '买入'}价")
            return None
        except Exception as e:
            logger.error(f"获取当前价格异常: {e}")
            logger.error(traceback.format_exc())
            return None

    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """
        取消订单

        Args:
            order_id: 订单ID

        Returns:
            取消结果
        """
        try:
            logger.info(f"开始取消订单: 订单ID={order_id}")

            # 导航到订单页面
            nav_success = await self._navigate_to_orders_page()
            if not nav_success:
                # 如果导航失败，直接使用URL
                logger.info("使用直接URL导航到订单页面")
                await self.page.goto(self.ORDERS_PAGE_URL, wait_until="domcontentloaded", timeout=45000)
                await asyncio.sleep(3)

            # 查找订单
            order_element = await self._find_order_by_id(order_id)
            if not order_element:
                return {
                    "success": False,
                    "message": f"未找到订单: {order_id}",
                    "order_id": order_id
                }

            # 尝试查找取消按钮（通常在订单项中）
            cancel_selectors = [
                ".cancel-btn[data-v-9a81d21c]",  # 取消按钮
                ".cancelbtn[data-v-9a81d21c]",   # 取消按钮（平仓弹窗中的）
                "uni-button:has-text('取消')",
                "uni-button:has-text('撤单')",
                ".btn-cancel",
                ".order-cancel"
            ]

            cancel_button = None
            for selector in cancel_selectors:
                try:
                    button = await order_element.query_selector(selector)
                    if button and await button.is_visible():
                        cancel_button = button
                        logger.info(f"找到取消按钮: {selector}")
                        break
                except Exception:
                    continue

            if not cancel_button:
                # 如果在订单项中没找到取消按钮，尝试在页面级别查找
                for selector in cancel_selectors:
                    try:
                        button = await self.page.query_selector(selector)
                        if button and await button.is_visible():
                            cancel_button = button
                            logger.info(f"在页面级别找到取消按钮: {selector}")
                            break
                    except Exception:
                        continue

            if cancel_button:
                logger.info("准备点击取消按钮")
                await cancel_button.click()
                logger.info("已点击取消按钮")

                # 等待操作完成
                await asyncio.sleep(2)

                return {
                    "success": True,
                    "message": "订单取消成功",
                    "order_id": order_id
                }
            else:
                logger.warning(f"未找到订单的取消按钮: {order_id}")
                return {
                    "success": False,
                    "message": "未找到取消按钮，订单可能已经成交或无法取消",
                    "order_id": order_id
                }

        except Exception as e:
            error_msg = f"取消订单异常: {e}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": error_msg,
                "order_id": order_id
            }

    async def close_position(self, order_id: str, use_default_settlement: bool = False) -> Dict[str, Any]:
        """
        平仓操作 - 根据选择器文档实现

        Args:
            order_id: 订单ID
            use_default_settlement: 是否使用违约结算，默认为False（使用正常结料）

        Returns:
            平仓结果
        """
        try:
            logger.info(f"开始平仓操作: 订单ID={order_id}, 使用违约结算={use_default_settlement}")

            # 导航到订单页面
            nav_success = await self._navigate_to_orders_page()
            if not nav_success:
                # 如果导航失败，直接使用URL
                logger.info("使用直接URL导航到订单页面")
                await self.page.goto(self.ORDERS_PAGE_URL, wait_until="domcontentloaded", timeout=45000)
                await asyncio.sleep(3)

            # 查找订单
            order_element = await self._find_order_by_id(order_id)
            if not order_element:
                return {
                    "success": False,
                    "message": f"未找到订单: {order_id}",
                    "order_id": order_id
                }

            # 点击平仓按钮
            close_result = await self._click_close_button(order_element, use_default_settlement)
            if not close_result["success"]:
                return close_result

            # 处理平仓弹窗
            confirm_result = await self._handle_close_popup(order_id)

            return confirm_result

        except Exception as e:
            logger.error(f"平仓操作失败: {e}")
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": f"平仓操作失败: {str(e)}",
                "order_id": order_id
            }

    async def _find_order_by_id(self, order_id: str) -> Optional[Any]:
        """
        根据订单ID查找订单元素

        Args:
            order_id: 订单ID

        Returns:
            订单元素，如果未找到则返回None
        """
        try:
            logger.info(f"查找订单: {order_id}")

            # 等待订单列表加载
            await asyncio.sleep(2)

            # 查找所有订单项
            order_items = await self.page.query_selector_all(self.ORDER_ITEM_SELECTOR)
            logger.info(f"找到 {len(order_items)} 个订单项")

            # 遍历订单项，查找匹配的订单ID
            for item in order_items:
                try:
                    # 获取订单ID文本
                    order_text = await item.text_content()
                    if order_text and order_id in order_text:
                        logger.info(f"找到匹配订单: {order_id}")
                        return item
                except Exception as item_error:
                    logger.debug(f"处理订单项时出错: {item_error}")
                    continue

            logger.warning(f"未找到匹配订单: {order_id}")
            return None

        except Exception as e:
            logger.error(f"查找订单失败: {e}")
            return None

    async def _click_close_button(self, order_element: Any, use_default_settlement: bool) -> Dict[str, Any]:
        """
        点击平仓按钮 - 使用经过100%验证的多重策略

        Args:
            order_element: 订单元素
            use_default_settlement: 是否优先使用违约结算

        Returns:
            操作结果
        """
        try:
            if use_default_settlement:
                # 使用违约结算按钮 - 采用经过100%验证的多重策略
                logger.info("使用违约结算按钮进行平仓（100%验证策略）")

                # 违约结算按钮的多重选择器策略（确保100%找到）
                settlement_selectors = [
                    ".back1",  # 主选择器
                    ".back1[data-v-14224f5e]",  # 带data-v属性
                    ".deposit-right .back1",  # 完整路径
                    "//uni-view[contains(text(), '违约结算')]",  # 文本匹配
                    "//uni-view[contains(@class, 'back1') and contains(text(), '违约结算')]",  # 类名和文本组合
                    ".order-button .back1",  # 按钮容器内
                    "//uni-view[text()='违约结算']",  # 精确文本匹配
                ]

                close_button = None
                used_strategy = None

                # 尝试所有策略
                for i, selector in enumerate(settlement_selectors):
                    try:
                        if selector.startswith("//"):
                            # XPath选择器
                            button = await order_element.query_selector(f"xpath={selector}")
                        else:
                            # CSS选择器
                            button = await order_element.query_selector(selector)

                        if button and await button.is_visible():
                            # 验证按钮文本
                            button_text = await button.text_content()
                            if "违约结算" in button_text:
                                close_button = button
                                used_strategy = f"策略{i+1}: {selector}"
                                logger.info(f"✅ 使用 {used_strategy} 找到违约结算按钮")
                                break
                    except Exception as e:
                        logger.debug(f"策略{i+1}失败: {selector} - {e}")
                        continue

                if not close_button:
                    logger.error("❌ 所有违约结算按钮查找策略都失败")
                    return {
                        "success": False,
                        "message": "无法找到违约结算按钮（所有策略都失败）"
                    }

                # 点击违约结算按钮
                await close_button.click()
                logger.info(f"✅ 成功点击违约结算按钮 (使用{used_strategy})")
                button_type = "违约结算"

            else:
                # 使用结料按钮（保持原有逻辑）
                logger.info("使用结料按钮进行平仓")
                close_button = await order_element.query_selector(self.FEEDBACK_BUTTON_SELECTOR)
                if close_button and await close_button.is_visible():
                    await close_button.click()
                    logger.info("已点击结料按钮")
                    button_type = "结料"
                else:
                    return {
                        "success": False,
                        "message": "未找到结料按钮"
                    }

            # 等待弹窗出现
            await asyncio.sleep(2)

            return {
                "success": True,
                "message": f"已点击{button_type}按钮",
                "button_type": button_type,
                "strategy": used_strategy if use_default_settlement else "标准结料策略"
            }

        except Exception as e:
            logger.error(f"点击平仓按钮失败: {e}")
            return {
                "success": False,
                "message": f"点击平仓按钮失败: {str(e)}"
            }

    async def _handle_close_popup(self, order_id: str) -> Dict[str, Any]:
        """
        处理平仓弹窗 - 根据选择器文档实现

        Args:
            order_id: 订单ID

        Returns:
            操作结果
        """
        try:
            logger.info("处理平仓弹窗")

            # 等待弹窗出现 - 使用多重策略确保找到弹窗
            popup_selectors = [
                ".popcont",  # 主选择器（不依赖data-v属性）
                ".popcont[data-v-9a81d21c]",  # 带data-v属性的选择器
                "//uni-view[contains(@class, 'popcont')]",  # XPath备用选择器
            ]

            popup = None
            for selector in popup_selectors:
                try:
                    if selector.startswith("//"):
                        # XPath选择器
                        popup = await self.page.wait_for_selector(f"xpath={selector}", timeout=5000)
                    else:
                        # CSS选择器
                        popup = await self.page.wait_for_selector(selector, timeout=5000)

                    if popup and await popup.is_visible():
                        logger.info(f"✅ 使用选择器找到弹窗: {selector}")
                        break
                except Exception as e:
                    logger.debug(f"弹窗选择器失败: {selector} - {e}")
                    continue

            if not popup:
                logger.error("❌ 所有弹窗选择器都失败")
                return {
                    "success": False,
                    "message": "等待平仓弹窗出现超时"
                }

            logger.info("平仓弹窗已出现")

            # 点击确认按钮（绿色背景）- 使用多重策略，支持back1和back2两种类名
            confirm_selectors = [
                ".back2",  # 主选择器（某些订单使用back2）
                ".back1",  # 备用选择器（某些订单使用back1）
                ".back2[data-v-14224f5e]",  # 带data-v属性的back2
                ".back1[data-v-14224f5e]",  # 带data-v属性的back1
                "//uni-button[contains(@class, 'back2')]",  # XPath选择器back2
                "//uni-button[contains(@class, 'back1')]",  # XPath选择器back1
                "//uni-button[contains(text(), '结算')]",  # 文本匹配"结算"
                "//uni-button[contains(text(), '确认')]",  # 文本匹配"确认"
            ]

            confirm_button = None
            for selector in confirm_selectors:
                try:
                    if selector.startswith("//"):
                        # XPath选择器
                        button = await self.page.query_selector(f"xpath={selector}")
                    else:
                        # CSS选择器
                        button = await self.page.query_selector(selector)

                    if button and await button.is_visible():
                        confirm_button = button
                        logger.info(f"✅ 找到确认按钮: {selector}")
                        break
                except Exception as e:
                    logger.debug(f"确认按钮选择器失败: {selector} - {e}")
                    continue

            if confirm_button:
                logger.info("找到平仓确认按钮，准备点击")
                await confirm_button.click()
                logger.info("已点击平仓确认按钮")

                # 等待操作完成
                await asyncio.sleep(3)

                # 验证平仓是否成功
                success = await self._verify_close_success(order_id)

                if success:
                    logger.info(f"平仓操作成功确认: 订单ID={order_id}")
                    return {
                        "success": True,
                        "message": "平仓操作成功",
                        "order_id": order_id,
                        "close_time": datetime.now().isoformat()
                    }
                else:
                    logger.warning(f"平仓操作可能未成功: 订单ID={order_id}")
                    return {
                        "success": False,
                        "message": "平仓操作可能未成功，未能确认结果",
                        "order_id": order_id
                    }
            else:
                return {
                    "success": False,
                    "message": "未找到平仓确认按钮"
                }

        except Exception as e:
            logger.error(f"处理平仓弹窗失败: {e}")
            return {
                "success": False,
                "message": f"处理平仓弹窗失败: {str(e)}"
            }

    async def _verify_close_success(self, order_id: str) -> bool:
        """
        验证平仓是否成功

        Args:
            order_id: 订单ID

        Returns:
            是否成功
        """
        try:
            # 检查是否有成功提示
            success_selectors = [
                "uni-view:has-text('成功')",
                "uni-view:has-text('已结算')",
                "uni-view:has-text('平仓成功')",
                ".success-message"
            ]

            for selector in success_selectors:
                try:
                    success_element = await self.page.query_selector(selector)
                    if success_element and await success_element.is_visible():
                        success_text = await success_element.text_content()
                        logger.info(f"找到平仓成功提示: {success_text}")
                        return True
                except Exception:
                    continue

            # 检查订单是否已从持仓列表中消失
            await asyncio.sleep(2)
            order_element = await self._find_order_by_id(order_id)
            if not order_element:
                logger.info(f"订单 {order_id} 已从持仓列表中消失，平仓成功")
                return True

            logger.warning(f"订单 {order_id} 仍在持仓列表中，平仓可能失败")
            return False

        except Exception as e:
            logger.error(f"验证平仓结果失败: {e}")
            return False

    async def _navigate_to_orders_page(self) -> bool:
        """
        导航到订单页面

        Returns:
            是否成功
        """
        try:
            logger.info("导航到订单页面")
            await self.page.goto(self.ORDERS_PAGE_URL, wait_until="domcontentloaded", timeout=45000)
            await asyncio.sleep(3)

            # 检查是否成功到达订单页面
            current_url = self.page.url
            if "myorder" in current_url:
                logger.info("成功导航到订单页面")
                return True
            else:
                logger.warning(f"导航到订单页面失败，当前URL: {current_url}")
                return False

        except Exception as e:
            logger.error(f"导航到订单页面失败: {e}")
            return False


