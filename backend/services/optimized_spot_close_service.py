#!/usr/bin/env python3
"""
优化的现货平仓服务

专门使用违约结算按钮进行平仓，基于100%测试验证的方法
移除所有结料相关代码，确保违约结算按钮100%能找到
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
from .default_settlement_service import DefaultSettlementService

logger = logging.getLogger(__name__)


class OptimizedSpotCloseService:
    """优化的现货平仓服务"""
    
    def __init__(self, page, timeout: int = 15):
        """
        初始化优化的平仓服务
        
        Args:
            page: Playwright页面实例
            timeout: 超时时间（秒）
        """
        self.page = page
        self.timeout = timeout
        
        # 使用经过100%验证的违约结算服务
        self.settlement_service = DefaultSettlementService(page, timeout)
        
        # 平仓操作统计
        self.operation_stats = {
            "total_attempts": 0,
            "successful_closures": 0,
            "failed_closures": 0,
            "last_operation_time": None
        }
    
    async def close_position(self, order_id: str) -> Dict[str, Any]:
        """
        平仓操作（仅使用违约结算）
        
        Args:
            order_id: 订单ID
            
        Returns:
            平仓结果
        """
        try:
            self.operation_stats["total_attempts"] += 1
            self.operation_stats["last_operation_time"] = datetime.now().isoformat()
            
            logger.info(f"🚀 开始平仓操作: 订单ID={order_id}")
            logger.info("📋 平仓策略: 仅使用违约结算按钮")
            
            # 1. 检查违约结算按钮可用性
            logger.info("🔍 步骤1: 检查违约结算按钮可用性")
            availability_result = await self.settlement_service.check_settlement_button_availability(order_id)
            
            if not availability_result["success"]:
                logger.error(f"❌ 违约结算按钮不可用: {availability_result['message']}")
                self.operation_stats["failed_closures"] += 1
                return {
                    "success": False,
                    "message": f"违约结算按钮不可用: {availability_result['message']}",
                    "error_type": "button_unavailable",
                    "order_id": order_id
                }
            
            logger.info(f"✅ 违约结算按钮可用，策略数: {len(availability_result['available_strategies'])}")
            logger.info(f"📋 可用策略: {availability_result['available_strategies']}")
            
            # 2. 执行违约结算平仓
            logger.info("⚡ 步骤2: 执行违约结算平仓")
            settlement_result = await self.settlement_service.close_position_by_default_settlement(order_id)
            
            if settlement_result["success"]:
                logger.info(f"🎉 平仓成功: 订单 {order_id}")
                self.operation_stats["successful_closures"] += 1
                
                # 添加成功统计信息
                settlement_result.update({
                    "close_method": "default_settlement",
                    "available_strategies": availability_result['available_strategies'],
                    "operation_stats": self.operation_stats.copy()
                })
                
                return settlement_result
            else:
                logger.error(f"❌ 平仓失败: {settlement_result['message']}")
                self.operation_stats["failed_closures"] += 1
                return settlement_result
                
        except Exception as e:
            logger.error(f"❌ 平仓操作异常: {e}")
            self.operation_stats["failed_closures"] += 1
            return {
                "success": False,
                "message": f"平仓操作异常: {str(e)}",
                "error_type": "exception",
                "order_id": order_id
            }
    
    async def batch_close_positions(self, order_ids: list) -> Dict[str, Any]:
        """
        批量平仓操作
        
        Args:
            order_ids: 订单ID列表
            
        Returns:
            批量平仓结果
        """
        try:
            logger.info(f"🚀 开始批量平仓: {len(order_ids)} 个订单")
            
            results = {
                "total_orders": len(order_ids),
                "successful_orders": [],
                "failed_orders": [],
                "results": {},
                "start_time": datetime.now().isoformat()
            }
            
            for i, order_id in enumerate(order_ids, 1):
                logger.info(f"📋 处理订单 {i}/{len(order_ids)}: {order_id}")
                
                # 执行单个平仓操作
                close_result = await self.close_position(order_id)
                results["results"][order_id] = close_result
                
                if close_result["success"]:
                    results["successful_orders"].append(order_id)
                    logger.info(f"✅ 订单 {order_id} 平仓成功")
                else:
                    results["failed_orders"].append(order_id)
                    logger.error(f"❌ 订单 {order_id} 平仓失败: {close_result['message']}")
                
                # 订单间等待，避免操作过快
                if i < len(order_ids):
                    await asyncio.sleep(2)
            
            results["end_time"] = datetime.now().isoformat()
            results["success_rate"] = (len(results["successful_orders"]) / len(order_ids)) * 100
            
            logger.info(f"📊 批量平仓完成:")
            logger.info(f"   总订单数: {results['total_orders']}")
            logger.info(f"   成功: {len(results['successful_orders'])}")
            logger.info(f"   失败: {len(results['failed_orders'])}")
            logger.info(f"   成功率: {results['success_rate']:.1f}%")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 批量平仓异常: {e}")
            return {
                "success": False,
                "message": f"批量平仓异常: {str(e)}",
                "error_type": "batch_exception"
            }
    
    async def get_closable_orders(self) -> Dict[str, Any]:
        """
        获取可平仓的订单列表（有违约结算按钮的订单）
        
        Returns:
            可平仓订单信息
        """
        try:
            logger.info("🔍 查找可平仓的订单...")
            
            # 导航到订单页面
            nav_result = await self.settlement_service._navigate_to_orders()
            if not nav_result["success"]:
                return {
                    "success": False,
                    "message": f"导航失败: {nav_result['message']}"
                }
            
            # 获取所有订单容器
            order_containers = await self.page.query_selector_all(".puwidth")
            logger.info(f"找到 {len(order_containers)} 个订单容器")
            
            closable_orders = []
            
            for i, container in enumerate(order_containers):
                try:
                    # 提取订单ID
                    order_sn_element = await container.query_selector(".order-sn")
                    if order_sn_element:
                        order_text = await order_sn_element.text_content()
                        import re
                        match = re.search(r'订单号[：:]\s*([A-Z0-9]+)', order_text)
                        if match:
                            order_id = match.group(1)
                            
                            # 检查是否有违约结算按钮
                            has_settlement = await self._check_settlement_button_quick(container)
                            
                            if has_settlement:
                                # 提取订单详细信息
                                order_info = await self._extract_order_info_quick(container, order_id)
                                closable_orders.append(order_info)
                                logger.info(f"✅ 可平仓订单: {order_id}")
                            else:
                                logger.info(f"❌ 不可平仓订单: {order_id} (无违约结算按钮)")
                
                except Exception as e:
                    logger.warning(f"处理订单容器 {i+1} 时出错: {e}")
                    continue
            
            result = {
                "success": True,
                "total_orders": len(order_containers),
                "closable_orders": len(closable_orders),
                "orders": closable_orders,
                "check_time": datetime.now().isoformat()
            }
            
            logger.info(f"📊 可平仓订单统计:")
            logger.info(f"   总订单数: {result['total_orders']}")
            logger.info(f"   可平仓: {result['closable_orders']}")
            logger.info(f"   可平仓率: {(result['closable_orders']/result['total_orders']*100) if result['total_orders'] > 0 else 0:.1f}%")
            
            return result
            
        except Exception as e:
            logger.error(f"获取可平仓订单失败: {e}")
            return {
                "success": False,
                "message": f"获取可平仓订单失败: {str(e)}"
            }
    
    async def _check_settlement_button_quick(self, container) -> bool:
        """快速检查容器中是否有违约结算按钮"""
        try:
            # 使用最可靠的策略快速检查
            selectors = [
                ".back1",  # 主选择器
                "//uni-view[contains(text(), '违约结算')]"  # 文本匹配
            ]
            
            for selector in selectors:
                try:
                    if selector.startswith("//"):
                        button = await container.query_selector(f"xpath={selector}")
                    else:
                        button = await container.query_selector(selector)
                    
                    if button and await button.is_visible():
                        # 验证按钮文本
                        button_text = await button.text_content()
                        if "违约结算" in button_text:
                            return True
                except Exception:
                    continue
            
            return False
            
        except Exception:
            return False
    
    async def _extract_order_info_quick(self, container, order_id: str) -> Dict[str, Any]:
        """快速提取订单信息"""
        try:
            order_info = {
                "order_id": order_id,
                "has_settlement_button": True
            }
            
            # 提取开仓价格
            try:
                open_price_element = await container.query_selector(".per-price")
                if open_price_element:
                    order_info["open_price"] = await open_price_element.text_content()
            except Exception:
                order_info["open_price"] = None
            
            # 提取当前价格
            try:
                current_price_element = await container.query_selector(".new-price")
                if current_price_element:
                    order_info["current_price"] = await current_price_element.text_content()
            except Exception:
                order_info["current_price"] = None
            
            return order_info
            
        except Exception as e:
            logger.debug(f"提取订单信息失败: {e}")
            return {
                "order_id": order_id,
                "has_settlement_button": True,
                "extraction_error": str(e)
            }
    
    def get_operation_stats(self) -> Dict[str, Any]:
        """获取操作统计信息"""
        stats = self.operation_stats.copy()
        
        if stats["total_attempts"] > 0:
            stats["success_rate"] = (stats["successful_closures"] / stats["total_attempts"]) * 100
        else:
            stats["success_rate"] = 0
        
        return stats
    
    def reset_stats(self):
        """重置操作统计"""
        self.operation_stats = {
            "total_attempts": 0,
            "successful_closures": 0,
            "failed_closures": 0,
            "last_operation_time": None
        }
        logger.info("📊 操作统计已重置")


# 使用示例
async def example_usage():
    """使用示例"""
    # 假设已有page实例
    # service = OptimizedSpotCloseService(page)
    # 
    # # 获取可平仓订单
    # closable_result = await service.get_closable_orders()
    # print(f"可平仓订单: {closable_result}")
    # 
    # # 单个平仓
    # if closable_result["success"] and closable_result["orders"]:
    #     order_id = closable_result["orders"][0]["order_id"]
    #     close_result = await service.close_position(order_id)
    #     print(f"平仓结果: {close_result}")
    # 
    # # 批量平仓
    # order_ids = [order["order_id"] for order in closable_result["orders"]]
    # batch_result = await service.batch_close_positions(order_ids)
    # print(f"批量平仓结果: {batch_result}")
    # 
    # # 查看统计
    # stats = service.get_operation_stats()
    # print(f"操作统计: {stats}")
    
    print("示例代码完成")


if __name__ == "__main__":
    import asyncio
    asyncio.run(example_usage())
