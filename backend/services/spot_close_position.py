"""
现货平台平仓操作服务

提供在现货平台持仓订单页面执行平仓操作的功能
"""
import asyncio
import logging
import time
import re
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, StaleElementReferenceException

from ..utils.logger import setup_logger

# 设置日志
logger = setup_logger("spot_close_position", "logs/spot_close_position.log")

class SpotClosePositionService:
    """现货平台平仓操作服务类"""

    def __init__(self, driver, account_info=None):
        """
        初始化服务

        Args:
            driver: Selenium WebDriver实例
            account_info: 账户信息
        """
        self.driver = driver
        self.account_info = account_info
        self.order_page_url = "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"

        # 选择器配置
        self.selectors = {
            # 结料按钮选择器
            "feed_button": ".feedingback[data-v-9a81d21c]",
            # 违约结算按钮选择器
            "default_button": ".back1[data-v-9a81d21c]",
            # 开仓价格选择器
            "open_price": ".per-price[data-v-9a81d21c]",
            # 实时平仓价格选择器（红色）
            "close_price_red": ".color1[data-v-9a81d21c]",
            # 实时平仓价格选择器（绿色）
            "close_price_green": ".color2[data-v-9a81d21c]",
            # 订单号选择器
            "order_id": ".deposit-bottom[data-v-9a81d21c]",
            # 平仓结算按钮选择器
            "settle_button": ".back2[data-v-9a81d21c]",
            # 平仓取消按钮选择器
            "cancel_button": ".cancelbtn[data-v-9a81d21c]",
            # 平仓弹窗订单号选择器
            "popup_order_id": ".popcont .boxinfo[data-v-9a81d21c]",
            # 平仓弹窗开仓价选择器
            "popup_open_price": ".popcont .boxinfo[data-v-9a81d21c]",
            # 平仓弹窗平仓价选择器
            "popup_close_price": ".popcont .boxinfo[data-v-9a81d21c] .color2[data-v-9a81d21c]"
        }

        # 等待超时配置
        self.timeout = 10  # 默认等待超时时间（秒）

    async def navigate_to_order_page(self) -> bool:
        """
        导航到持仓订单页面

        Returns:
            是否成功
        """
        try:
            logger.info(f"导航到持仓订单页面: {self.order_page_url}")
            self.driver.get(self.order_page_url)

            # 等待页面加载完成
            await asyncio.sleep(2)

            # 检查页面标题或特定元素是否存在，确认页面已正确加载
            try:
                WebDriverWait(self.driver, self.timeout).until(
                    EC.presence_of_element_located((By.XPATH, "//uni-view[contains(text(), '实物挂订单')]"))
                )
                logger.info("持仓订单页面加载成功")
                return True
            except TimeoutException:
                logger.warning("等待持仓订单页面加载超时")
                # 尝试截图记录错误
                self._save_screenshot("order_page_timeout")
                return False

        except Exception as e:
            logger.error(f"导航到持仓订单页面失败: {e}")
            return False

    async def find_order_by_id(self, order_id: str) -> Optional[Dict[str, Any]]:
        """
        根据订单ID查找订单

        Args:
            order_id: 订单ID

        Returns:
            订单信息，如果未找到则返回None
        """
        try:
            logger.info(f"查找订单: {order_id}")

            # 等待订单列表加载 - 修复选择器
            try:
                # 尝试新的选择器
                WebDriverWait(self.driver, self.timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".puwidth[data-v-14224f5e]"))
                )
            except TimeoutException:
                logger.warning("等待订单列表加载超时，尝试备用选择器")
                try:
                    # 备用选择器：不依赖data-v属性
                    WebDriverWait(self.driver, self.timeout).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".puwidth"))
                    )
                except TimeoutException:
                    logger.warning("所有选择器都超时")
                    return None

            # 查找所有订单项 - 使用更新的选择器
            order_items = self.driver.find_elements(By.CSS_SELECTOR, ".puwidth[data-v-14224f5e]")

            # 如果没找到，尝试备用选择器
            if not order_items:
                logger.info("使用备用选择器查找订单项")
                order_items = self.driver.find_elements(By.CSS_SELECTOR, ".puwidth")
            logger.info(f"找到 {len(order_items)} 个订单项")

            # 遍历订单项，查找匹配的订单ID
            for item in order_items:
                try:
                    # 获取订单ID
                    item_id_element = item.find_element(By.XPATH, ".//uni-view[contains(text(), '订单号')]")
                    if item_id_element:
                        item_id_text = item_id_element.text
                        # 提取订单ID
                        match = re.search(r'订单号[：:]\s*([A-Z0-9]+)', item_id_text)
                        if match:
                            item_id = match.group(1)
                            if item_id == order_id:
                                logger.info(f"找到匹配订单: {order_id}")

                                # 提取订单信息
                                order_info = {
                                    "id": order_id,
                                    "element": item
                                }

                                # 尝试提取更多信息
                                try:
                                    # 提取开仓价格
                                    open_price_element = item.find_element(By.CSS_SELECTOR, self.selectors["open_price"])
                                    if open_price_element:
                                        order_info["open_price"] = open_price_element.text.strip()

                                    # 提取实时平仓价格
                                    try:
                                        close_price_element = item.find_element(By.CSS_SELECTOR, self.selectors["close_price_red"])
                                        order_info["close_price"] = close_price_element.text.strip()
                                        order_info["price_color"] = "red"
                                    except NoSuchElementException:
                                        try:
                                            close_price_element = item.find_element(By.CSS_SELECTOR, self.selectors["close_price_green"])
                                            order_info["close_price"] = close_price_element.text.strip()
                                            order_info["price_color"] = "green"
                                        except NoSuchElementException:
                                            pass
                                except Exception as info_error:
                                    logger.warning(f"提取订单详细信息失败: {info_error}")

                                return order_info
                except Exception as item_error:
                    logger.warning(f"处理订单项时出错: {item_error}")
                    continue

            logger.warning(f"未找到匹配订单: {order_id}")
            return None

        except Exception as e:
            logger.error(f"查找订单失败: {e}")
            return None

    async def close_position(self, order_id: str, use_default_settlement: bool = False) -> Dict[str, Any]:
        """
        平仓操作

        Args:
            order_id: 订单ID
            use_default_settlement: 是否优先使用违约结算，默认为False（使用正常结料）。
                                    如果设置为True，将先尝试违约结算，如果找不到违约结算按钮，再尝试结料按钮。

        Returns:
            平仓结果
        """
        try:
            logger.info(f"开始平仓操作: 订单ID={order_id}, 使用违约结算={use_default_settlement}")

            # 导航到订单页面
            nav_success = await self.navigate_to_order_page()
            if not nav_success:
                return {
                    "success": False,
                    "message": "导航到订单页面失败"
                }

            # 查找订单
            order_info = await self.find_order_by_id(order_id)
            if not order_info:
                return {
                    "success": False,
                    "message": f"未找到订单: {order_id}"
                }

            # 点击平仓按钮
            close_result = await self._click_close_button(order_info, use_default_settlement)
            if not close_result["success"]:
                return close_result

            # 处理平仓弹窗
            confirm_result = await self._handle_close_popup(order_id)

            return confirm_result

        except Exception as e:
            logger.error(f"平仓操作失败: {e}")
            # 截图记录错误
            self._save_screenshot(f"close_position_error_{order_id}")
            return {
                "success": False,
                "message": f"平仓操作失败: {str(e)}"
            }

    async def _click_close_button(self, order_info: Dict[str, Any], use_default_settlement: bool) -> Dict[str, Any]:
        """
        点击平仓按钮

        Args:
            order_info: 订单信息
            use_default_settlement: 是否优先使用违约结算

        Returns:
            操作结果
        """
        try:
            order_element = order_info["element"]

            # 如果优先使用违约结算，先尝试违约结算按钮，如果找不到再尝试结料按钮
            if use_default_settlement:
                # 先尝试违约结算按钮
                try:
                    logger.info("尝试点击违约结算按钮")
                    close_button = order_element.find_element(By.CSS_SELECTOR, self.selectors["default_button"])
                    button_type = "违约结算"
                except NoSuchElementException:
                    try:
                        logger.warning("未找到违约结算按钮，尝试使用XPath查找")
                        xpath = f"//uni-view[contains(@class, '{self.selectors['default_button'].replace('.', '')}')]"
                        close_button = order_element.find_element(By.XPATH, xpath)
                        button_type = "违约结算"
                    except NoSuchElementException:
                        logger.warning("未找到违约结算按钮，尝试使用结料按钮")
                        # 如果找不到违约结算按钮，尝试结料按钮
                        try:
                            close_button = order_element.find_element(By.CSS_SELECTOR, self.selectors["feed_button"])
                            button_type = "结料"
                        except NoSuchElementException:
                            try:
                                xpath = f"//uni-view[contains(@class, '{self.selectors['feed_button'].replace('.', '')}')]"
                                close_button = order_element.find_element(By.XPATH, xpath)
                                button_type = "结料"
                            except NoSuchElementException:
                                logger.error("未找到违约结算按钮和结料按钮")
                                return {
                                    "success": False,
                                    "message": "未找到违约结算按钮和结料按钮"
                                }
            else:
                # 直接使用结料按钮
                button_type = "结料"
                try:
                    logger.info("尝试点击结料按钮")
                    close_button = order_element.find_element(By.CSS_SELECTOR, self.selectors["feed_button"])
                except NoSuchElementException:
                    logger.warning("未找到结料按钮，尝试使用XPath查找")
                    try:
                        xpath = f"//uni-view[contains(@class, '{self.selectors['feed_button'].replace('.', '')}')]"
                        close_button = order_element.find_element(By.XPATH, xpath)
                    except NoSuchElementException:
                        logger.error("未找到结料按钮")
                        return {
                            "success": False,
                            "message": "未找到结料按钮"
                        }

            # 点击按钮
            try:
                close_button.click()
                logger.info(f"已点击{button_type}按钮")

                # 等待弹窗出现
                await asyncio.sleep(1)

                return {
                    "success": True,
                    "message": f"已点击{button_type}按钮"
                }
            except Exception as click_error:
                logger.error(f"点击{button_type}按钮失败: {click_error}")

                # 尝试使用JavaScript点击
                try:
                    self.driver.execute_script("arguments[0].click();", close_button)
                    logger.info(f"已使用JavaScript点击{button_type}按钮")

                    # 等待弹窗出现
                    await asyncio.sleep(1)

                    return {
                        "success": True,
                        "message": f"已使用JavaScript点击{button_type}按钮"
                    }
                except Exception as js_error:
                    logger.error(f"JavaScript点击{button_type}按钮失败: {js_error}")
                    return {
                        "success": False,
                        "message": f"点击{button_type}按钮失败"
                    }

        except Exception as e:
            logger.error(f"点击平仓按钮失败: {e}")
            return {
                "success": False,
                "message": f"点击平仓按钮失败: {str(e)}"
            }

    async def _handle_close_popup(self, order_id: str) -> Dict[str, Any]:
        """
        处理平仓弹窗

        Args:
            order_id: 订单ID

        Returns:
            操作结果
        """
        try:
            logger.info("处理平仓弹窗")

            # 等待弹窗出现
            try:
                WebDriverWait(self.driver, self.timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".popcont[data-v-9a81d21c]"))
                )
                logger.info("平仓弹窗已出现")
            except TimeoutException:
                logger.warning("等待平仓弹窗出现超时")
                return {
                    "success": False,
                    "message": "等待平仓弹窗出现超时"
                }

            # 截图记录弹窗
            self._save_screenshot(f"close_popup_{order_id}")

            # 点击确认按钮
            try:
                # 查找确认按钮
                confirm_button = self.driver.find_element(By.CSS_SELECTOR, self.selectors["settle_button"])

                # 点击确认按钮
                confirm_button.click()
                logger.info("已点击平仓确认按钮")

                # 等待操作完成
                await asyncio.sleep(2)

                # 验证平仓是否成功
                success = await self._verify_close_success(order_id)

                if success:
                    logger.info(f"平仓操作成功确认: 订单ID={order_id}")
                    return {
                        "success": True,
                        "message": "平仓操作成功",
                        "close_time": datetime.now().isoformat()
                    }
                else:
                    logger.warning(f"平仓操作可能未成功: 订单ID={order_id}")
                    return {
                        "success": False,
                        "message": "平仓操作可能未成功，未能确认结果"
                    }
            except NoSuchElementException:
                logger.warning("未找到平仓确认按钮，尝试使用XPath查找")
                try:
                    xpath = "//uni-view[contains(@class, 'back2') and contains(@data-v-9a81d21c, '')]"
                    confirm_button = self.driver.find_element(By.XPATH, xpath)

                    # 点击确认按钮
                    confirm_button.click()
                    logger.info("已使用XPath点击平仓确认按钮")

                    # 等待操作完成
                    await asyncio.sleep(2)

                    # 验证平仓是否成功
                    success = await self._verify_close_success(order_id)

                    if success:
                        logger.info(f"平仓操作成功确认: 订单ID={order_id}")
                        return {
                            "success": True,
                            "message": "平仓操作成功",
                            "close_time": datetime.now().isoformat()
                        }
                    else:
                        logger.warning(f"平仓操作可能未成功: 订单ID={order_id}")
                        return {
                            "success": False,
                            "message": "平仓操作可能未成功，未能确认结果"
                        }
                except Exception as xpath_error:
                    logger.error(f"使用XPath查找并点击确认按钮失败: {xpath_error}")
                    return {
                        "success": False,
                        "message": "未找到平仓确认按钮"
                    }
            except Exception as click_error:
                logger.error(f"点击平仓确认按钮失败: {click_error}")
                return {
                    "success": False,
                    "message": f"点击平仓确认按钮失败: {str(click_error)}"
                }

        except Exception as e:
            logger.error(f"处理平仓弹窗失败: {e}")
            return {
                "success": False,
                "message": f"处理平仓弹窗失败: {str(e)}"
            }

    async def _verify_close_success(self, order_id: str) -> bool:
        """
        验证平仓是否成功

        Args:
            order_id: 订单ID

        Returns:
            是否成功
        """
        try:
            # 检查是否有成功提示
            try:
                success_element = WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, "//uni-view[contains(text(), '成功') or contains(text(), '已结算')]"))
                )
                if success_element:
                    logger.info(f"找到平仓成功提示: {success_element.text}")
                    return True
            except TimeoutException:
                logger.warning("未找到平仓成功提示")

            # 检查订单是否已从持仓列表中消失
            # 导航到订单页面
            await self.navigate_to_order_page()

            # 查找订单
            order_info = await self.find_order_by_id(order_id)
            if not order_info:
                logger.info(f"订单 {order_id} 已从持仓列表中消失，平仓可能成功")
                return True

            # 如果订单仍然存在，检查其状态
            # 这里需要根据实际页面结构实现

            logger.warning(f"订单 {order_id} 仍在持仓列表中，平仓可能失败")
            return False
        except Exception as e:
            logger.error(f"验证平仓结果失败: {e}")
            return False

    def _save_screenshot(self, name: str) -> None:
        """
        保存截图

        Args:
            name: 截图名称
        """
        try:
            screenshot_path = f"logs/{name}_{int(time.time())}.png"
            self.driver.save_screenshot(screenshot_path)
            logger.debug(f"截图已保存: {screenshot_path}")
        except Exception as e:
            logger.warning(f"保存截图失败: {e}")
