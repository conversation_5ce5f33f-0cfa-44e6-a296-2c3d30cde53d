#!/usr/bin/env python3
"""
现货平台选择器优化配置

基于HTML结构分析，提供更稳定的选择器策略，避免依赖易变的data-v属性
"""

class SpotSelectorsOptimized:
    """优化的现货平台选择器配置"""
    
    def __init__(self):
        """初始化选择器配置"""
        
        # 基于HTML结构分析的稳定选择器
        self.selectors = {
            # ===== 订单容器选择器 =====
            # 基于语义化类名，更稳定
            "order_container": ".puwidth",  # 订单容器
            "order_detail": ".deposit-bottom",  # 订单详情区域
            "order_top": ".order-top",  # 订单顶部区域
            
            # ===== 订单信息选择器 =====
            # 基于语义化类名和内容
            "order_sn": ".order-sn",  # 订单号容器：包含"订单号:S2025053002173230988"
            "order_payment": ".order-payment",  # 货款信息：包含"货款:769100.00"
            
            # ===== 价格选择器 =====
            # 基于语义化类名，稳定性高
            "open_price": ".per-price",  # 开仓价格：769.10
            "current_price": ".new-price",  # 当前价格
            "price_red": ".color1",  # 红色价格（亏损/高价）
            "price_green": ".color2",  # 绿色价格（盈利/低价）
            
            # ===== 操作按钮选择器 =====
            # 基于语义化类名
            "button_container": ".order-button",  # 按钮容器
            "deposit_right": ".deposit-right",  # 右侧操作区域
            
            # 具体按钮（不依赖data-v属性）
            "feed_button": ".feedingback",  # 结料按钮
            "default_settlement": ".back1",  # 违约结算按钮
            "close_confirm": ".back2",  # 平仓确认按钮
            "close_cancel": ".cancelbtn",  # 平仓取消按钮
            
            # ===== 弹窗选择器 =====
            "popup_container": ".popcont",  # 弹窗容器
            "popup_info": ".boxinfo",  # 弹窗信息区域
            
            # ===== 账户操作选择器 =====
            "deposit_button": ".auth-button",  # 退/付定金按钮
            "batch_button": ".batch-button",  # 批量操作按钮
        }
        
        # 多层级备用选择器策略
        self.fallback_selectors = {
            # 订单容器备用策略
            "order_container": [
                ".puwidth",  # 主选择器
                ".orderlistbox.puwidth",  # 带父级的选择器
                "uni-view.puwidth",  # 带标签的选择器
                "[class*='puwidth']",  # 包含类名的选择器
            ],
            
            # 订单详情备用策略
            "order_detail": [
                ".deposit-bottom",
                ".puwidth .deposit-bottom",
                "uni-view.deposit-bottom",
                "[class*='deposit-bottom']",
            ],
            
            # 订单号备用策略
            "order_sn": [
                ".order-sn",  # 主选择器
                "uni-view:has-text('订单号')",  # 基于文本内容
                "//uni-view[contains(text(), '订单号')]",  # XPath选择器
                ".deposit-box:has-text('订单号')",  # 父容器+文本
            ],
            
            # 价格选择器备用策略
            "open_price": [
                ".per-price",
                ".textbox .per-price",
                "uni-view.per-price",
                "[class*='per-price']",
            ],
            
            "current_price": [
                ".new-price",
                ".textbox .new-price", 
                "uni-view.new-price",
                "[class*='new-price']",
            ],
            
            # 按钮备用策略
            "feed_button": [
                ".feedingback",
                ".order-button .feedingback",
                "uni-view.feedingback",
                "//uni-view[contains(text(), '结料')]",
            ],
            
            "default_settlement": [
                ".back1",
                ".order-button .back1",
                "uni-view.back1",
                "//uni-view[contains(text(), '违约结算')]",
            ],
        }
        
        # 文本内容匹配选择器
        self.text_selectors = {
            "order_id_text": "//uni-view[contains(text(), '订单号')]",
            "payment_text": "//uni-view[contains(text(), '货款')]",
            "deposit_text": "//uni-view[contains(text(), '定金')]",
            "feed_button_text": "//uni-view[contains(text(), '结料')]",
            "settlement_text": "//uni-view[contains(text(), '违约结算')]",
            "confirm_text": "//uni-view[contains(text(), '确认')]",
            "cancel_text": "//uni-view[contains(text(), '取消')]",
        }
        
        # 组合选择器（多个条件）
        self.combined_selectors = {
            # 订单容器：类名+结构
            "order_with_id": ".puwidth:has(.order-sn)",
            "order_with_price": ".puwidth:has(.per-price)",
            
            # 按钮：容器+类名
            "feed_in_container": ".order-button .feedingback",
            "settlement_in_container": ".order-button .back1",
            
            # 价格：容器+类名
            "price_in_textbox": ".textbox .per-price, .textbox .new-price",
            "colored_price": ".color1, .color2",
        }
    
    def get_selector(self, key: str, use_fallback: bool = False) -> str:
        """
        获取选择器
        
        Args:
            key: 选择器键名
            use_fallback: 是否使用备用选择器
            
        Returns:
            选择器字符串
        """
        if use_fallback and key in self.fallback_selectors:
            return self.fallback_selectors[key][0]  # 返回第一个备用选择器
        
        return self.selectors.get(key, "")
    
    def get_all_fallbacks(self, key: str) -> list:
        """
        获取所有备用选择器
        
        Args:
            key: 选择器键名
            
        Returns:
            备用选择器列表
        """
        return self.fallback_selectors.get(key, [])
    
    def get_text_selector(self, key: str) -> str:
        """
        获取文本匹配选择器
        
        Args:
            key: 选择器键名
            
        Returns:
            XPath选择器字符串
        """
        return self.text_selectors.get(key, "")
    
    def get_combined_selector(self, key: str) -> str:
        """
        获取组合选择器
        
        Args:
            key: 选择器键名
            
        Returns:
            组合选择器字符串
        """
        return self.combined_selectors.get(key, "")


# 全局实例
optimized_selectors = SpotSelectorsOptimized()


def get_robust_selector_strategy(element_type: str) -> dict:
    """
    获取健壮的选择器策略
    
    Args:
        element_type: 元素类型
        
    Returns:
        选择器策略字典
    """
    strategies = {
        "order_container": {
            "primary": ".puwidth",
            "fallbacks": [".orderlistbox.puwidth", "uni-view.puwidth"],
            "text_based": "//uni-view[contains(@class, 'puwidth')]",
            "description": "订单容器选择器"
        },
        
        "order_id": {
            "primary": ".order-sn",
            "fallbacks": ["//uni-view[contains(text(), '订单号')]"],
            "text_based": "//uni-view[contains(text(), '订单号')]",
            "description": "订单号选择器"
        },
        
        "open_price": {
            "primary": ".per-price",
            "fallbacks": [".textbox .per-price", "uni-view.per-price"],
            "text_based": "//uni-view[contains(@class, 'per-price')]",
            "description": "开仓价格选择器"
        },
        
        "current_price": {
            "primary": ".new-price",
            "fallbacks": [".textbox .new-price", "uni-view.new-price"],
            "text_based": "//uni-view[contains(@class, 'new-price')]",
            "description": "当前价格选择器"
        },
        
        "feed_button": {
            "primary": ".feedingback",
            "fallbacks": [".order-button .feedingback", "//uni-view[contains(text(), '结料')]"],
            "text_based": "//uni-view[contains(text(), '结料')]",
            "description": "结料按钮选择器"
        }
    }
    
    return strategies.get(element_type, {})


# 使用示例和测试函数
def test_selectors():
    """测试选择器配置"""
    selectors = SpotSelectorsOptimized()
    
    print("=== 优化选择器配置测试 ===")
    print(f"订单容器: {selectors.get_selector('order_container')}")
    print(f"订单号: {selectors.get_selector('order_sn')}")
    print(f"开仓价格: {selectors.get_selector('open_price')}")
    print(f"结料按钮: {selectors.get_selector('feed_button')}")
    
    print("\n=== 备用选择器 ===")
    print(f"订单容器备用: {selectors.get_all_fallbacks('order_container')}")
    print(f"订单号备用: {selectors.get_all_fallbacks('order_sn')}")
    
    print("\n=== 文本选择器 ===")
    print(f"订单号文本: {selectors.get_text_selector('order_id_text')}")
    print(f"结料按钮文本: {selectors.get_text_selector('feed_button_text')}")


if __name__ == "__main__":
    test_selectors()
