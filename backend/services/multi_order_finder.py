#!/usr/bin/env python3
"""
多订单查找器

基于实际HTML结构分析，提供准确的多订单查找功能
"""

import re
import logging
from typing import Optional, Dict, Any, List
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

logger = logging.getLogger(__name__)


class MultiOrderFinder:
    """多订单查找器"""
    
    def __init__(self, driver, timeout: int = 10):
        """
        初始化多订单查找器
        
        Args:
            driver: WebDriver实例
            timeout: 超时时间（秒）
        """
        self.driver = driver
        self.timeout = timeout
        
        # 基于实际HTML结构的选择器
        self.selectors = {
            # 订单容器选择器（每个订单的根容器）
            "order_containers": ".puwidth",
            
            # 订单详情容器
            "order_detail": ".deposit-bottom",
            
            # 订单号选择器（在订单详情容器内）
            "order_sn": ".order-sn",
            
            # 价格相关选择器
            "open_price": ".per-price",  # 开仓价格
            "current_price": ".new-price",  # 当前价格
            "price_red": ".color1",  # 红色价格
            "price_green": ".color2",  # 绿色价格
            
            # 操作按钮选择器
            "feed_button": ".feedingback",  # 结料按钮
            "default_settlement": ".back1",  # 违约结算按钮
            "order_buttons": ".deposit-right",  # 按钮容器
        }
    
    def find_all_orders(self) -> List[Dict[str, Any]]:
        """
        查找页面上的所有订单
        
        Returns:
            订单信息列表
        """
        try:
            logger.info("开始查找页面上的所有订单")
            
            # 等待页面加载
            WebDriverWait(self.driver, self.timeout).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, self.selectors["order_containers"]))
            )
            
            # 获取所有订单容器
            order_containers = self.driver.find_elements(By.CSS_SELECTOR, self.selectors["order_containers"])
            logger.info(f"找到 {len(order_containers)} 个订单容器")
            
            orders = []
            for i, container in enumerate(order_containers):
                try:
                    order_info = self._extract_order_info_from_container(container, i + 1)
                    if order_info:
                        orders.append(order_info)
                        logger.info(f"成功提取第 {i + 1} 个订单信息: {order_info.get('order_id', 'Unknown')}")
                    else:
                        logger.warning(f"第 {i + 1} 个订单容器信息提取失败")
                except Exception as e:
                    logger.warning(f"处理第 {i + 1} 个订单容器时出错: {e}")
                    continue
            
            logger.info(f"成功提取 {len(orders)} 个订单信息")
            return orders
            
        except Exception as e:
            logger.error(f"查找所有订单失败: {e}")
            return []
    
    def find_order_by_id(self, target_order_id: str) -> Optional[Dict[str, Any]]:
        """
        根据订单ID查找特定订单
        
        Args:
            target_order_id: 目标订单ID
            
        Returns:
            订单信息，如果未找到则返回None
        """
        try:
            logger.info(f"查找特定订单: {target_order_id}")
            
            # 获取所有订单
            all_orders = self.find_all_orders()
            
            # 查找匹配的订单
            for order in all_orders:
                if order.get("order_id") == target_order_id:
                    logger.info(f"找到匹配订单: {target_order_id}")
                    return order
            
            logger.warning(f"未找到匹配订单: {target_order_id}")
            return None
            
        except Exception as e:
            logger.error(f"查找特定订单失败: {e}")
            return None
    
    def _extract_order_info_from_container(self, container, order_index: int) -> Optional[Dict[str, Any]]:
        """
        从订单容器中提取订单信息
        
        Args:
            container: 订单容器元素
            order_index: 订单索引（用于调试）
            
        Returns:
            订单信息字典
        """
        try:
            order_info = {
                "container_element": container,
                "order_index": order_index
            }
            
            # 1. 提取订单号
            order_id = self._extract_order_id(container)
            if not order_id:
                logger.warning(f"第 {order_index} 个订单容器未能提取到订单号")
                return None
            
            order_info["order_id"] = order_id
            
            # 2. 提取价格信息
            price_info = self._extract_price_info(container)
            order_info.update(price_info)
            
            # 3. 提取按钮信息
            button_info = self._extract_button_info(container)
            order_info.update(button_info)
            
            # 4. 提取其他信息
            other_info = self._extract_other_info(container)
            order_info.update(other_info)
            
            return order_info
            
        except Exception as e:
            logger.warning(f"从第 {order_index} 个订单容器提取信息失败: {e}")
            return None
    
    def _extract_order_id(self, container) -> Optional[str]:
        """提取订单ID"""
        try:
            # 策略1: 使用订单号选择器
            try:
                order_sn_element = container.find_element(By.CSS_SELECTOR, self.selectors["order_sn"])
                order_text = order_sn_element.text.strip()
                logger.debug(f"订单号元素文本: {order_text}")
                
                # 提取订单ID
                match = re.search(r'订单号[：:]\s*([A-Z0-9]+)', order_text)
                if match:
                    return match.group(1)
            except NoSuchElementException:
                logger.debug("使用订单号选择器未找到元素")
            
            # 策略2: 使用XPath查找包含"订单号"的元素
            try:
                order_element = container.find_element(By.XPATH, ".//uni-view[contains(text(), '订单号')]")
                order_text = order_element.text.strip()
                logger.debug(f"XPath查找订单号文本: {order_text}")
                
                match = re.search(r'订单号[：:]\s*([A-Z0-9]+)', order_text)
                if match:
                    return match.group(1)
            except NoSuchElementException:
                logger.debug("使用XPath未找到订单号元素")
            
            # 策略3: 在整个容器文本中搜索
            try:
                container_text = container.text
                match = re.search(r'订单号[：:]\s*([A-Z0-9]+)', container_text)
                if match:
                    logger.debug(f"在容器文本中找到订单号: {match.group(1)}")
                    return match.group(1)
            except Exception:
                pass
            
            logger.debug("所有订单ID提取策略都失败")
            return None
            
        except Exception as e:
            logger.warning(f"提取订单ID失败: {e}")
            return None
    
    def _extract_price_info(self, container) -> Dict[str, Any]:
        """提取价格信息"""
        price_info = {}
        
        try:
            # 提取开仓价格
            try:
                open_price_element = container.find_element(By.CSS_SELECTOR, self.selectors["open_price"])
                price_info["open_price"] = open_price_element.text.strip()
                logger.debug(f"开仓价格: {price_info['open_price']}")
            except NoSuchElementException:
                logger.debug("未找到开仓价格")
            
            # 提取当前价格
            try:
                current_price_element = container.find_element(By.CSS_SELECTOR, self.selectors["current_price"])
                price_info["current_price"] = current_price_element.text.strip()
                logger.debug(f"当前价格: {price_info['current_price']}")
            except NoSuchElementException:
                logger.debug("未找到当前价格")
            
            # 提取红色价格
            try:
                red_price_elements = container.find_elements(By.CSS_SELECTOR, self.selectors["price_red"])
                if red_price_elements:
                    price_info["red_prices"] = [elem.text.strip() for elem in red_price_elements if elem.text.strip()]
                    logger.debug(f"红色价格: {price_info['red_prices']}")
            except Exception:
                pass
            
            # 提取绿色价格
            try:
                green_price_elements = container.find_elements(By.CSS_SELECTOR, self.selectors["price_green"])
                if green_price_elements:
                    price_info["green_prices"] = [elem.text.strip() for elem in green_price_elements if elem.text.strip()]
                    logger.debug(f"绿色价格: {price_info['green_prices']}")
            except Exception:
                pass
                
        except Exception as e:
            logger.warning(f"提取价格信息失败: {e}")
        
        return price_info
    
    def _extract_button_info(self, container) -> Dict[str, Any]:
        """提取按钮信息"""
        button_info = {}
        
        try:
            available_buttons = []
            
            # 检查结料按钮
            try:
                feed_button = container.find_element(By.CSS_SELECTOR, self.selectors["feed_button"])
                if feed_button.is_displayed():
                    available_buttons.append("feed_button")
                    button_info["feed_button_element"] = feed_button
            except NoSuchElementException:
                pass
            
            # 检查违约结算按钮
            try:
                default_button = container.find_element(By.CSS_SELECTOR, self.selectors["default_settlement"])
                if default_button.is_displayed():
                    available_buttons.append("default_settlement")
                    button_info["default_settlement_element"] = default_button
            except NoSuchElementException:
                pass
            
            button_info["available_buttons"] = available_buttons
            logger.debug(f"可用按钮: {available_buttons}")
            
        except Exception as e:
            logger.warning(f"提取按钮信息失败: {e}")
        
        return button_info
    
    def _extract_other_info(self, container) -> Dict[str, Any]:
        """提取其他信息"""
        other_info = {}
        
        try:
            # 提取货款信息
            try:
                payment_element = container.find_element(By.XPATH, ".//uni-view[contains(text(), '货款')]")
                other_info["payment_info"] = payment_element.text.strip()
                logger.debug(f"货款信息: {other_info['payment_info']}")
            except NoSuchElementException:
                logger.debug("未找到货款信息")
            
            # 提取定金信息
            try:
                deposit_elements = container.find_elements(By.XPATH, ".//uni-view[contains(text(), '定金')]")
                if deposit_elements:
                    other_info["deposit_info"] = [elem.text.strip() for elem in deposit_elements]
                    logger.debug(f"定金信息: {other_info['deposit_info']}")
            except Exception:
                pass
            
            # 获取容器的HTML（用于调试）
            try:
                other_info["container_html"] = container.get_attribute("outerHTML")[:500] + "..."
            except Exception:
                pass
                
        except Exception as e:
            logger.warning(f"提取其他信息失败: {e}")
        
        return other_info
    
    def get_order_summary(self) -> Dict[str, Any]:
        """
        获取订单摘要信息
        
        Returns:
            订单摘要
        """
        try:
            all_orders = self.find_all_orders()
            
            summary = {
                "total_orders": len(all_orders),
                "order_ids": [order.get("order_id") for order in all_orders if order.get("order_id")],
                "orders_with_feed_button": len([order for order in all_orders if "feed_button" in order.get("available_buttons", [])]),
                "orders_with_settlement_button": len([order for order in all_orders if "default_settlement" in order.get("available_buttons", [])]),
            }
            
            logger.info(f"订单摘要: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"获取订单摘要失败: {e}")
            return {}


def test_multi_order_finder():
    """测试多订单查找器"""
    print("=== 多订单查找器测试 ===")
    
    # 这里需要实际的WebDriver实例进行测试
    # finder = MultiOrderFinder(driver)
    # 
    # # 测试查找所有订单
    # all_orders = finder.find_all_orders()
    # print(f"找到 {len(all_orders)} 个订单")
    # 
    # # 测试查找特定订单
    # target_order = "S2025053001424825524"
    # order = finder.find_order_by_id(target_order)
    # print(f"查找订单 {target_order}: {'找到' if order else '未找到'}")
    # 
    # # 获取订单摘要
    # summary = finder.get_order_summary()
    # print(f"订单摘要: {summary}")
    
    print("测试完成")


if __name__ == "__main__":
    test_multi_order_finder()
