#!/usr/bin/env python3
"""
现货平台最终选择器配置

基于测试验证的稳定选择器，可直接投入生产使用
"""

class SpotSelectorsFinal:
    """最终验证的现货平台选择器配置"""
    
    def __init__(self):
        """初始化选择器配置"""
        
        # ===== 经过测试验证的稳定选择器 =====
        self.STABLE_SELECTORS = {
            # 订单容器（100%有效）
            "order_container": ".puwidth",
            "order_detail": ".deposit-bottom",
            
            # 订单信息（100%有效）
            "order_sn": ".order-sn",  # 订单号：S2025053002173230988
            "order_payment": ".order-payment",  # 货款信息
            
            # 价格信息（100%有效）
            "open_price": ".per-price",  # 开仓价格：769.10
            "current_price": ".new-price",  # 当前价格：766.29
            "price_red": ".color1",  # 红色价格（亏损）
            "price_green": ".color2",  # 绿色价格（盈利）
            
            # 操作按钮（100%有效）
            "feed_button": ".feedingback",  # 结料按钮
            "default_settlement": ".back1",  # 违约结算按钮
            "close_confirm": ".back2",  # 平仓确认按钮
            "close_cancel": ".cancelbtn",  # 平仓取消按钮
            
            # 弹窗相关
            "popup_container": ".popcont",  # 弹窗容器
            "popup_info": ".boxinfo",  # 弹窗信息
            
            # 账户操作
            "deposit_button": ".auth-button",  # 退/付定金按钮
            "batch_button": ".batch-button",  # 批量操作按钮
        }
        
        # ===== 备用选择器（当主选择器失效时使用） =====
        self.FALLBACK_SELECTORS = {
            "order_container": [
                ".puwidth",
                ".orderlistbox.puwidth",
                "uni-view.puwidth",
                "[class*='puwidth']"
            ],
            "order_sn": [
                ".order-sn",
                "//uni-view[contains(text(), '订单号')]",
                ".deposit-box:contains('订单号')"
            ],
            "open_price": [
                ".per-price",
                ".textbox .per-price",
                "uni-view.per-price"
            ],
            "feed_button": [
                ".feedingback",
                ".order-button .feedingback",
                "//uni-view[contains(text(), '结料')]"
            ]
        }
        
        # ===== 文本匹配选择器（最稳定的备用方案） =====
        self.TEXT_SELECTORS = {
            "order_id_xpath": "//uni-view[contains(text(), '订单号')]",
            "payment_xpath": "//uni-view[contains(text(), '货款')]",
            "feed_button_xpath": "//uni-view[contains(text(), '结料')]",
            "settlement_xpath": "//uni-view[contains(text(), '违约结算')]",
            "confirm_xpath": "//uni-view[contains(text(), '确认')]",
            "cancel_xpath": "//uni-view[contains(text(), '取消')]"
        }
    
    def get_selector(self, key: str) -> str:
        """获取主选择器"""
        return self.STABLE_SELECTORS.get(key, "")
    
    def get_fallback_selectors(self, key: str) -> list:
        """获取备用选择器列表"""
        return self.FALLBACK_SELECTORS.get(key, [])
    
    def get_text_selector(self, key: str) -> str:
        """获取文本匹配选择器"""
        return self.TEXT_SELECTORS.get(key, "")


# ===== 实用工具函数 =====

def extract_order_id_from_text(text: str) -> str:
    """从文本中提取订单ID"""
    import re
    match = re.search(r'订单号[：:]\s*([A-Z0-9]+)', text)
    return match.group(1) if match else ""

def extract_price_from_text(text: str) -> float:
    """从文本中提取价格"""
    import re
    match = re.search(r'(\d+\.?\d*)', text.strip())
    return float(match.group(1)) if match else 0.0

def is_order_match(order_text: str, target_order_id: str) -> bool:
    """检查订单文本是否匹配目标订单ID"""
    extracted_id = extract_order_id_from_text(order_text)
    return extracted_id == target_order_id


# ===== 选择器使用示例 =====

class OrderSelectorExample:
    """选择器使用示例"""
    
    def __init__(self, driver):
        self.driver = driver
        self.selectors = SpotSelectorsFinal()
    
    def find_order_container(self):
        """查找订单容器的示例"""
        # 1. 尝试主选择器
        try:
            container = self.driver.find_element("css", self.selectors.get_selector("order_container"))
            return container
        except:
            pass
        
        # 2. 尝试备用选择器
        for fallback in self.selectors.get_fallback_selectors("order_container"):
            try:
                if fallback.startswith("//"):
                    container = self.driver.find_element("xpath", fallback)
                else:
                    container = self.driver.find_element("css", fallback)
                return container
            except:
                continue
        
        return None
    
    def find_order_by_id(self, target_order_id: str):
        """根据订单ID查找订单的示例"""
        # 1. 找到所有订单容器
        containers = self.driver.find_elements("css", self.selectors.get_selector("order_container"))
        
        # 2. 遍历容器查找匹配的订单
        for container in containers:
            try:
                # 尝试使用订单号选择器
                order_sn_element = container.find_element("css", self.selectors.get_selector("order_sn"))
                order_text = order_sn_element.text
                
                if is_order_match(order_text, target_order_id):
                    return container
                    
            except:
                # 备用方案：使用XPath文本匹配
                try:
                    order_element = container.find_element("xpath", self.selectors.get_text_selector("order_id_xpath"))
                    order_text = order_element.text
                    
                    if is_order_match(order_text, target_order_id):
                        return container
                except:
                    continue
        
        return None
    
    def extract_order_info(self, container):
        """从订单容器提取信息的示例"""
        info = {}
        
        # 提取订单号
        try:
            order_sn = container.find_element("css", self.selectors.get_selector("order_sn"))
            info["order_id"] = extract_order_id_from_text(order_sn.text)
        except:
            pass
        
        # 提取开仓价格
        try:
            open_price = container.find_element("css", self.selectors.get_selector("open_price"))
            info["open_price"] = extract_price_from_text(open_price.text)
        except:
            pass
        
        # 提取当前价格
        try:
            current_price = container.find_element("css", self.selectors.get_selector("current_price"))
            info["current_price"] = extract_price_from_text(current_price.text)
        except:
            pass
        
        return info
    
    def find_action_button(self, container, button_type: str):
        """查找操作按钮的示例"""
        button_selector = self.selectors.get_selector(f"{button_type}_button")
        
        # 1. 尝试主选择器
        try:
            button = container.find_element("css", button_selector)
            if button.is_displayed():
                return button
        except:
            pass
        
        # 2. 尝试文本匹配
        text_selector = self.selectors.get_text_selector(f"{button_type}_xpath")
        if text_selector:
            try:
                button = container.find_element("xpath", text_selector)
                if button.is_displayed():
                    return button
            except:
                pass
        
        return None


# ===== 全局实例 =====
final_selectors = SpotSelectorsFinal()


# ===== 测试函数 =====
def test_final_selectors():
    """测试最终选择器配置"""
    selectors = SpotSelectorsFinal()
    
    print("=== 最终选择器配置测试 ===")
    print("主要选择器:")
    for key, selector in selectors.STABLE_SELECTORS.items():
        print(f"  {key}: {selector}")
    
    print("\n备用选择器:")
    for key, fallbacks in selectors.FALLBACK_SELECTORS.items():
        print(f"  {key}: {len(fallbacks)} 个备用选择器")
    
    print("\n文本选择器:")
    for key, xpath in selectors.TEXT_SELECTORS.items():
        print(f"  {key}: {xpath}")
    
    print("\n工具函数测试:")
    test_text = "订单号：S2025053002173230988"
    order_id = extract_order_id_from_text(test_text)
    print(f"  提取订单ID: {order_id}")
    
    test_price = "769.10"
    price = extract_price_from_text(test_price)
    print(f"  提取价格: {price}")


if __name__ == "__main__":
    test_final_selectors()
