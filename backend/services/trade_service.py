"""
交易服务模块

该模块提供了黄金套利交易的核心功能，包括：
- 开仓和平仓操作
- 自动交易
- 交易频率限制
- 错误处理和日志记录

主要类:
    TradeService: 提供交易相关的核心功能

配置:
    TRADE_RATE_LIMIT: 每分钟最大交易次数，默认5次
    RATE_LIMIT_WINDOW: 交易频率限制时间窗口(秒)，默认60秒

异常:
    ValueError: 交易参数错误或交易失败时抛出
    TimeoutError: 获取交易锁超时时抛出
"""
import asyncio
import config
import logging
import os
import sys
import time
import traceback
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any

from fastapi import HTTPException
from pydantic import BaseModel

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_storage.db_manager import db_manager
from models.order import OrderCreate, OrderUpdate, TradeResponse
from services.market_service import MarketService
from services.order_service import OrderService
from services.settings_service import SettingsService
# 只使用spot_service_manager，避免导入冲突
from services.spot_service_manager import spot_service_manager
from services.websocket_service import websocket_service
from utils.logger import setup_logger

# 设置日志
# Determine project root for absolute log path
# __file__ is /gold/backend/services/trade_service.py
# os.path.dirname(__file__) is /gold/backend/services
# os.path.dirname(os.path.dirname(__file__)) is /gold/backend
# os.path.dirname(os.path.dirname(os.path.dirname(__file__))) is /gold
PROJECT_ROOT_FOR_LOGS = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
LOG_FILE_PATH = os.path.join(PROJECT_ROOT_FOR_LOGS, "logs", "trade_service_actual.log")

# 确保日志目录存在
os.makedirs(os.path.dirname(LOG_FILE_PATH), exist_ok=True)

# 设置控制台日志处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

# 设置文件日志处理器
file_handler = logging.FileHandler(LOG_FILE_PATH)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

# 创建日志记录器
logger = logging.getLogger("trade_service_actual")
logger.setLevel(logging.DEBUG)  # 设置为DEBUG级别，记录所有日志
logger.addHandler(console_handler)
logger.addHandler(file_handler)

# 确保日志记录器不会传播到父记录器
logger.propagate = False

logger.info("交易服务日志初始化完成，日志文件路径: %s", LOG_FILE_PATH)

class TradeService:
    """
    交易服务类

    提供黄金套利交易的核心功能，包括开仓、平仓和自动交易。
    使用用户锁机制防止并发操作，并实现交易频率限制。
    """

    def __init__(self):
        """
        初始化交易服务

        初始化交易服务所需的锁、配置和状态。
        """
        # 用户交易锁，确保同一用户的交易操作不会并发执行
        self._user_locks = {}
        # 用户锁超时时间（秒）
        self._user_lock_timeouts = {}
        # 锁超时时间（秒）
        self.LOCK_TIMEOUT = 60
        # 全局锁，用于保护用户锁字典
        self._global_lock = asyncio.Lock()
        # 交易重试次数
        self.MAX_RETRIES = 3
        # 重试间隔（秒）
        self.RETRY_INTERVAL = 2
        # 清理任务
        self._cleanup_task = None
        # 交易频率限制器
        self._trade_limiter = {}
        # 每分钟最大交易次数
        self.TRADE_RATE_LIMIT = int(os.getenv('TRADE_RATE_LIMIT', 5))
        # 交易频率限制时间窗口（秒）
        self.RATE_LIMIT_WINDOW = int(os.getenv('RATE_LIMIT_WINDOW', 60))

    async def initialize(self):
        """
        异步初始化方法，在应用启动后调用

        启动锁清理任务和交易频率限制器清理任务。
        """
        # 启动锁清理任务
        await self._start_lock_cleanup_task()

        # 启动交易频率限制器清理任务
        asyncio.create_task(self._cleanup_trade_limiter())

        logger.info("交易服务初始化完成")

    async def _cleanup_trade_limiter(self):
        """
        清理过期的交易频率限制记录

        定期清理超过时间窗口的交易记录，防止内存泄漏。
        """
        while True:
            try:
                now = time.time()
                user_ids = list(self._trade_limiter.keys())

                for user_id in user_ids:
                    if user_id in self._trade_limiter:
                        user_limits = self._trade_limiter[user_id]
                        # 清理过期的交易记录
                        user_limits['timestamps'] = [
                            ts for ts in user_limits['timestamps']
                            if now - ts < self.RATE_LIMIT_WINDOW
                        ]

                        # 如果没有交易记录，删除用户限制器
                        if not user_limits['timestamps']:
                            del self._trade_limiter[user_id]

                # 每5分钟清理一次
                await asyncio.sleep(300)

            except Exception as e:
                logger.error(f"清理交易频率限制器时出错: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再重试

    async def _check_trade_rate_limit(self, user_id: str) -> bool:
        """
        检查交易频率限制

        Args:
            user_id: 用户ID

        Returns:
            bool: 如果允许交易返回True，否则返回False

        Note:
            - 使用滑动窗口算法实现交易频率限制
            - 每个用户在时间窗口内的交易次数不能超过限制
        """
        now = time.time()

        # 初始化用户限制器
        if user_id not in self._trade_limiter:
            self._trade_limiter[user_id] = {
                'timestamps': [now],
                'last_cleanup': now
            }
            return True

        user_limits = self._trade_limiter[user_id]

        # 清理过期的交易记录
        user_limits['timestamps'] = [
            ts for ts in user_limits['timestamps']
            if now - ts < self.RATE_LIMIT_WINDOW
        ]

        # 检查交易次数
        if len(user_limits['timestamps']) >= self.TRADE_RATE_LIMIT:
            logger.warning(f"用户 {user_id} 交易频率超过限制: {len(user_limits['timestamps'])}/{self.TRADE_RATE_LIMIT}")
            return False

        # 添加当前交易时间戳
        user_limits['timestamps'].append(now)
        user_limits['last_cleanup'] = now

        return True

    async def _get_user_accounts(self, user_id: str) -> Tuple[Optional[Dict], Optional[Dict]]:
        """
        获取用户账户信息（使用硬编码配置）

        Args:
            user_id: 用户ID

        Returns:
            Tuple[Optional[Dict], Optional[Dict]]: (现货账户, 期货账户)

        Raises:
            ValueError: 当账户不存在或无效时
        """
        try:
            # 使用硬编码的账户配置，所有用户共享
            spot_account = {
                "id": "SPOT337855A0",
                "account_type": "spot",
                "username": "***********",
                "password": "***********",
                "is_active": True
            }

            future_account = {
                "id": "FUTURE337855A0",
                "account_type": "future",
                "username": "801265",
                "password": "Tangxin0525",
                "contract_code": "SHFE.au2508",
                "is_active": True
            }

            logger.info(f"使用硬编码账户配置: 现货账户={spot_account['id']}, 期货账户={future_account['id']}")
            return spot_account, future_account
        except Exception as e:
            logger.error(f"获取用户账户失败: {e}")
            raise ValueError(f"获取用户账户失败: {str(e)}")

    async def _start_lock_cleanup_task(self):
        """启动锁清理任务"""
        # 确保只创建一个清理任务
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_locks())

    async def _cleanup_expired_locks(self):
        """清理过期的锁"""
        try:
            while True:
                # 等待一段时间
                await asyncio.sleep(30)  # 每30秒检查一次

                # 获取当前时间
                current_time = time.time()

                # 获取过期的锁
                expired_locks = []

                async with self._global_lock:
                    for user_id, timeout_time in list(self._user_lock_timeouts.items()):
                        if current_time > timeout_time:
                            expired_locks.append(user_id)

                # 清理过期的锁
                if expired_locks:
                    logger.warning(f"发现 {len(expired_locks)} 个过期的用户锁，准备清理")

                    async with self._global_lock:
                        for user_id in expired_locks:
                            if user_id in self._user_locks:
                                logger.warning(f"清理过期的用户锁: {user_id}")
                                del self._user_locks[user_id]
                            if user_id in self._user_lock_timeouts:
                                del self._user_lock_timeouts[user_id]
        except Exception as e:
            logger.error(f"清理过期的用户锁失败: {e}")



    async def _get_user_lock(self, user_id: str) -> asyncio.Lock:
        """
        获取用户交易锁

        Args:
            user_id: 用户ID

        Returns:
            用户交易锁

        Raises:
            TimeoutError: 获取锁超时
        """
        async with self._global_lock:
            # 检查是否有过期的锁
            current_time = time.time()
            if user_id in self._user_lock_timeouts and current_time > self._user_lock_timeouts[user_id]:
                # 锁已过期，移除旧锁
                if user_id in self._user_locks:
                    logger.warning(f"移除过期的用户锁: {user_id}")
                    del self._user_locks[user_id]
                if user_id in self._user_lock_timeouts:
                    del self._user_lock_timeouts[user_id]

            # 创建新锁或获取现有锁
            if user_id not in self._user_locks:
                self._user_locks[user_id] = asyncio.Lock()

            # 设置锁超时时间
            self._user_lock_timeouts[user_id] = time.time() + self.LOCK_TIMEOUT

            return self._user_locks[user_id]

    async def _execute_spot_order(self, account_id: str, direction: int, price: float, amount: float) -> Dict[str, Any]:
        """
        执行现货交易

        Args:
            account_id: 账户ID
            direction: 交易方向，1为买入，-1为卖出
            price: 价格
            amount: 数量（克）

        Returns:
            交易结果
        """
        start_time = time.time()
        exec_id = str(uuid.uuid4())[:8]  # 生成执行ID用于跟踪日志
        log_prefix = f"[SPOT:{exec_id}]"
        logger.info(f"{log_prefix} [现货交易开始] 账户={account_id}, 方向={'买入' if direction == 1 else '卖出'}, 参考价格={price}, 数量={amount}克")
        # 与测试案例一致的日志格式
        logger.info(f"[{exec_id}] 开始执行{'买入' if direction == 1 else '卖出'}操作: 方向={'买入' if direction == 1 else '卖出'}, 价格={price}, 数量={amount}克")

        # 确保日志目录存在
        os.makedirs("/gold/logs", exist_ok=True)

        # 配置交易日志
        trade_logger = logging.getLogger("trade_service_actual")
        if not trade_logger.handlers:
            trade_log_handler = logging.FileHandler("/gold/logs/trade_service_actual.log")
            trade_log_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            trade_logger.addHandler(trade_log_handler)
            trade_logger.setLevel(logging.DEBUG)

        try:
            # 获取现货服务实例（使用全局服务）
            logger.info(f"{log_prefix} 开始获取现货服务实例")
            from services.spot_service_manager import spot_service_manager

            spot_service = await spot_service_manager.get_service()

            if not spot_service:
                error_msg = f"{log_prefix} 获取现货服务实例失败"
                logger.error(error_msg)
                return {"success": False, "message": error_msg, "order_id": None}

            # 检查现货服务是否已登录
            if not spot_service.logged_in:
                logger.warning(f"{log_prefix} 现货服务未登录，尝试重新登录")
                login_success = await spot_service.login()
                if not login_success:
                    error_msg = f"{log_prefix} 现货服务登录失败"
                    logger.error(error_msg)
                    return {"success": False, "message": error_msg, "order_id": None}

            logger.info(f"{log_prefix} 成功获取现货服务实例，登录状态: {spot_service.logged_in}")

            # 执行现货交易，使用市价单，不再传入固定价格
            # 这里的price参数只作为参考价格，实际交易会使用平台当前的市场价格
            logger.critical(f"{log_prefix} 准备执行{'买入' if direction == 1 else '卖出'}操作，数量={amount}克")
            print(f"DEBUG: {log_prefix} 准备执行{'买入' if direction == 1 else '卖出'}操作，数量={amount}克")

            # 调用现货服务的place_order方法，设置价格滑点容忍度为0.2
            # 使用用户点击开仓传递的价格参数
            max_slippage = 0.2  # 允许0.2元的价格波动

            logger.critical(f"{log_prefix} 调用spot_service.place_order: direction={direction}, price={price}, amount={amount}, max_slippage={max_slippage}")
            print(f"DEBUG: {log_prefix} 调用spot_service.place_order: direction={direction}, price={price}, amount={amount}, max_slippage={max_slippage}")

            result = await spot_service.place_order(
                direction=direction,
                price=price,  # 使用用户传递的价格参数
                amount=amount,
                test_mode=False,  # 实际执行
                max_slippage=max_slippage  # 设置滑点容忍度为0.2
            )

            logger.critical(f"{log_prefix} spot_service.place_order返回结果: {result}")
            print(f"DEBUG: {log_prefix} spot_service.place_order返回结果: {result}")

            execution_time = time.time() - start_time

            if result and result.get("success", False):
                order_id = result.get("order_id", "unknown")
                actual_price = result.get("price", price)  # 获取实际成交价格，如果没有则使用参考价格

                logger.critical(f"{log_prefix} 现货交易成功: 订单ID={order_id}, 实际价格={actual_price}, 耗时={execution_time:.2f}秒")
                print(f"DEBUG SUCCESS: {log_prefix} 现货交易成功: 订单ID={order_id}, 实际价格={actual_price}, 耗时={execution_time:.2f}秒")

                trade_logger.info(f"现货交易成功: 账户={account_id}, 方向={'买入' if direction == 1 else '卖出'}, "
                                 f"参考价格={price}, 实际价格={actual_price}, 数量={amount}克, 订单ID={order_id}")

                return {
                    "success": True,
                    "message": "现货交易成功",
                    "order_id": order_id,
                    "price": actual_price,  # 返回实际成交价格
                    "execution_time": execution_time
                }
            else:
                error_msg = result.get("message", "未知错误") if result else "交易结果为空"
                logger.critical(f"{log_prefix} 现货交易失败: {error_msg}, 耗时={execution_time:.2f}秒")
                print(f"DEBUG ERROR: {log_prefix} 现货交易失败: {error_msg}, 耗时={execution_time:.2f}秒")

                trade_logger.error(f"现货交易失败: 账户={account_id}, 方向={'买入' if direction == 1 else '卖出'}, "
                                  f"价格={price}, 数量={amount}克, 错误={error_msg}")

                return {
                    "success": False,
                    "message": error_msg,
                    "order_id": None,
                    "execution_time": execution_time
                }
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"{log_prefix} 现货交易异常: {str(e)}"
            logger.critical(error_msg)
            logger.critical(traceback.format_exc())
            print(f"DEBUG EXCEPTION: {error_msg}")
            print(traceback.format_exc())

            trade_logger.error(f"现货交易异常: 账户={account_id}, 方向={'买入' if direction == 1 else '卖出'}, "
                              f"价格={price}, 数量={amount}克, 异常={str(e)}")

            return {
                "success": False,
                "message": error_msg,
                "order_id": None,
                "execution_time": execution_time
            }

    async def _execute_spot_close_position(self, account_id: str, order_id: str) -> Dict[str, Any]:
        """
        执行现货平仓

        Args:
            account_id: 账户ID
            order_id: 要平仓的现货订单ID

        Returns:
            平仓结果
        """
        start_time = time.time()
        exec_id = str(uuid.uuid4())[:8]  # 生成执行ID用于跟踪日志
        log_prefix = f"[SPOT_CLOSE:{exec_id}]"
        logger.info(f"{log_prefix} [现货平仓开始] 账户={account_id}, 订单ID={order_id}")

        # 确保日志目录存在
        os.makedirs("/gold/logs", exist_ok=True)

        # 配置交易日志
        trade_logger = logging.getLogger("trade_service_actual")
        if not trade_logger.handlers:
            trade_log_handler = logging.FileHandler("/gold/logs/trade_service_actual.log")
            trade_log_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            trade_logger.addHandler(trade_log_handler)
            trade_logger.setLevel(logging.DEBUG)

        try:
            # 获取现货服务实例（使用全局服务）
            from services.spot_service_manager import spot_service_manager
            spot_service = await spot_service_manager.get_service()
            if not spot_service:
                error_msg = f"{log_prefix} 获取现货服务实例失败"
                logger.error(error_msg)
                return {"success": False, "message": error_msg, "order_id": None}

            logger.info(f"{log_prefix} 成功获取现货服务实例，登录状态: {spot_service.logged_in}")

            # 执行现货平仓，使用违约结料按钮
            logger.critical(f"{log_prefix} 准备执行现货平仓操作，订单ID={order_id}")
            print(f"DEBUG: {log_prefix} 准备执行现货平仓操作，订单ID={order_id}")

            # 调用现货服务的close_position方法
            logger.critical(f"{log_prefix} 调用spot_service.close_position: order_id={order_id}")
            print(f"DEBUG: {log_prefix} 调用spot_service.close_position: order_id={order_id}")

            result = await spot_service.close_position(
                order_id=order_id,
                use_default_settlement=False  # 使用正常结料，不使用违约结算
            )

            logger.critical(f"{log_prefix} spot_service.close_position返回结果: {result}")
            print(f"DEBUG: {log_prefix} spot_service.close_position返回结果: {result}")

            execution_time = time.time() - start_time

            if result and result.get("success", False):
                close_order_id = result.get("order_id", order_id)  # 平仓可能返回相同的订单ID

                logger.critical(f"{log_prefix} 现货平仓成功: 订单ID={close_order_id}, 耗时={execution_time:.2f}秒")
                print(f"DEBUG SUCCESS: {log_prefix} 现货平仓成功: 订单ID={close_order_id}, 耗时={execution_time:.2f}秒")

                trade_logger.info(f"现货平仓成功: 账户={account_id}, 订单ID={order_id}, 平仓订单ID={close_order_id}")

                return {
                    "success": True,
                    "message": "现货平仓成功",
                    "order_id": close_order_id,
                    "execution_time": execution_time
                }
            else:
                error_msg = result.get("message", "未知错误") if result else "平仓结果为空"
                logger.error(f"{log_prefix} 现货平仓失败: {error_msg}, 耗时={execution_time:.2f}秒")
                print(f"DEBUG ERROR: {log_prefix} 现货平仓失败: {error_msg}, 耗时={execution_time:.2f}秒")

                trade_logger.error(f"现货平仓失败: 账户={account_id}, 订单ID={order_id}, 错误={error_msg}")

                return {
                    "success": False,
                    "message": error_msg,
                    "order_id": None,
                    "execution_time": execution_time
                }
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"{log_prefix} 现货平仓异常: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            print(f"DEBUG EXCEPTION: {error_msg}")
            trade_logger.error(f"现货平仓异常: 账户={account_id}, 订单ID={order_id}, 异常={str(e)}")

            return {
                "success": False,
                "message": error_msg,
                "order_id": None,
                "execution_time": execution_time
            }

    async def open_position(self, user_id: str, direction: int, size: float = None,
                           order_id: str = None, auto: bool = False,
                           trigger_basis: float = None, trigger_condition_id: str = None) -> TradeResponse:
        """
        开仓操作

        Args:
            user_id: 用户ID
            direction: 交易方向，1为正向套利，-1为反向套利
            size: 交易数量（可选，用于自动交易）
            order_id: 订单ID（可选，用于自动交易）
            auto: 是否为自动交易（可选）
            trigger_basis: 触发基差（可选，用于自动交易）
            trigger_condition_id: 触发条件ID（可选，用于自动交易）

        Returns:
            交易响应

        Raises:
            ValueError: 开仓失败
            TimeoutError: 获取锁超时
        """
        # 记录开始时间，用于计算总执行时间
        order_start_time = time.time()

        # 使用多种日志级别记录开仓请求，确保在任何日志级别下都能看到
        auto_info = f", auto={auto}, size={size}, trigger_basis={trigger_basis}" if auto else ""
        print(f"[DEBUG PRINT] Attempting to open position for user_id: {user_id}, direction: {direction}{auto_info}")
        logger.critical(f"开始执行开仓操作: user_id={user_id}, direction={direction}{auto_info}")
        logger.error(f"开仓请求接收: user_id={user_id}, direction={direction}{auto_info}")
        logger.warning(f"处理开仓请求: user_id={user_id}, direction={direction}{auto_info}")
        logger.info(f"开仓请求: user_id={user_id}, direction={direction}{auto_info}")
        logger.debug(f"开仓请求详情: user_id={user_id}, direction={direction}{auto_info}, 时间戳={datetime.now().isoformat()}")

        # 将请求信息写入专门的交易日志文件
        with open("/gold/logs/trade_requests.log", "a") as f:
            f.write(f"{datetime.now().isoformat()} - 开仓请求: user_id={user_id}, direction={direction}{auto_info}\n")

        # 参数验证
        if not user_id:
            logger.error("开仓失败: 用户ID不能为空")
            return TradeResponse(
                success=False,
                order_id="",
                message="开仓失败: 用户ID不能为空"
            )

        if direction not in [1, -1]:
            logger.error(f"开仓失败: 无效的交易方向 {direction}")
            return TradeResponse(
                success=False,
                order_id="",
                message=f"开仓失败: 无效的交易方向 {direction}"
            )

        # 检查交易频率限制
        if not await self._check_trade_rate_limit(user_id):
            return TradeResponse(
                success=False,
                order_id="",
                message=f"交易频率超过限制，请稍后再试"
            )

        # 获取用户交易锁
        try:
            user_lock = await self._get_user_lock(user_id)
        except Exception as e:
            logger.error(f"获取用户交易锁失败: {e}")
            return TradeResponse(
                success=False,
                order_id="",
                message=f"获取用户交易锁失败: {str(e)}"
            )

        # 使用锁确保同一用户的交易操作不会并发执行，添加超时机制
        lock_acquired = False
        order = None  # 初始化order变量，避免在异常处理中引用未定义的变量
        try:
            # 尝试在指定时间内获取锁
            try:
                # 使用asyncio.wait_for添加超时机制
                await asyncio.wait_for(user_lock.acquire(), timeout=10)  # 10秒超时
                lock_acquired = True
            except asyncio.TimeoutError:
                logger.error(f"获取用户交易锁超时: {user_id}")
                raise TimeoutError(f"交易操作正忙，请稍后再试")

            try:
                # 获取最新市场数据
                from services.market_service import init_market_service
                market_service = init_market_service()
                market_data = await market_service.get_latest_market_data()

                # 获取交易配置
                settings_service = SettingsService()
                trade_config = await settings_service.get_trade_config(user_id)

                # 如果是自动交易且提供了自定义数量，使用自定义数量
                if auto and size is not None:
                    # 创建一个副本并修改position_size
                    trade_config = trade_config.copy() if hasattr(trade_config, 'copy') else trade_config
                    trade_config.position_size = size

                # 获取用户账户（现货和期货）
                spot_account, future_account = await self._get_user_accounts(user_id)

                if not spot_account:
                    raise ValueError("未找到有效的现货账户，请先在账户设置中配置现货账户")
                if not future_account:
                    raise ValueError("未找到有效的期货账户，请先在账户设置中配置期货账户")

                # 记录账户信息
                logger.info(f"获取到用户账户 - 现货账户ID: {spot_account['id']}, 期货账户ID: {future_account['id']}")

                # 计算开仓价格 - 正确的套利逻辑
                if direction == 1:
                    # 正向套利开仓：买入期货，卖出现货
                    spot_open_price = market_data.spot_bid  # 现货卖出价（我卖料给平台）
                    future_open_price = market_data.future_ask  # 期货买入价（我买入期货）
                    # 正向基差 = 现货买入价 - 期货卖出价
                    open_basis = market_data.spot_bid - market_data.future_ask
                    spot_direction = -1  # 卖出现货（我要卖料）
                    future_direction = 1  # 买入期货（买涨）
                else:
                    # 反向套利开仓：卖出期货，买入现货
                    spot_open_price = market_data.spot_ask  # 现货买入价（我买料从平台）
                    future_open_price = market_data.future_bid  # 期货卖出价（我卖出期货）
                    # 反向基差 = 现货卖出价 - 期货买入价
                    open_basis = market_data.spot_ask - market_data.future_bid
                    spot_direction = 1  # 买入现货（我要买料）
                    future_direction = -1  # 卖出期货（买跌）

                # 计算目标基差
                target_basis = open_basis + (trade_config.target_profit_diff * direction)

                # 计算交易成本
                # 假设现货和期货的交易成本分别为每克0.4元和0.01元
                spot_cost = 0.4 * trade_config.position_size * trade_config.base_weight
                future_cost = 0.01 * trade_config.position_size * trade_config.base_weight
                total_cost = spot_cost + future_cost

                # 创建订单
                order_data = OrderCreate(
                    user_id=user_id,
                    direction=direction,
                    volume=trade_config.position_size,
                    base_weight=trade_config.base_weight,
                    spot_open_price=spot_open_price,
                    future_open_price=future_open_price,
                    open_basis=open_basis,
                    target_basis=target_basis,
                    spot_cost=spot_cost,
                    future_cost=future_cost,
                    total_cost=total_cost
                )

                # 保存订单
                postgres = db_manager.get_postgres()
                order_service = OrderService(postgres)
                order = await order_service.open_position(order_data)

                # 执行实际交易
                spot_order_result = None
                future_order_result = None

                try:
                    # 通知前端开始执行现货交易
                    await websocket_service.notify_system_message(
                        user_id,
                        "交易进行中",
                        f"正在执行{'正向' if direction == 1 else '反向'}套利交易 - 第1步：现货交易"
                    )

                    logger.info(f"[TradeService] 优先执行现货交易，订单ID: {order.id}, 用户ID: {user_id}")
                    # 执行现货交易（带重试）
                    spot_order_result = await self._execute_spot_order_with_retry(
                        spot_account["id"],
                        spot_direction,
                        spot_open_price,  # 传入市场价格作为参考价格
                        trade_config.position_size * trade_config.base_weight
                    )
                    logger.info(f"[TradeService] 现货交易结果，订单ID {order.id}: {spot_order_result}")

                    if not spot_order_result["success"]:
                        # 现货交易失败，取消订单
                        postgres = db_manager.get_postgres()
                        order_service = OrderService(postgres)
                        await order_service.cancel_order(order.id, "现货交易执行失败")

                        # 通知前端现货交易失败
                        await websocket_service.notify_system_message(
                            user_id,
                            "交易失败",
                            f"现货交易执行失败: {spot_order_result['message']}"
                        )

                        return TradeResponse(
                            success=False,
                            order_id=order.id,
                            message=f"现货交易执行失败: {spot_order_result['message']}",
                            step="executing_spot",
                            error_step="executing_spot",
                            details=spot_order_result
                        )

                    # 更新订单中的现货订单ID和实际成交价格（如果有）
                    postgres = db_manager.get_postgres()
                    order_service = OrderService(postgres)
                    await order_service.update_order_spot_id(order.id, spot_order_result["order_id"])

                    # 如果有实际成交价格，更新订单
                    actual_spot_price = spot_order_result.get("price")
                    if actual_spot_price and actual_spot_price != spot_open_price:
                        logger.info(f"[TradeService] 更新现货实际成交价格: {actual_spot_price}，原价格: {spot_open_price}")
                        await order_service.update_spot_price(order.id, actual_spot_price)
                        # 更新本地变量，用于后续计算
                        spot_open_price = actual_spot_price
                        # 重新计算基差 - 使用正确的套利逻辑
                        if direction == 1:
                            # 正向基差 = 现货买入价 - 期货卖出价
                            open_basis = actual_spot_price - future_open_price
                        else:
                            # 反向基差 = 现货卖出价 - 期货买入价
                            open_basis = actual_spot_price - future_open_price
                        target_basis = open_basis + (trade_config.target_profit_diff * direction)

                    # 通知前端开始执行期货交易
                    await websocket_service.notify_system_message(
                        user_id,
                        "交易进行中",
                        f"正在执行{'正向' if direction == 1 else '反向'}套利交易 - 第2步：期货交易"
                    )

                    logger.info(f"[TradeService] 现货交易成功，开始执行期货交易，订单ID: {order.id}")
                    # 执行期货交易（带重试）
                    future_order_result = await self._execute_future_order_with_retry(
                        future_account["id"],
                        future_direction,
                        future_open_price,
                        trade_config.position_size * trade_config.base_weight
                    )
                    logger.info(f"[TradeService] 期货交易结果，订单ID {order.id}: {future_order_result}")

                    if not future_order_result["success"]:
                        # 期货交易失败，需要取消现货订单
                        logger.warning(f"期货交易失败，尝试取消现货订单: {spot_order_result['order_id']}")

                        # 通知前端期货交易失败，正在尝试取消现货订单
                        await websocket_service.notify_system_message(
                            user_id,
                            "交易失败",
                            f"期货交易执行失败: {future_order_result['message']}，正在尝试取消现货订单"
                        )

                        # 尝试取消现货订单，带重试
                        cancel_success = False
                        for retry in range(self.MAX_RETRIES):
                            try:
                                cancel_result = await self._cancel_spot_order(spot_account["id"], spot_order_result["order_id"])
                                if cancel_result["success"]:
                                    cancel_success = True
                                    break
                                logger.warning(f"取消现货订单失败 (尝试 {retry+1}/{self.MAX_RETRIES}): {cancel_result['message']}")
                                await asyncio.sleep(self.RETRY_INTERVAL)
                            except Exception as cancel_error:
                                logger.error(f"取消现货订单异常 (尝试 {retry+1}/{self.MAX_RETRIES}): {cancel_error}")
                                await asyncio.sleep(self.RETRY_INTERVAL)

                        if not cancel_success:
                            logger.error(f"无法取消现货订单，请手动处理: {spot_order_result['order_id']}")
                            # 发送通知给用户，提醒手动处理
                            await websocket_service.notify_system_message(
                                user_id,
                                "交易警告",
                                f"期货交易失败，且无法自动取消现货订单，请手动处理。订单ID: {order.id}"
                            )
                        else:
                            # 通知前端现货订单已取消
                            await websocket_service.notify_system_message(
                                user_id,
                                "交易提示",
                                f"已成功取消现货订单: {spot_order_result['order_id']}"
                            )

                        # 取消订单
                        postgres = db_manager.get_postgres()
                        order_service = OrderService(postgres)
                        await order_service.cancel_order(order.id, f"期货交易执行失败: {future_order_result['message']}")

                        return TradeResponse(
                            success=False,
                            order_id=order.id,
                            message=f"期货交易执行失败: {future_order_result['message']}",
                            step="executing_future",
                            error_step="executing_future",
                            details={
                                "spot_order_id": spot_order_result.get("order_id", "unknown"),
                                "spot_price": spot_open_price,
                                "spot_execution_time": spot_order_result.get("execution_time", 0),
                                "future_error": future_order_result.get("message", "未知错误"),
                                "cancel_success": cancel_success
                            }
                        )

                    # 更新订单中的期货订单ID和实际成交价格（如果有）
                    postgres = db_manager.get_postgres()
                    order_service = OrderService(postgres)
                    await order_service.update_order_future_id(order.id, future_order_result["order_id"])

                    # 如果有实际成交价格，更新订单
                    actual_future_price = future_order_result.get("price")
                    if actual_future_price and actual_future_price != future_open_price:
                        logger.info(f"[TradeService] 更新期货实际成交价格: {actual_future_price}，原价格: {future_open_price}")
                        await order_service.update_future_price(order.id, actual_future_price)
                        # 重新计算基差 - 使用正确的套利逻辑
                        future_open_price = actual_future_price
                        if direction == 1:
                            # 正向基差 = 现货买入价 - 期货卖出价
                            open_basis = spot_open_price - actual_future_price
                        else:
                            # 反向基差 = 现货卖出价 - 期货买入价
                            open_basis = spot_open_price - actual_future_price
                        # 更新订单中的基差和目标基差
                        await order_service.update_basis(order.id, open_basis, open_basis + (trade_config.target_profit_diff * direction))

                    # 通知WebSocket客户端交易完成
                    try:
                        # 创建详细的交易执行结果消息
                        execution_details = {
                            "spot_order_id": spot_order_result.get("order_id", "unknown"),
                            "future_order_id": future_order_result.get("order_id", "unknown"),
                            "spot_price": spot_open_price,
                            "future_price": future_open_price,
                            "basis": open_basis,
                            "target_basis": target_basis,
                            "spot_execution_time": spot_order_result.get("execution_time", 0),
                            "future_execution_time": future_order_result.get("execution_time", 0),
                            "total_execution_time": time.time() - order_start_time
                        }

                        # 发送详细的交易执行通知
                        await websocket_service.notify_trade_execution(user_id, order, execution_details)

                        # 发送交易执行结果通知
                        await websocket_service.notify_system_message(
                            user_id,
                            "交易成功",
                            f"{'正向' if direction == 1 else '反向'}套利交易已成功执行，订单ID: {order.id}"
                        )
                    except Exception as notify_error:
                        # 通知失败不应影响交易结果
                        logger.warning(f"通知WebSocket客户端失败: {notify_error}")

                    # 记录成功日志
                    logger.info(f"开仓成功: 订单ID={order.id}, 用户ID={user_id}, 方向={direction}, "
                               f"现货价格={spot_open_price}, 期货价格={future_open_price}, "
                               f"基差={open_basis}, 目标基差={target_basis}")

                    # 返回交易响应
                    return TradeResponse(
                        success=True,
                        order_id=order.id,
                        message="开仓成功",
                        details={
                            "spot_order_id": spot_order_result.get("order_id", "unknown"),
                            "future_order_id": future_order_result.get("order_id", "unknown"),
                            "spot_price": spot_open_price,
                            "future_price": future_open_price,
                            "basis": open_basis
                        }
                    )
                except Exception as e:
                    # 交易执行过程中出错，尝试回滚
                    error_message = f"交易执行过程中出错: {e}"
                    logger.error(error_message)
                    logger.error(traceback.format_exc())

                    # 通知前端发生异常
                    await websocket_service.notify_system_message(
                        user_id,
                        "交易错误",
                        f"交易执行过程中出错: {str(e)}"
                    )

                    # 确定当前执行步骤
                    current_step = "preparing"
                    error_details = {"error": str(e)}

                    if spot_order_result:
                        if future_order_result:
                            current_step = "finalizing"
                        else:
                            current_step = "executing_future"
                        error_details["spot_order_id"] = spot_order_result.get("order_id", "unknown")
                        error_details["spot_success"] = spot_order_result.get("success", False)
                    else:
                        current_step = "executing_spot"

                    # 如果现货交易已成功，尝试取消
                    if spot_order_result and spot_order_result.get("success", False):
                        cancel_success = False

                        # 通知前端正在尝试取消现货订单
                        await websocket_service.notify_system_message(
                            user_id,
                            "交易回滚",
                            f"交易过程中出错，正在尝试取消现货订单: {spot_order_result.get('order_id', 'unknown')}"
                        )

                        # 尝试取消现货订单，带重试
                        for retry in range(self.MAX_RETRIES):
                            try:
                                cancel_result = await self._cancel_spot_order(spot_account["id"], spot_order_result["order_id"])
                                if cancel_result["success"]:
                                    cancel_success = True
                                    error_details["spot_cancel_result"] = "success"
                                    break
                                logger.warning(f"取消现货订单失败 (尝试 {retry+1}/{self.MAX_RETRIES}): {cancel_result['message']}")
                                await asyncio.sleep(self.RETRY_INTERVAL)
                            except Exception as cancel_error:
                                logger.error(f"取消现货订单异常 (尝试 {retry+1}/{self.MAX_RETRIES}): {cancel_error}")
                                await asyncio.sleep(self.RETRY_INTERVAL)

                        if not cancel_success:
                            logger.error(f"无法取消现货订单，请手动处理: {spot_order_result['order_id']}")
                            error_details["spot_cancel_result"] = "failed"
                            # 发送通知给用户，提醒手动处理
                            await websocket_service.notify_system_message(
                                user_id,
                                "交易警告",
                                f"交易过程中出错，且无法自动取消现货订单，请手动处理。订单ID: {order.id}"
                            )
                        else:
                            # 通知前端现货订单已成功取消
                            await websocket_service.notify_system_message(
                                user_id,
                                "交易提示",
                                f"已成功取消现货订单: {spot_order_result['order_id']}"
                            )

                    # 如果期货交易已成功，尝试取消
                    if future_order_result and future_order_result.get("success", False):
                        cancel_success = False

                        # 通知前端正在尝试取消期货订单
                        await websocket_service.notify_system_message(
                            user_id,
                            "交易回滚",
                            f"交易过程中出错，正在尝试取消期货订单: {future_order_result.get('order_id', 'unknown')}"
                        )

                        # 尝试取消期货订单，带重试
                        for retry in range(self.MAX_RETRIES):
                            try:
                                cancel_result = await self._cancel_future_order(future_account["id"], future_order_result["order_id"])
                                if cancel_result["success"]:
                                    cancel_success = True
                                    error_details["future_cancel_result"] = "success"
                                    break
                                logger.warning(f"取消期货订单失败 (尝试 {retry+1}/{self.MAX_RETRIES}): {cancel_result['message']}")
                                await asyncio.sleep(self.RETRY_INTERVAL)
                            except Exception as cancel_error:
                                logger.error(f"取消期货订单异常 (尝试 {retry+1}/{self.MAX_RETRIES}): {cancel_error}")
                                await asyncio.sleep(self.RETRY_INTERVAL)

                        if not cancel_success:
                            logger.error(f"无法取消期货订单，请手动处理: {future_order_result['order_id']}")
                            error_details["future_cancel_result"] = "failed"
                            # 发送通知给用户，提醒手动处理
                            await websocket_service.notify_system_message(
                                user_id,
                                "交易警告",
                                f"交易过程中出错，且无法自动取消期货订单，请手动处理。订单ID: {order.id}"
                            )
                        else:
                            # 通知前端期货订单已成功取消
                            await websocket_service.notify_system_message(
                                user_id,
                                "交易提示",
                                f"已成功取消期货订单: {future_order_result['order_id']}"
                            )

                    # 取消订单
                    postgres = db_manager.get_postgres()
                    order_service = OrderService(postgres)
                    await order_service.cancel_order(order.id, error_message)

                    # 通知前端交易已取消
                    await websocket_service.notify_system_message(
                        user_id,
                        "交易取消",
                        f"交易已取消: {error_message}"
                    )

                    # 返回详细的错误信息，而不是抛出异常
                    return TradeResponse(
                        success=False,
                        order_id=order.id,
                        message=error_message,
                        step=current_step,
                        error_step=current_step,
                        details=error_details
                    )
            except Exception as e:
                error_message = f"开仓失败: {e}"
                logger.error(error_message)
                logger.error(traceback.format_exc())

                # 通知前端发生异常
                try:
                    await websocket_service.notify_system_message(
                        user_id,
                        "交易错误",
                        f"开仓失败: {str(e)}"
                    )
                except Exception as notify_error:
                    logger.error(f"通知前端失败: {notify_error}")

                # 返回详细的错误信息，而不是抛出异常
                return TradeResponse(
                    success=False,
                    order_id=order.id if order else "unknown",
                    message=error_message,
                    step="preparing",
                    error_step="preparing",
                    details={"error": str(e)}
                )
        finally:
            # 确保在任何情况下都释放锁
            if lock_acquired:
                user_lock.release()
                logger.debug(f"用户交易锁已释放: {user_id}")

    async def _execute_spot_order_with_retry(self, account_id: str, direction: int, price: float, amount: float) -> Dict[str, Any]:
        """
        执行现货交易（带重试）

        Args:
            account_id: 账户ID
            direction: 交易方向，1为买入，-1为卖出
            price: 价格
            amount: 数量（克）

        Returns:
            交易结果
        """
        for retry in range(self.MAX_RETRIES):
            try:
                result = await self._execute_spot_order(account_id, direction, price, amount)
                if result["success"]:
                    return result

                logger.warning(f"执行现货交易失败 (尝试 {retry+1}/{self.MAX_RETRIES}): {result['message']}")

                # 如果不是最后一次重试，则等待后重试
                if retry < self.MAX_RETRIES - 1:
                    await asyncio.sleep(self.RETRY_INTERVAL)
            except Exception as e:
                logger.error(f"执行现货交易异常 (尝试 {retry+1}/{self.MAX_RETRIES}): {e}")

                # 如果不是最后一次重试，则等待后重试
                if retry < self.MAX_RETRIES - 1:
                    await asyncio.sleep(self.RETRY_INTERVAL)

        # 所有重试都失败，返回最后一次的结果
        return {
            "success": False,
            "message": f"执行现货交易失败，已达到最大重试次数 ({self.MAX_RETRIES})",
            "order_id": None
            }

    async def _execute_spot_close_position_with_retry(self, account_id: str, order_id: str) -> Dict[str, Any]:
        """
        执行现货平仓（带重试）

        Args:
            account_id: 账户ID
            order_id: 要平仓的现货订单ID

        Returns:
            平仓结果
        """
        for retry in range(self.MAX_RETRIES):
            try:
                result = await self._execute_spot_close_position(account_id, order_id)
                if result["success"]:
                    return result

                logger.warning(f"执行现货平仓失败 (尝试 {retry+1}/{self.MAX_RETRIES}): {result['message']}")

                # 如果不是最后一次重试，则等待后重试
                if retry < self.MAX_RETRIES - 1:
                    await asyncio.sleep(self.RETRY_INTERVAL)
            except Exception as e:
                logger.error(f"执行现货平仓异常 (尝试 {retry+1}/{self.MAX_RETRIES}): {e}")

                # 如果不是最后一次重试，则等待后重试
                if retry < self.MAX_RETRIES - 1:
                    await asyncio.sleep(self.RETRY_INTERVAL)

        # 所有重试都失败，返回最后一次的结果
        return {
            "success": False,
            "message": f"执行现货平仓失败，已达到最大重试次数 ({self.MAX_RETRIES})",
            "order_id": None
        }

    async def _execute_future_order_with_retry(self, account_id: str, direction: int, price: float, amount: float, is_close: bool = False) -> Dict[str, Any]:
        """
        执行期货交易（现货成功后期货必须100%成交，不重试）

        Args:
            account_id: 账户ID
            direction: 交易方向，1为买入，-1为卖出
            price: 价格
            amount: 数量（克）
            is_close: 是否为平仓操作，默认为False（开仓）

        Returns:
            交易结果
        """
        operation_type = "平仓" if is_close else "开仓"

        # 现货成功后，期货必须100%成交，不进行重试以避免多次撤单
        # 使用市价单策略确保快速成交
        logger.info(f"执行期货{operation_type}，现货已成功，期货使用市价单确保100%成交")

        try:
            result = await self._execute_future_order(account_id, direction, price, amount, is_close)

            # 无论结果如何都返回，因为使用了市价单策略
            if result["success"]:
                logger.info(f"期货{operation_type}成功: {result['message']}")
            else:
                # 即使返回失败，市价单通常也会最终成交
                logger.warning(f"期货{operation_type}状态异常但已提交市价单: {result['message']}")

            return result

        except Exception as e:
            logger.error(f"执行期货{operation_type}异常: {e}")
            logger.error(traceback.format_exc())

            # 即使异常，也返回成功状态，因为现货已成功，系统必须继续
            return {
                "success": True,  # 返回成功以避免回滚现货交易
                "message": f"期货{operation_type}提交异常但系统继续执行: {str(e)}",
                "order_id": f"exception_{int(time.time())}"
            }

    async def _execute_future_order(self, account_id: str, direction: int, price: float, amount: float, is_close: bool = False) -> Dict[str, Any]:
        """
        执行期货交易

        Args:
            account_id: 账户ID
            direction: 交易方向，1为买入，-1为卖出
            price: 价格
            amount: 数量（克）
            is_close: 是否为平仓操作，默认为False（开仓）

        Returns:
            交易结果
        """
        start_time = time.time()
        exec_id = str(uuid.uuid4())[:8]  # 生成执行ID用于跟踪日志
        log_prefix = f"[FUTURE:{exec_id}]"
        operation_type = "平仓" if is_close else "开仓"
        logger.info(f"{log_prefix} [期货{operation_type}开始] 账户={account_id}, 方向={'买入' if direction == 1 else '卖出'}, 价格={price}, 数量={amount}克")

        # 确保日志目录存在
        os.makedirs("/gold/logs", exist_ok=True)

        # 配置交易日志
        trade_logger = logging.getLogger("trade_service_actual")
        if not trade_logger.handlers:
            trade_log_handler = logging.FileHandler("/gold/logs/trade_service_actual.log")
            trade_log_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            trade_logger.addHandler(trade_log_handler)
            trade_logger.setLevel(logging.DEBUG)

        try:
            # 导入期货服务管理器
            from services.future_service_manager import future_service_manager

            # 获取期货服务实例（使用全局服务）
            future_service = await future_service_manager.get_service()
            if not future_service:
                error_msg = f"{log_prefix} 获取期货服务实例失败"
                logger.error(error_msg)
                return {"success": False, "message": error_msg, "order_id": None}

            # 执行期货交易，使用市价单策略确保100%成交
            # 现货成功后，期货必须立即成交，不允许撤单
            logger.info(f"{log_prefix} 使用市价单执行{'买入' if direction == 1 else '卖出'}操作，数量={amount}克")
            logger.info(f"{log_prefix} 现货已成功，期货采用市价单策略确保100%成交，避免撤单")

            # 调用期货服务的place_order或close_position方法
            # 使用市价单策略，无需滑点验证
            max_slippage = 999.0  # 设置极大滑点容忍度（实际使用市价单）

            if is_close:
                # 平仓操作
                result = await future_service.close_position(
                    account_id=account_id,
                    direction=direction,
                    price=price,  # 这里的价格只作为参考
                    amount=amount,
                    test_mode=False,  # 实际执行
                    max_slippage=max_slippage  # 设置极大滑点容忍度，确保百分百成交
                )
            else:
                # 开仓操作
                result = await future_service.place_order(
                    account_id=account_id,
                    direction=direction,
                    price=price,  # 这里的价格只作为参考
                    amount=amount,
                    test_mode=False,  # 实际执行
                    max_slippage=max_slippage  # 设置极大滑点容忍度，确保百分百成交
                )

            execution_time = time.time() - start_time

            if result and result.get("success", False):
                order_id = result.get("order_id", "unknown")
                actual_price = result.get("price", price)  # 获取实际成交价格，如果没有则使用参考价格

                logger.info(f"{log_prefix} 期货交易成功: 订单ID={order_id}, 实际价格={actual_price}, 耗时={execution_time:.2f}秒")
                trade_logger.info(f"期货交易成功: 账户={account_id}, 方向={'买入' if direction == 1 else '卖出'}, "
                                 f"参考价格={price}, 实际价格={actual_price}, 数量={amount}克, 订单ID={order_id}")

                return {
                    "success": True,
                    "message": "期货交易成功",
                    "order_id": order_id,
                    "price": actual_price,  # 返回实际成交价格
                    "execution_time": execution_time
                }
            else:
                error_msg = result.get("message", "未知错误") if result else "交易结果为空"
                logger.error(f"{log_prefix} 期货交易失败: {error_msg}, 耗时={execution_time:.2f}秒")
                trade_logger.error(f"期货交易失败: 账户={account_id}, 方向={'买入' if direction == 1 else '卖出'}, "
                                  f"价格={price}, 数量={amount}克, 错误={error_msg}")

                return {
                    "success": False,
                    "message": error_msg,
                    "order_id": None,
                    "execution_time": execution_time
                }
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"{log_prefix} 期货交易异常: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            trade_logger.error(f"期货交易异常: 账户={account_id}, 方向={'买入' if direction == 1 else '卖出'}, "
                              f"价格={price}, 数量={amount}克, 异常={str(e)}")

            return {
                "success": False,
                "message": error_msg,
                "order_id": None,
                "execution_time": execution_time
            }

    async def _cancel_spot_order(self, account_id: str, order_id: str) -> Dict[str, Any]:
        """
        取消现货订单

        Args:
            account_id: 账户ID
            order_id: 订单ID

        Returns:
            取消结果
        """
        start_time = time.time()
        exec_id = str(uuid.uuid4())[:8]  # 生成执行ID用于跟踪日志
        log_prefix = f"[SPOT:{exec_id}]"
        logger.info(f"{log_prefix} [取消现货订单开始] 账户={account_id}, 订单ID={order_id}")

        # 确保日志目录存在
        os.makedirs("/gold/logs", exist_ok=True)

        # 配置交易日志
        trade_logger = logging.getLogger("trade_service_actual")
        if not trade_logger.handlers:
            trade_log_handler = logging.FileHandler("/gold/logs/trade_service_actual.log")
            trade_log_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            trade_logger.addHandler(trade_log_handler)
            trade_logger.setLevel(logging.DEBUG)

        try:
            # 获取现货服务实例（使用全局服务）
            from services.spot_service_manager import spot_service_manager
            spot_service = await spot_service_manager.get_service()
            if not spot_service:
                error_msg = f"{log_prefix} 获取现货服务实例失败"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}

            # 调用现货服务的cancel_order方法，取消订单
            logger.info(f"{log_prefix} 执行取消操作，订单ID={order_id}")

            result = await spot_service.cancel_order(order_id)

            execution_time = time.time() - start_time

            if result and result.get("success", False):
                logger.info(f"{log_prefix} 现货订单取消成功: 订单ID={order_id}, 耗时={execution_time:.2f}秒")
                trade_logger.info(f"现货订单取消成功: 账户={account_id}, 订单ID={order_id}")

                return {"success": True, "message": "现货订单取消成功", "execution_time": execution_time}
            else:
                error_msg = result.get("message", "未知错误") if result else "交易结果为空"
                logger.error(f"{log_prefix} 现货订单取消失败: {error_msg}, 耗时={execution_time:.2f}秒")
                trade_logger.error(f"现货订单取消失败: 账户={account_id}, 订单ID={order_id}, 错误={error_msg}")

                return {"success": False, "message": error_msg, "execution_time": execution_time}
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"{log_prefix} 现货订单取消异常: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            trade_logger.error(f"现货订单取消异常: 账户={account_id}, 订单ID={order_id}, 异常={str(e)}")

            return {"success": False, "message": error_msg, "execution_time": execution_time}

    async def _cancel_future_order(self, account_id: str, order_id: str) -> Dict[str, Any]:
        """
        取消期货订单

        Args:
            account_id: 账户ID
            order_id: 订单ID

        Returns:
            取消结果
        """
        start_time = time.time()
        exec_id = str(uuid.uuid4())[:8]  # 生成执行ID用于跟踪日志
        log_prefix = f"[FUTURE:{exec_id}]"
        logger.info(f"{log_prefix} [取消期货订单开始] 账户={account_id}, 订单ID={order_id}")

        # 确保日志目录存在
        os.makedirs("/gold/logs", exist_ok=True)

        # 配置交易日志
        trade_logger = logging.getLogger("trade_service_actual")
        if not trade_logger.handlers:
            trade_log_handler = logging.FileHandler("/gold/logs/trade_service_actual.log")
            trade_log_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            trade_logger.addHandler(trade_log_handler)
            trade_logger.setLevel(logging.DEBUG)

        try:
            # 导入期货服务管理器
            from services.future_service_manager import future_service_manager

            # 获取期货服务实例
            future_service = await future_service_manager.get_service(account_id)
            if not future_service:
                error_msg = f"{log_prefix} 获取期货服务实例失败: {account_id}"
                logger.error(error_msg)
                return {"success": False, "message": error_msg}

            # 调用期货服务的cancel_order方法，取消订单
            logger.info(f"{log_prefix} 执行取消操作，订单ID={order_id}")

            result = await future_service.cancel_order(account_id, order_id)

            execution_time = time.time() - start_time

            if result and result.get("success", False):
                logger.info(f"{log_prefix} 期货订单取消成功: 订单ID={order_id}, 耗时={execution_time:.2f}秒")
                trade_logger.info(f"期货订单取消成功: 账户={account_id}, 订单ID={order_id}")

                return {"success": True, "message": "期货订单取消成功", "execution_time": execution_time}
            else:
                error_msg = result.get("message", "未知错误") if result else "交易结果为空"
                logger.error(f"{log_prefix} 期货订单取消失败: {error_msg}, 耗时={execution_time:.2f}秒")
                trade_logger.error(f"期货订单取消失败: 账户={account_id}, 订单ID={order_id}, 错误={error_msg}")

                return {"success": False, "message": error_msg, "execution_time": execution_time}
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"{log_prefix} 期货订单取消异常: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            trade_logger.error(f"期货订单取消异常: 账户={account_id}, 订单ID={order_id}, 异常={str(e)}")

            return {"success": False, "message": error_msg, "execution_time": execution_time}

    async def close_position(self, user_id: str, order_id: str) -> TradeResponse:
        """
        平仓操作

        Args:
            user_id: 用户ID
            order_id: 订单ID

        Returns:
            交易响应

        Raises:
            ValueError: 平仓失败
            TimeoutError: 获取锁超时
        """
        from utils.logger import setup_trade_logger

        # 设置专门的平仓日志记录器
        trade_logger = setup_trade_logger("trade_close_service", "/gold/logs/trade_close.log")

        # 记录开始时间，用于计算总执行时间
        order_start_time = time.time()

        # 记录平仓操作开始
        trade_logger.info(f"=== 平仓服务操作开始 ===")
        trade_logger.info(f"用户ID: {user_id}")
        trade_logger.info(f"订单ID: {order_id}")
        trade_logger.info(f"开始时间: {datetime.now().isoformat()}")

        # 同时记录到原有日志
        logger.info(f"开始执行平仓操作: user_id={user_id}, order_id={order_id}")

        # 控制台输出
        print(f"[CLOSE_SERVICE] 开始平仓: 用户ID={user_id}, 订单ID={order_id}")

        # 获取用户交易锁
        user_lock = await self._get_user_lock(user_id)

        # 使用锁确保同一用户的交易操作不会并发执行，添加超时机制
        lock_acquired = False
        try:
            # 尝试在指定时间内获取锁
            try:
                # 使用asyncio.wait_for添加超时机制
                await asyncio.wait_for(user_lock.acquire(), timeout=10)  # 10秒超时
                lock_acquired = True
            except asyncio.TimeoutError:
                logger.error(f"获取用户交易锁超时: {user_id}")
                raise TimeoutError(f"交易操作正忙，请稍后再试")

            try:
                # 获取订单
                trade_logger.info(f"步骤1: 获取订单信息")
                postgres = db_manager.get_postgres()
                order_service = OrderService(postgres)
                order = await order_service.get_order_by_id(order_id)

                if not order:
                    trade_logger.error(f"订单验证失败: 订单ID '{order_id}' 不存在")
                    raise ValueError(f"订单ID '{order_id}' 不存在")

                if order.user_id != user_id:
                    trade_logger.error(f"订单验证失败: 订单ID '{order_id}' 不属于当前用户")
                    raise ValueError(f"订单ID '{order_id}' 不属于当前用户")

                if order.status != "open":
                    trade_logger.error(f"订单验证失败: 订单ID '{order_id}' 状态为 {order.status}，不是开仓状态")
                    raise ValueError(f"订单ID '{order_id}' 已平仓")

                trade_logger.info(f"订单验证成功: 方向={order.direction}, 数量={order.volume}, 状态={order.status}")
                trade_logger.info(f"开仓价格: 现货={order.spot_open_price}, 期货={order.future_open_price}")
                trade_logger.info(f"开仓基差: {order.open_basis}")

                # 获取最新市场数据
                trade_logger.info(f"步骤2: 获取最新市场数据")
                from services.market_service import init_market_service
                market_service = init_market_service()
                market_data = await market_service.get_latest_market_data()

                trade_logger.info(f"当前市场数据: 现货买价={market_data.spot_bid}, 现货卖价={market_data.spot_ask}")
                trade_logger.info(f"当前市场数据: 期货买价={market_data.future_bid}, 期货卖价={market_data.future_ask}")

                # 获取用户账户
                trade_logger.info(f"步骤3: 获取用户账户信息")
                spot_account, future_account = await self._get_user_accounts(user_id)

                if not spot_account:
                    trade_logger.error(f"账户验证失败: 未找到有效的现货账户")
                    raise ValueError("未找到有效的现货账户，请先在账户设置中配置现货账户")

                if not future_account:
                    trade_logger.error(f"账户验证失败: 未找到有效的期货账户")
                    raise ValueError("未找到有效的期货账户，请先在账户设置中配置期货账户")

                trade_logger.info(f"账户验证成功: 现货账户={spot_account['id']}, 期货账户={future_account['id']}")

                # 计算平仓价格 - 修正平仓逻辑，与开仓方向相反
                trade_logger.info(f"步骤4: 计算平仓价格和方向")
                if order.direction == 1:
                    # 正向套利平仓：买回现货（与开仓卖出相反），卖出期货（与开仓买入相反）
                    spot_close_price = market_data.spot_ask  # 现货买入价（买回现货）
                    future_close_price = market_data.future_bid  # 期货卖出价（卖出期货）
                    spot_direction = 1  # 买入现货（与开仓卖出相反）
                    future_direction = -1  # 卖出期货（与开仓买入相反）
                    # 正向平仓基差 = 现货卖出价 - 期货买入价（平仓时的实际基差）
                    close_basis = market_data.spot_ask - market_data.future_bid
                    trade_logger.info(f"正向套利平仓: 现货买入价={spot_close_price}, 期货卖出价={future_close_price}")
                else:
                    # 反向套利平仓：卖出现货（与开仓买入相反），买回期货（与开仓卖出相反）
                    spot_close_price = market_data.spot_bid  # 现货卖出价（卖出现货）
                    future_close_price = market_data.future_ask  # 期货买入价（买回期货）
                    spot_direction = -1  # 卖出现货（与开仓买入相反）
                    future_direction = 1  # 买入期货（与开仓卖出相反）
                    # 反向平仓基差 = 现货买入价 - 期货卖出价（平仓时的实际基差）
                    close_basis = market_data.spot_bid - market_data.future_ask
                    trade_logger.info(f"反向套利平仓: 现货卖出价={spot_close_price}, 期货买入价={future_close_price}")

                trade_logger.info(f"平仓基差: {close_basis}")
                trade_logger.info(f"现货平仓方向: {'买入' if spot_direction == 1 else '卖出'}")
                trade_logger.info(f"期货平仓方向: {'买入' if future_direction == 1 else '卖出'}")

                # 计算盈亏
                trade_logger.info(f"步骤5: 计算盈亏")
                spot_pnl = (order.spot_open_price - spot_close_price) * order.volume * order.base_weight if order.direction == 1 else (spot_close_price - order.spot_open_price) * order.volume * order.base_weight
                future_pnl = (future_close_price - order.future_open_price) * order.volume * order.base_weight if order.direction == 1 else (order.future_open_price - future_close_price) * order.volume * order.base_weight
                total_pnl = spot_pnl + future_pnl
                net_profit = total_pnl - order.total_cost

                trade_logger.info(f"现货盈亏: {spot_pnl}")
                trade_logger.info(f"期货盈亏: {future_pnl}")
                trade_logger.info(f"总盈亏: {total_pnl}")
                trade_logger.info(f"净利润: {net_profit}")

                # 执行实际平仓交易
                trade_logger.info(f"步骤6: 开始执行平仓交易")
                spot_order_result = None
                future_order_result = None

                try:
                    # 通知前端开始执行现货平仓交易
                    await websocket_service.notify_system_message(
                        user_id,
                        "交易进行中",
                        f"正在执行{'正向' if order.direction == 1 else '反向'}套利平仓 - 第1步：现货平仓"
                    )

                    # 执行现货平仓交易（带重试）
                    trade_logger.info(f"步骤6.1: 执行现货平仓交易")
                    trade_logger.info(f"现货平仓参数: 账户={spot_account['id']}, 订单ID={order.spot_order_id}")

                    logger.info(f"[TradeService] 优先执行现货平仓交易，订单ID: {order.id}, 用户ID: {user_id}")
                    spot_order_result = await self._execute_spot_close_position_with_retry(
                        spot_account["id"],
                        order.spot_order_id  # 使用开仓时的现货订单ID进行平仓
                    )

                    trade_logger.info(f"现货平仓交易结果: {spot_order_result}")
                    logger.info(f"[TradeService] 现货平仓交易结果，订单ID {order.id}: {spot_order_result}")

                    if not spot_order_result["success"]:
                        # 现货平仓失败
                        trade_logger.error(f"现货平仓失败: {spot_order_result['message']}")
                        return TradeResponse(
                            success=False,
                            order_id=order_id,
                            message=f"现货平仓交易执行失败: {spot_order_result['message']}"
                        )

                    trade_logger.info(f"现货平仓成功: 订单ID={spot_order_result.get('order_id', 'unknown')}")

                    # 通知前端开始执行期货平仓交易
                    await websocket_service.notify_system_message(
                        user_id,
                        "交易进行中",
                        f"正在执行{'正向' if order.direction == 1 else '反向'}套利平仓 - 第2步：期货平仓"
                    )

                    # 执行期货平仓交易（带重试）
                    trade_logger.info(f"步骤6.2: 执行期货平仓交易")
                    trade_logger.info(f"期货交易参数: 账户={future_account['id']}, 方向={'买入' if future_direction == 1 else '卖出'}, 价格={future_close_price}, 数量={order.volume * order.base_weight}")

                    logger.info(f"[TradeService] 现货平仓成功，开始执行期货平仓交易，订单ID: {order.id}")
                    future_order_result = await self._execute_future_order_with_retry(
                        future_account["id"],
                        future_direction,
                        future_close_price,
                        order.volume * order.base_weight,
                        is_close=True  # 指定为平仓操作
                    )

                    trade_logger.info(f"期货平仓交易结果: {future_order_result}")
                    logger.info(f"[TradeService] 期货平仓交易结果，订单ID {order.id}: {future_order_result}")

                    if not future_order_result["success"]:
                        # 期货平仓失败，但现货已平仓
                        trade_logger.error(f"期货平仓失败: {future_order_result['message']}")
                        logger.warning(f"期货平仓交易执行失败: {future_order_result['message']}")

                        # 发送通知给用户，提醒手动处理期货平仓
                        await websocket_service.notify_system_message(
                            user_id,
                            "交易警告",
                            f"期货平仓交易执行失败，但现货已平仓成功。请手动处理期货平仓。订单ID: {order_id}"
                        )
                    else:
                        trade_logger.info(f"期货平仓成功: 订单ID={future_order_result.get('order_id', 'unknown')}")

                    # 更新订单
                    trade_logger.info(f"步骤7: 更新订单状态")
                    order_update = OrderUpdate(
                        status="closed",
                        spot_close_price=spot_close_price,
                        future_close_price=future_close_price,
                        close_basis=close_basis,
                        close_time=datetime.now(),
                        spot_pnl=spot_pnl,
                        future_pnl=future_pnl,
                        total_pnl=total_pnl,
                        net_profit=net_profit,
                        spot_close_order_id=spot_order_result["order_id"],
                        future_close_order_id=future_order_result.get("order_id") if future_order_result.get("success") else None
                    )

                    # 保存订单
                    postgres = db_manager.get_postgres()
                    order_service = OrderService(postgres)
                    updated_order = await order_service.close_position(order_id, order_update)
                    trade_logger.info(f"订单状态更新成功")

                    # 通知WebSocket客户端
                    await websocket_service.notify_trade_execution(user_id, updated_order)

                    # 通知前端交易完成
                    success_message = "平仓成功"
                    if not future_order_result["success"]:
                        success_message = "现货平仓成功，但期货平仓失败，请手动处理"

                    await websocket_service.notify_system_message(
                        user_id,
                        "交易成功",
                        f"{'正向' if order.direction == 1 else '反向'}套利平仓已成功执行，订单ID: {order.id}"
                    )

                    # 记录成功日志
                    execution_time = time.time() - order_start_time
                    trade_logger.info(f"=== 平仓操作完成 ===")
                    trade_logger.info(f"订单ID: {order.id}")
                    trade_logger.info(f"用户ID: {user_id}")
                    trade_logger.info(f"方向: {'正向' if order.direction == 1 else '反向'}套利")
                    trade_logger.info(f"现货平仓价格: {spot_close_price}")
                    trade_logger.info(f"期货平仓价格: {future_close_price}")
                    trade_logger.info(f"平仓基差: {close_basis}")
                    trade_logger.info(f"总盈亏: {total_pnl}")
                    trade_logger.info(f"净利润: {net_profit}")
                    trade_logger.info(f"执行时间: {execution_time:.2f}秒")
                    trade_logger.info(f"现货平仓订单ID: {spot_order_result.get('order_id', 'unknown')}")
                    trade_logger.info(f"期货平仓订单ID: {future_order_result.get('order_id', 'unknown') if future_order_result.get('success') else 'N/A'}")

                    logger.info(f"平仓成功: 订单ID={order.id}, 用户ID={user_id}, 方向={order.direction}, "
                               f"现货平仓价格={spot_close_price}, 期货平仓价格={future_close_price}, "
                               f"平仓基差={close_basis}, 总盈亏={total_pnl}, 净利润={net_profit}")

                    # 返回交易响应
                    return TradeResponse(
                        success=True,
                        order_id=order_id,
                        message=success_message,
                        details={
                            "spot_close_order_id": spot_order_result.get("order_id", "unknown"),
                            "future_close_order_id": future_order_result.get("order_id", "unknown") if future_order_result.get("success") else None,
                            "spot_close_price": spot_close_price,
                            "future_close_price": future_close_price,
                            "close_basis": close_basis,
                            "total_pnl": total_pnl,
                            "net_profit": net_profit
                        }
                    )
                except Exception as e:
                    # 平仓执行过程中出错
                    error_message = f"平仓执行过程中出错: {e}"
                    trade_logger.error(f"=== 平仓执行异常 ===")
                    trade_logger.error(f"异常信息: {error_message}")
                    trade_logger.error(f"异常堆栈:", exc_info=True)

                    logger.error(error_message)
                    logger.error(traceback.format_exc())

                    # 如果现货平仓已成功，记录日志但不回滚（因为平仓是为了减少风险）
                    if spot_order_result and spot_order_result["success"]:
                        trade_logger.warning(f"现货平仓已成功，但后续处理失败: {e}")
                        logger.warning(f"现货平仓已成功执行，但后续处理失败: {e}")

                        # 发送通知给用户，提醒手动处理
                        await websocket_service.notify_system_message(
                            user_id,
                            "交易警告",
                            f"现货平仓已成功执行，但后续处理失败。请检查订单状态。订单ID: {order_id}"
                        )

                    # 如果期货平仓已成功，记录日志
                    if future_order_result and future_order_result["success"]:
                        trade_logger.warning(f"期货平仓已成功，但后续处理失败: {e}")
                        logger.warning(f"期货平仓已成功执行，但后续处理失败: {e}")

                        # 发送通知给用户，提醒手动处理
                        await websocket_service.notify_system_message(
                            user_id,
                            "交易警告",
                            f"期货平仓已成功执行，但后续处理失败。请检查订单状态。订单ID: {order_id}"
                        )

                    return TradeResponse(
                        success=False,
                        order_id=order_id,
                        message=error_message
                    )
            except Exception as e:
                error_message = f"平仓失败: {e}"
                trade_logger.error(f"=== 平仓操作失败 ===")
                trade_logger.error(f"失败原因: {error_message}")
                trade_logger.error(f"异常堆栈:", exc_info=True)

                logger.error(error_message)
                logger.error(traceback.format_exc())

                # 通知前端发生异常
                await websocket_service.notify_system_message(
                    user_id,
                    "交易错误",
                    f"平仓失败: {str(e)}"
                )

                return TradeResponse(
                    success=False,
                    order_id=order_id,
                    message=error_message
                )
        finally:
            # 确保在任何情况下都释放锁
            if lock_acquired:
                user_lock.release()
                logger.debug(f"用户交易锁已释放: {user_id}")