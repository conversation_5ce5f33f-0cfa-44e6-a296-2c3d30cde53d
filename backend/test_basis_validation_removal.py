#!/usr/bin/env python3
"""
测试移除基差验证器后的效果

验证移除基差验证器后不再产生日志噪音
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.order import OrderCreate, OrderUpdate
from utils.logger import setup_logger

# 设置日志
os.makedirs("/gold/logs", exist_ok=True)
logger = setup_logger('test_basis_validation_removal', "/gold/logs/test_basis_validation_removal.log")


class BasisValidationRemovalTester:
    """基差验证器移除测试器"""
    
    def __init__(self):
        self.test_results = {}
    
    def test_order_create_without_basis_logs(self):
        """测试创建订单时不再产生基差验证日志"""
        logger.info("=" * 60)
        logger.info("测试创建订单时不再产生基差验证日志")
        logger.info("=" * 60)
        
        try:
            # 创建测试订单数据
            order_data = {
                "user_id": "test_user",
                "direction": -1,  # 反向套利
                "volume": 1,
                "base_weight": 1000,
                "spot_open_price": 765.50,  # 现货价格
                "future_open_price": 769.80,  # 期货价格
                "open_basis": -4.30,  # 实际基差
                "target_basis": -3.80,  # 目标基差
                "spot_cost": 4.0,
                "future_cost": 0.1,
                "total_cost": 4.1
            }
            
            logger.info(f"创建测试订单，数据: {order_data}")
            
            # 记录创建前的日志位置
            log_position_before = self._get_log_position()
            
            # 创建订单
            order = OrderCreate(**order_data)
            
            # 记录创建后的日志位置
            log_position_after = self._get_log_position()
            
            # 检查是否产生了基差验证日志
            new_logs = self._get_new_logs(log_position_before, log_position_after)
            basis_validation_logs = [log for log in new_logs if "基差调试" in log or "基差差异" in log]
            
            logger.info(f"✅ 订单创建成功")
            logger.info(f"📋 订单信息:")
            logger.info(f"   用户ID: {order.user_id}")
            logger.info(f"   方向: {order.direction}")
            logger.info(f"   现货价格: {order.spot_open_price}")
            logger.info(f"   期货价格: {order.future_open_price}")
            logger.info(f"   开仓基差: {order.open_basis}")
            
            if not basis_validation_logs:
                logger.info("✅ 没有产生基差验证日志 - 验证器已成功移除")
                self.test_results["order_create_test"] = {
                    "success": True,
                    "basis_logs_count": 0,
                    "order_created": True
                }
                return True
            else:
                logger.warning(f"⚠️ 仍然产生了 {len(basis_validation_logs)} 条基差验证日志:")
                for log in basis_validation_logs:
                    logger.warning(f"   {log}")
                self.test_results["order_create_test"] = {
                    "success": False,
                    "basis_logs_count": len(basis_validation_logs),
                    "order_created": True,
                    "logs": basis_validation_logs
                }
                return False
                
        except Exception as e:
            logger.error(f"测试订单创建失败: {e}")
            self.test_results["order_create_test"] = {
                "success": False,
                "error": str(e),
                "order_created": False
            }
            return False
    
    def test_order_update_without_basis_logs(self):
        """测试更新订单时不再产生基差验证日志"""
        logger.info("=" * 60)
        logger.info("测试更新订单时不再产生基差验证日志")
        logger.info("=" * 60)
        
        try:
            # 创建测试订单更新数据
            update_data = {
                "status": "closed",
                "spot_close_price": 764.80,  # 现货平仓价格
                "future_close_price": 769.20,  # 期货平仓价格
                "close_basis": -4.40,  # 平仓基差
                "close_time": datetime.now(),
                "spot_pnl": 70.0,
                "future_pnl": 60.0,
                "total_pnl": 130.0,
                "net_profit": 125.9
            }
            
            logger.info(f"创建测试订单更新，数据: {update_data}")
            
            # 记录更新前的日志位置
            log_position_before = self._get_log_position()
            
            # 创建订单更新
            order_update = OrderUpdate(**update_data)
            
            # 记录更新后的日志位置
            log_position_after = self._get_log_position()
            
            # 检查是否产生了基差验证日志
            new_logs = self._get_new_logs(log_position_before, log_position_after)
            basis_validation_logs = [log for log in new_logs if "基差调试" in log or "基差差异" in log]
            
            logger.info(f"✅ 订单更新创建成功")
            logger.info(f"📋 更新信息:")
            logger.info(f"   状态: {order_update.status}")
            logger.info(f"   现货平仓价格: {order_update.spot_close_price}")
            logger.info(f"   期货平仓价格: {order_update.future_close_price}")
            logger.info(f"   平仓基差: {order_update.close_basis}")
            
            if not basis_validation_logs:
                logger.info("✅ 没有产生基差验证日志 - 验证器已成功移除")
                self.test_results["order_update_test"] = {
                    "success": True,
                    "basis_logs_count": 0,
                    "order_updated": True
                }
                return True
            else:
                logger.warning(f"⚠️ 仍然产生了 {len(basis_validation_logs)} 条基差验证日志:")
                for log in basis_validation_logs:
                    logger.warning(f"   {log}")
                self.test_results["order_update_test"] = {
                    "success": False,
                    "basis_logs_count": len(basis_validation_logs),
                    "order_updated": True,
                    "logs": basis_validation_logs
                }
                return False
                
        except Exception as e:
            logger.error(f"测试订单更新失败: {e}")
            self.test_results["order_update_test"] = {
                "success": False,
                "error": str(e),
                "order_updated": False
            }
            return False
    
    def _get_log_position(self):
        """获取当前日志文件位置"""
        try:
            log_file = "/gold/logs/test_basis_validation_removal.log"
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    f.seek(0, 2)  # 移动到文件末尾
                    return f.tell()
            return 0
        except Exception:
            return 0
    
    def _get_new_logs(self, start_position, end_position):
        """获取新增的日志内容"""
        try:
            log_file = "/gold/logs/test_basis_validation_removal.log"
            if os.path.exists(log_file) and end_position > start_position:
                with open(log_file, 'r') as f:
                    f.seek(start_position)
                    new_content = f.read(end_position - start_position)
                    return new_content.split('\n')
            return []
        except Exception:
            return []
    
    def test_multiple_orders_performance(self):
        """测试创建多个订单的性能（无基差验证日志）"""
        logger.info("=" * 60)
        logger.info("测试创建多个订单的性能")
        logger.info("=" * 60)
        
        try:
            import time
            
            order_count = 10
            start_time = time.time()
            
            # 记录开始前的日志位置
            log_position_before = self._get_log_position()
            
            created_orders = []
            for i in range(order_count):
                order_data = {
                    "user_id": f"test_user_{i}",
                    "direction": 1 if i % 2 == 0 else -1,
                    "volume": 1,
                    "base_weight": 1000,
                    "spot_open_price": 765.50 + i * 0.1,
                    "future_open_price": 769.80 + i * 0.1,
                    "open_basis": -4.30 + i * 0.05,
                    "target_basis": -3.80 + i * 0.05,
                    "spot_cost": 4.0,
                    "future_cost": 0.1,
                    "total_cost": 4.1
                }
                
                order = OrderCreate(**order_data)
                created_orders.append(order)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 记录结束后的日志位置
            log_position_after = self._get_log_position()
            
            # 检查是否产生了基差验证日志
            new_logs = self._get_new_logs(log_position_before, log_position_after)
            basis_validation_logs = [log for log in new_logs if "基差调试" in log or "基差差异" in log]
            
            logger.info(f"✅ 成功创建 {len(created_orders)} 个订单")
            logger.info(f"⏱️ 执行时间: {execution_time:.4f} 秒")
            logger.info(f"📊 平均每个订单: {execution_time/order_count:.4f} 秒")
            logger.info(f"📋 基差验证日志数量: {len(basis_validation_logs)}")
            
            self.test_results["performance_test"] = {
                "success": True,
                "orders_created": len(created_orders),
                "execution_time": execution_time,
                "avg_time_per_order": execution_time / order_count,
                "basis_logs_count": len(basis_validation_logs)
            }
            
            return len(basis_validation_logs) == 0
            
        except Exception as e:
            logger.error(f"性能测试失败: {e}")
            self.test_results["performance_test"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def generate_report(self):
        """生成测试报告"""
        logger.info("=" * 80)
        logger.info("基差验证器移除测试报告")
        logger.info("=" * 80)
        
        order_create_test = self.test_results.get("order_create_test", {})
        order_update_test = self.test_results.get("order_update_test", {})
        performance_test = self.test_results.get("performance_test", {})
        
        logger.info(f"📊 测试结果摘要:")
        
        # 订单创建测试
        if order_create_test.get("success"):
            logger.info(f"   订单创建测试: ✅ 成功")
            logger.info(f"     基差日志数量: {order_create_test.get('basis_logs_count', 0)}")
        else:
            logger.info(f"   订单创建测试: ❌ 失败")
            if order_create_test.get("basis_logs_count", 0) > 0:
                logger.info(f"     仍有基差日志: {order_create_test.get('basis_logs_count')}")
        
        # 订单更新测试
        if order_update_test.get("success"):
            logger.info(f"   订单更新测试: ✅ 成功")
            logger.info(f"     基差日志数量: {order_update_test.get('basis_logs_count', 0)}")
        else:
            logger.info(f"   订单更新测试: ❌ 失败")
            if order_update_test.get("basis_logs_count", 0) > 0:
                logger.info(f"     仍有基差日志: {order_update_test.get('basis_logs_count')}")
        
        # 性能测试
        if performance_test.get("success"):
            logger.info(f"   性能测试: ✅ 成功")
            logger.info(f"     创建订单数: {performance_test.get('orders_created', 0)}")
            logger.info(f"     执行时间: {performance_test.get('execution_time', 0):.4f}秒")
            logger.info(f"     基差日志数量: {performance_test.get('basis_logs_count', 0)}")
        else:
            logger.info(f"   性能测试: ❌ 失败")
        
        # 总体评估
        all_tests_passed = (
            order_create_test.get("success", False) and
            order_update_test.get("success", False) and
            performance_test.get("success", False)
        )
        
        total_basis_logs = (
            order_create_test.get("basis_logs_count", 0) +
            order_update_test.get("basis_logs_count", 0) +
            performance_test.get("basis_logs_count", 0)
        )
        
        if all_tests_passed and total_basis_logs == 0:
            logger.info("\n🎉 基差验证器移除成功！")
            logger.info("✅ 订单创建不再产生基差验证日志")
            logger.info("✅ 订单更新不再产生基差验证日志")
            logger.info("✅ 性能测试无基差验证日志")
            logger.info("🚀 系统日志已清理，不再有基差验证噪音")
            return True
        else:
            logger.warning("\n⚠️ 基差验证器移除存在问题")
            if not all_tests_passed:
                logger.warning("❌ 部分测试失败")
            if total_basis_logs > 0:
                logger.warning(f"❌ 仍然产生了 {total_basis_logs} 条基差验证日志")
            return False


def main():
    """主函数"""
    logger.info("开始测试基差验证器移除效果...")
    
    tester = BasisValidationRemovalTester()
    
    try:
        # 测试订单创建
        create_success = tester.test_order_create_without_basis_logs()
        
        # 测试订单更新
        update_success = tester.test_order_update_without_basis_logs()
        
        # 测试性能
        performance_success = tester.test_multiple_orders_performance()
        
        # 生成报告
        success = tester.generate_report()
        
        return success
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
