#!/usr/bin/env python3
"""
测试选择器修复的脚本

验证以下修复：
1. 基差计算差异修复
2. 现货平仓选择器修复
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import setup_logger

# 设置日志
os.makedirs("/gold/logs", exist_ok=True)
logger = setup_logger('test_selector_fix', "/gold/logs/test_selector_fix.log")

class SelectorFixTester:
    """选择器修复测试器"""
    
    def __init__(self):
        self.test_results = {}
    
    def test_basis_calculation_fix(self):
        """测试基差计算修复"""
        logger.info("测试基差计算修复...")
        
        # 模拟订单数据
        test_cases = [
            {
                "name": "正向套利开仓",
                "direction": 1,
                "spot_open_price": 765.5,
                "future_open_price": 769.10,
                "expected_basis": 765.5 - 769.10,  # -3.6
                "expected_diff_should_be_small": True
            },
            {
                "name": "反向套利开仓",
                "direction": -1,
                "spot_open_price": 765.8,
                "future_open_price": 769.72,
                "expected_basis": 765.8 - 769.72,  # -3.92
                "expected_diff_should_be_small": True
            },
            {
                "name": "正向套利负基差",
                "direction": 1,
                "spot_open_price": 464.0,
                "future_open_price": 468.29,
                "expected_basis": 464.0 - 468.29,  # -4.29
                "expected_diff_should_be_small": True
            }
        ]
        
        all_passed = True
        for case in test_cases:
            # 模拟基差验证器逻辑（修复后）
            spot_price = case["spot_open_price"]
            future_price = case["future_open_price"]
            
            # 统一使用 spot_price - future_price 计算理论基差
            expected_basis = round(spot_price - future_price, 3)
            actual_basis = case["expected_basis"]
            
            difference = abs(actual_basis - expected_basis)
            
            if difference < 0.001:  # 差异应该很小
                logger.info(f"✅ {case['name']}: 实际基差={actual_basis:.3f}, "
                           f"理论基差={expected_basis:.3f}, 差异={difference:.6f}")
            else:
                logger.error(f"❌ {case['name']}: 实际基差={actual_basis:.3f}, "
                            f"理论基差={expected_basis:.3f}, 差异={difference:.6f}")
                all_passed = False
        
        self.test_results["basis_calculation_fix"] = all_passed
        return all_passed
    
    def test_selector_updates(self):
        """测试选择器更新"""
        logger.info("测试选择器更新...")
        
        # 检查选择器文件是否已更新
        selector_files = [
            "backend/services/spot_service.py",
            "backend/services/spot_close_position.py"
        ]
        
        expected_updates = {
            "spot_service.py": [
                ".puwidth[data-v-14224f5e]",
                ".feedingback[data-v-14224f5e]",
                ".back1[data-v-14224f5e]",
                ".back2[data-v-14224f5e]"
            ],
            "spot_close_position.py": [
                ".puwidth[data-v-14224f5e]"
            ]
        }
        
        all_updated = True
        for file_path in selector_files:
            try:
                with open(f"/gold/{file_path}", 'r', encoding='utf-8') as f:
                    content = f.read()
                
                file_name = file_path.split('/')[-1]
                if file_name in expected_updates:
                    for selector in expected_updates[file_name]:
                        if selector in content:
                            logger.info(f"✅ {file_name}: 找到更新的选择器 {selector}")
                        else:
                            logger.error(f"❌ {file_name}: 未找到更新的选择器 {selector}")
                            all_updated = False
                
            except Exception as e:
                logger.error(f"读取文件失败 {file_path}: {e}")
                all_updated = False
        
        self.test_results["selector_updates"] = all_updated
        return all_updated
    
    def test_order_model_fix(self):
        """测试订单模型修复"""
        logger.info("测试订单模型修复...")
        
        try:
            # 检查订单模型文件是否已修复
            with open("/gold/backend/models/order.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否移除了错误的方向判断
            has_old_logic = "if direction == 1:" in content and "else:" in content and "expected_basis = round(spot_price - future_price, 3)" in content
            has_unified_logic = "统一使用 spot_price - future_price" in content
            
            if has_unified_logic and not has_old_logic:
                logger.info("✅ 订单模型已修复：使用统一的基差计算逻辑")
                self.test_results["order_model_fix"] = True
                return True
            else:
                logger.error("❌ 订单模型未正确修复")
                self.test_results["order_model_fix"] = False
                return False
                
        except Exception as e:
            logger.error(f"检查订单模型修复失败: {e}")
            self.test_results["order_model_fix"] = False
            return False
    
    def test_real_world_scenario(self):
        """测试真实世界场景"""
        logger.info("测试真实世界场景...")
        
        # 基于报错日志中的实际数据
        real_scenarios = [
            {
                "name": "报错场景1",
                "direction": -1,  # 反向套利
                "spot_price": 765.11,  # 现货买入价
                "future_price": 769.72,  # 期货卖出价
                "reported_actual": -4.29,
                "reported_theoretical": 4.29  # 这是错误的理论值
            },
            {
                "name": "报错场景2", 
                "direction": -1,  # 反向套利
                "spot_price": 765.8,   # 现货卖出价
                "future_price": 769.76,  # 期货买入价
                "reported_actual": -4.3,
                "reported_theoretical": 4.3   # 这是错误的理论值
            }
        ]
        
        all_fixed = True
        for scenario in real_scenarios:
            # 使用修复后的逻辑计算
            correct_theoretical = round(scenario["spot_price"] - scenario["future_price"], 3)
            actual = scenario["reported_actual"]
            
            # 计算差异
            difference = abs(actual - correct_theoretical)
            
            logger.info(f"场景: {scenario['name']}")
            logger.info(f"  方向: {scenario['direction']} ({'反向' if scenario['direction'] == -1 else '正向'})")
            logger.info(f"  现货价格: {scenario['spot_price']}")
            logger.info(f"  期货价格: {scenario['future_price']}")
            logger.info(f"  实际基差: {actual}")
            logger.info(f"  修复前理论基差: {scenario['reported_theoretical']} (错误)")
            logger.info(f"  修复后理论基差: {correct_theoretical} (正确)")
            logger.info(f"  修复前差异: {abs(actual - scenario['reported_theoretical']):.6f} (很大)")
            logger.info(f"  修复后差异: {difference:.6f} (应该很小)")
            
            if difference < 0.1:  # 差异应该很小
                logger.info(f"✅ {scenario['name']}: 修复成功，差异从 {abs(actual - scenario['reported_theoretical']):.1f} 降到 {difference:.6f}")
            else:
                logger.error(f"❌ {scenario['name']}: 修复失败，差异仍然很大: {difference:.6f}")
                all_fixed = False
        
        self.test_results["real_world_scenario"] = all_fixed
        return all_fixed
    
    def test_selector_compatibility(self):
        """测试选择器兼容性"""
        logger.info("测试选择器兼容性...")
        
        # 检查是否保留了备用选择器
        try:
            with open("/gold/backend/services/spot_service.py", 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有备用选择器
            has_primary = ".puwidth[data-v-14224f5e]" in content
            has_backup = ".puwidth[data-v-9a81d21c]" in content
            has_fallback = '".puwidth"' in content
            
            if has_primary and has_backup and has_fallback:
                logger.info("✅ 选择器兼容性良好：包含主选择器、备用选择器和回退选择器")
                self.test_results["selector_compatibility"] = True
                return True
            else:
                logger.warning("⚠️ 选择器兼容性可能有问题")
                logger.info(f"  主选择器: {'✓' if has_primary else '✗'}")
                logger.info(f"  备用选择器: {'✓' if has_backup else '✗'}")
                logger.info(f"  回退选择器: {'✓' if has_fallback else '✗'}")
                self.test_results["selector_compatibility"] = False
                return False
                
        except Exception as e:
            logger.error(f"检查选择器兼容性失败: {e}")
            self.test_results["selector_compatibility"] = False
            return False
    
    def generate_report(self):
        """生成测试报告"""
        logger.info("=== 选择器修复测试报告 ===")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试数: {passed_tests}")
        logger.info(f"失败测试数: {total_tests - passed_tests}")
        logger.info(f"通过率: {(passed_tests / total_tests * 100):.1f}%")
        
        logger.info("\n详细结果:")
        test_names = {
            "basis_calculation_fix": "基差计算修复",
            "selector_updates": "选择器更新",
            "order_model_fix": "订单模型修复",
            "real_world_scenario": "真实场景测试",
            "selector_compatibility": "选择器兼容性"
        }
        
        for test_key, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            test_name = test_names.get(test_key, test_key)
            logger.info(f"  {test_name}: {status}")
        
        overall_success = passed_tests == total_tests
        if overall_success:
            logger.info("\n🎉 所有测试通过！选择器修复成功！")
            logger.info("\n✅ 修复内容总结:")
            logger.info("  1. 基差计算逻辑统一为 spot_price - future_price")
            logger.info("  2. 现货平仓选择器更新为 data-v-14224f5e")
            logger.info("  3. 保留备用选择器确保兼容性")
            logger.info("  4. 修复了基差差异过大的问题")
            logger.info("  5. 修复了现货订单查找失败的问题")
        else:
            logger.warning(f"\n⚠️ 有 {total_tests - passed_tests} 个测试失败，需要进一步修复")
        
        return overall_success

def main():
    """主函数"""
    logger.info("开始测试选择器修复...")
    
    tester = SelectorFixTester()
    
    try:
        # 执行各项测试
        tester.test_basis_calculation_fix()
        tester.test_selector_updates()
        tester.test_order_model_fix()
        tester.test_real_world_scenario()
        tester.test_selector_compatibility()
        
        # 生成测试报告
        overall_success = tester.generate_report()
        
        return overall_success
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
