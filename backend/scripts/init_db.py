"""
数据库初始化脚本
"""
import psycopg2
import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import setup_logger

# 设置日志
logger = setup_logger("init_db", "logs/init_db.log")

def create_tables(conn):
    """
    创建数据库表

    Args:
        conn: 数据库连接
    """
    try:
        with conn.cursor() as cur:
            # 创建数据库版本表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS db_version (
                id SERIAL PRIMARY KEY,
                version VARCHAR(20) NOT NULL,
                description TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            """)

            # 检查数据库版本
            cur.execute("SELECT version FROM db_version ORDER BY id DESC LIMIT 1")
            version_record = cur.fetchone()
            current_version = "1.0.0"  # 当前代码的数据库版本

            if version_record and version_record[0] == current_version:
                # 数据库版本匹配，检查所有表是否存在
                tables_to_check = [
                    'users', 'accounts', 'settings', 'orders', 'risk_limits',
                    'risk_metrics', 'risk_logs', 'auto_trade_settings'
                ]

                # 检查所有表是否存在
                cur.execute("""
                SELECT table_name FROM information_schema.tables
                WHERE table_schema = 'public' AND table_name = ANY(%s)
                """, (tables_to_check,))

                existing_tables = [row[0] for row in cur.fetchall()]

                if len(existing_tables) == len(tables_to_check):
                    logger.info("所有数据库表已存在，无需创建")
                    return

                missing_tables = [table for table in tables_to_check if table not in existing_tables]
                logger.info(f"以下表需要创建: {', '.join(missing_tables)}")

            # 创建用户表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id VARCHAR(36) PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password_hash VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                phone VARCHAR(20),
                role VARCHAR(20) NOT NULL DEFAULT 'user',
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                last_login TIMESTAMP,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            """)

            # 创建账户表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id VARCHAR(36) PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL REFERENCES users(id),
                account_type VARCHAR(10) NOT NULL,
                platform_name VARCHAR(100) NOT NULL,
                platform_url VARCHAR(200),
                contract_code VARCHAR(20),
                username VARCHAR(50) NOT NULL,
                password_encrypted VARCHAR(200) NOT NULL,
                cost_per_gram DECIMAL(10, 4) NOT NULL,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                connection_status VARCHAR(20) NOT NULL DEFAULT 'disconnected',
                last_connection_time TIMESTAMP,
                -- CTP交易所需的字段
                broker_id VARCHAR(50),
                td_server VARCHAR(255),
                md_server VARCHAR(255),
                app_id VARCHAR(100),
                auth_code VARCHAR(100),
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                UNIQUE (user_id, account_type)
            )
            """)

            # 创建设置表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS settings (
                id SERIAL PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL REFERENCES users(id),
                target_profit_diff DECIMAL(10, 4) NOT NULL DEFAULT 0.5,
                position_size INTEGER NOT NULL DEFAULT 1,
                max_positions INTEGER NOT NULL DEFAULT 5,
                max_daily_cycles INTEGER NOT NULL DEFAULT 10,
                spot_refresh_rate INTEGER NOT NULL DEFAULT 3,
                base_weight INTEGER NOT NULL DEFAULT 1000,
                forward_basis_ranges JSONB NOT NULL DEFAULT '[]',
                reverse_basis_ranges JSONB NOT NULL DEFAULT '[]',
                auto_trade_enabled BOOLEAN NOT NULL DEFAULT FALSE,
                trading_times JSONB NOT NULL DEFAULT '[]',
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                UNIQUE (user_id)
            )
            """)

            # 创建订单表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS orders (
                id VARCHAR(36) PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL REFERENCES users(id),
                direction INTEGER NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'open',
                spot_order_id VARCHAR(36),
                future_order_id VARCHAR(64),  -- 扩展长度以支持TqSDK订单ID
                volume INTEGER NOT NULL DEFAULT 1,
                base_weight INTEGER NOT NULL DEFAULT 1000,

                -- 开仓信息
                spot_open_price DECIMAL(10, 2) NOT NULL,
                future_open_price DECIMAL(10, 2) NOT NULL,
                open_basis DECIMAL(10, 2) NOT NULL,
                target_basis DECIMAL(10, 2) NOT NULL,
                open_time TIMESTAMP NOT NULL,

                -- 平仓信息
                spot_close_price DECIMAL(10, 2),
                future_close_price DECIMAL(10, 2),
                close_basis DECIMAL(10, 2),
                close_time TIMESTAMP,
                spot_close_order_id VARCHAR(36),
                future_close_order_id VARCHAR(64),  -- 扩展长度以支持TqSDK订单ID
                cancel_reason VARCHAR(200),

                -- 交易成本
                spot_cost DECIMAL(10, 2) NOT NULL,
                future_cost DECIMAL(10, 2) NOT NULL,
                total_cost DECIMAL(10, 2) NOT NULL,

                -- 盈亏信息
                spot_pnl DECIMAL(10, 2),
                future_pnl DECIMAL(10, 2),
                total_pnl DECIMAL(10, 2),
                net_profit DECIMAL(10, 2),

                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            """)

            # 创建风控限制表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS risk_limits (
                id SERIAL PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL REFERENCES users(id),
                daily_pnl_limit DECIMAL(10, 2) NOT NULL DEFAULT 10000.0,
                max_drawdown_limit DECIMAL(10, 2) NOT NULL DEFAULT 5000.0,
                max_positions INTEGER NOT NULL DEFAULT 10,
                max_daily_trades INTEGER NOT NULL DEFAULT 50,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                UNIQUE (user_id)
            )
            """)

            # 创建风控指标表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS risk_metrics (
                id SERIAL PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL REFERENCES users(id),
                daily_pnl DECIMAL(10, 2) NOT NULL DEFAULT 0.0,
                max_drawdown DECIMAL(10, 2) NOT NULL DEFAULT 0.0,
                risk_status VARCHAR(20) NOT NULL DEFAULT 'normal',
                metrics_data JSONB,
                date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            """)

            # 创建风控日志表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS risk_logs (
                id SERIAL PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL REFERENCES users(id),
                log_id VARCHAR(36) NOT NULL,
                type VARCHAR(20) NOT NULL DEFAULT 'risk_alert',
                message TEXT NOT NULL,
                metrics_data JSONB,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            """)

            # 创建自动交易设置表
            cur.execute("""
            CREATE TABLE IF NOT EXISTS auto_trade_settings (
                id SERIAL PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL REFERENCES users(id),
                enabled BOOLEAN NOT NULL DEFAULT FALSE,
                max_positions INTEGER NOT NULL DEFAULT 5,
                max_daily_trades INTEGER NOT NULL DEFAULT 20,
                settings_data JSONB,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                UNIQUE (user_id)
            )
            """)

            # 更新数据库版本
            # 记录数据库版本操作的状态
            db_version_status = {
                "success": [],
                "failed": []
            }

            try:
                cur.execute("SELECT version FROM db_version ORDER BY id DESC LIMIT 1")
                version_record = cur.fetchone()
                current_version = "1.0.0"  # 当前代码的数据库版本
                db_version_status["success"].append("查询数据库版本")

                if not version_record or version_record[0] != current_version:
                    try:
                        cur.execute("""
                        INSERT INTO db_version (version, description)
                        VALUES (%s, %s)
                        """, (current_version, "初始数据库结构"))
                        logger.info(f"数据库版本已更新至 {current_version}")
                        db_version_status["success"].append("更新数据库版本")
                    except psycopg2.Error as e:
                        if "permission denied" in str(e).lower() or "must be owner" in str(e).lower():
                            logger.warning(f"无权限更新数据库版本: {e}")
                            logger.warning("注意: 无法更新数据库版本可能会导致未来的数据库迁移出现问题")
                            db_version_status["failed"].append("更新数据库版本")
                        else:
                            raise
            except psycopg2.Error as e:
                if "permission denied" in str(e).lower() or "must be owner" in str(e).lower():
                    logger.warning(f"无权限查询数据库版本: {e}")
                    logger.warning("注意: 无法查询数据库版本可能会导致未来的数据库迁移出现问题")
                    db_version_status["failed"].append("查询数据库版本")
                else:
                    raise

            # 记录数据库版本操作的总体状态
            if db_version_status["failed"]:
                logger.warning(f"数据库版本操作部分失败: {len(db_version_status['failed'])}/{len(db_version_status['success']) + len(db_version_status['failed'])} 个操作失败")
                logger.warning("要解决这些权限问题，请联系数据库管理员执行以下操作之一:")
                logger.warning("1. 授予当前用户对db_version表的所有者权限: ALTER TABLE db_version OWNER TO current_user;")
                logger.warning("2. 授予当前用户对db_version表的所有权限: GRANT ALL PRIVILEGES ON TABLE db_version TO current_user;")
            else:
                logger.info("所有数据库版本操作成功完成")

            conn.commit()
            logger.info("数据库表创建成功")
    except Exception as e:
        conn.rollback()
        logger.error(f"创建数据库表失败: {e}")
        raise

def create_admin_user(conn):
    """
    创建管理员用户

    Args:
        conn: 数据库连接
    """
    try:
        with conn.cursor() as cur:
            # 检查管理员用户是否已存在
            cur.execute("SELECT id FROM users WHERE username = 'admin'")
            admin_user = cur.fetchone()
            if admin_user:
                logger.info("管理员用户已存在")
                return admin_user[0]  # 返回管理员用户ID

            # 创建管理员用户
            from passlib.context import CryptContext
            import uuid

            admin_id = str(uuid.uuid4())
            pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
            password_hash = pwd_context.hash("admin")

            cur.execute("""
            INSERT INTO users (id, username, password_hash, email, role)
            VALUES (%s, %s, %s, %s, %s)
            """, (admin_id, "admin", password_hash, "<EMAIL>", "admin"))

            conn.commit()
            logger.info("管理员用户创建成功")
            return admin_id
    except Exception as e:
        conn.rollback()
        logger.error(f"创建管理员用户失败: {e}")
        raise

def create_default_risk_data(conn, user_id):
    """
    创建默认风控数据

    Args:
        conn: 数据库连接
        user_id: 用户ID
    """
    try:
        with conn.cursor() as cur:
            # 检查用户的风控限制是否已存在
            cur.execute("SELECT id FROM risk_limits WHERE user_id = %s", (user_id,))
            if cur.fetchone():
                logger.info(f"用户 {user_id} 的风控限制已存在")
            else:
                # 创建默认风控限制
                cur.execute("""
                INSERT INTO risk_limits (user_id, daily_pnl_limit, max_drawdown_limit, max_positions, max_daily_trades)
                VALUES (%s, %s, %s, %s, %s)
                """, (user_id, 10000.0, 5000.0, 10, 50))
                logger.info(f"用户 {user_id} 的默认风控限制创建成功")

            # 检查用户的自动交易设置是否已存在
            cur.execute("SELECT id FROM auto_trade_settings WHERE user_id = %s", (user_id,))
            if cur.fetchone():
                logger.info(f"用户 {user_id} 的自动交易设置已存在")
            else:
                # 创建默认自动交易设置
                cur.execute("""
                INSERT INTO auto_trade_settings (user_id, enabled, max_positions, max_daily_trades)
                VALUES (%s, %s, %s, %s)
                """, (user_id, False, 5, 20))
                logger.info(f"用户 {user_id} 的默认自动交易设置创建成功")

            # 检查用户的风控指标是否已存在
            cur.execute("SELECT id FROM risk_metrics WHERE user_id = %s", (user_id,))
            if cur.fetchone():
                logger.info(f"用户 {user_id} 的风控指标已存在")
            else:
                # 创建初始风控指标
                import json
                from datetime import datetime

                cur.execute("""
                INSERT INTO risk_metrics (user_id, daily_pnl, max_drawdown, risk_status, metrics_data, date)
                VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    user_id,
                    0.0,
                    0.0,
                    'normal',
                    json.dumps({
                        "position_count": 0,
                        "daily_trade_count": 0,
                        "last_updated": datetime.now().isoformat()
                    }),
                    datetime.now()
                ))
                logger.info(f"用户 {user_id} 的初始风控指标创建成功")

            conn.commit()
    except Exception as e:
        conn.rollback()
        logger.error(f"创建默认风控数据失败: {e}")
        raise

def main():
    """主函数"""
    try:
        # 连接数据库
        conn = psycopg2.connect(
            host=os.getenv("POSTGRES_HOST", "localhost"),
            port=os.getenv("POSTGRES_PORT", "5432"),
            dbname=os.getenv("POSTGRES_DB", "gold_arbitrage"),
            user=os.getenv("POSTGRES_USER", "gold_user"),
            password=os.getenv("POSTGRES_PASSWORD", "gold_password")
        )

        # 创建数据库表
        create_tables(conn)

        # 创建管理员用户
        admin_id = create_admin_user(conn)

        # 创建默认风控数据
        if admin_id:
            create_default_risk_data(conn, admin_id)

        # 关闭连接
        conn.close()

        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
