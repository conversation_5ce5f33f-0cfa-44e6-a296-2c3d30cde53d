/**
 * 订单服务
 * 用于管理订单数据的获取和更新
 */
import { store } from '../redux/store';
import {
  fetchOrdersStart,
  fetchPositionsSuccess,
  fetchHistorySuccess,
  fetchOrdersFailure
} from '../redux/ordersSlice';
import { getPositions, getHistoryOrders, refreshPositions, closePosition } from '../api/ordersApi';
import notificationManager from '../utils/notificationManager';
import tokenManager from '../utils/tokenManager';
import { Order } from '../types/api';

class OrderService {
  private refreshInterval: NodeJS.Timeout | null = null;
  private isInitialized: boolean = false;
  private autoRefreshEnabled: boolean = true;
  private refreshRate: number = 10000; // 默认10秒刷新一次

  // 持仓筛选和排序状态
  private positionSortBy: string = 'open_time';
  private positionSortOrder: 'asc' | 'desc' = 'desc';
  private positionFilter: { direction?: 1 | -1, minPnl?: number, maxPnl?: number } | undefined = undefined;

  // 历史订单筛选和排序状态
  private historySortBy: string = 'close_time';
  private historySortOrder: 'asc' | 'desc' = 'desc';
  private historyFilter: {
    direction?: 1 | -1,
    status?: 'closed' | 'cancelled' | 'error',
    startDate?: string,
    endDate?: string,
    minProfit?: number,
    maxProfit?: number,
    search?: string
  } | undefined = undefined;

  /**
   * 初始化订单服务
   */
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // 立即获取一次数据
    this.fetchPositions();

    // 设置定时刷新
    this.setAutoRefresh(true);

    // 设置为已初始化
    this.isInitialized = true;

    console.log('订单服务已初始化');
  }

  /**
   * 设置自动刷新
   * @param enabled 是否启用自动刷新
   * @param rate 刷新间隔（毫秒）
   */
  public setAutoRefresh(enabled: boolean, rate?: number): void {
    this.autoRefreshEnabled = enabled;

    if (rate !== undefined) {
      this.refreshRate = rate;
    }

    // 清除现有的刷新定时器
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }

    // 如果启用自动刷新，设置新的定时器
    if (enabled) {
      this.refreshInterval = setInterval(() => {
        // 使用静默刷新，不显示加载状态和错误通知
        this.fetchPositions(undefined, undefined, undefined, true);
      }, this.refreshRate);

      console.log(`已启用持仓自动刷新，间隔: ${this.refreshRate}ms`);
    } else {
      console.log('已禁用持仓自动刷新');
    }
  }

  // 用于跟踪持仓数据的缓存
  private positionsCache: {
    data: any;
    timestamp: number;
    sortBy: string;
    sortOrder: 'asc' | 'desc';
    filter: any;
  } | null = null;

  // 缓存过期时间（毫秒）
  private positionsCacheExpiry: number = 2000; // 2秒

  // 是否正在加载持仓数据
  private isLoadingPositions: boolean = false;

  // 最后一次请求时间
  private lastPositionsRequestTime: number = 0;

  // 请求节流时间（毫秒）
  private positionsThrottleTime: number = 2000; // 2秒

  /**
   * 获取持仓列表
   * @param sortBy 排序字段
   * @param sortOrder 排序方向
   * @param filter 筛选条件
   * @param silent 是否静默刷新，不显示加载状态和错误通知
   * @param forceRefresh 是否强制刷新，忽略缓存和节流
   */
  public async fetchPositions(
    sortBy?: string,
    sortOrder?: 'asc' | 'desc',
    filter?: { direction?: 1 | -1, minPnl?: number, maxPnl?: number },
    silent: boolean = false,
    forceRefresh: boolean = false
  ): Promise<void> {
    // 防止重复请求
    if (this.isLoadingPositions) {
      console.log('已有持仓请求正在进行中，跳过此次请求');
      return;
    }

    // 请求节流 - 除非强制刷新，否则限制请求频率
    const now = Date.now();
    if (!forceRefresh && now - this.lastPositionsRequestTime < this.positionsThrottleTime) {
      console.log('持仓请求过于频繁，跳过此次请求');
      return;
    }

    // 更新排序和筛选状态
    if (sortBy !== undefined) {
      this.positionSortBy = sortBy;
    }
    if (sortOrder !== undefined) {
      this.positionSortOrder = sortOrder;
    }
    if (filter !== undefined) {
      this.positionFilter = filter;
    }

    // 检查是否可以使用缓存
    if (!forceRefresh && this.positionsCache) {
      const cacheAge = now - this.positionsCache.timestamp;

      // 如果缓存未过期，且请求参数与缓存参数相同，则使用缓存
      if (cacheAge < this.positionsCacheExpiry &&
          this.positionsCache.sortBy === this.positionSortBy &&
          this.positionsCache.sortOrder === this.positionSortOrder &&
          JSON.stringify(this.positionsCache.filter) === JSON.stringify(this.positionFilter)) {

        console.log('使用持仓缓存数据');

        // 分发缓存数据
        store.dispatch(fetchPositionsSuccess({
          positions: this.positionsCache.data.positions,
          totalPnl: this.positionsCache.data.total_pnl
        }));

        return;
      }
    }

    this.isLoadingPositions = true;
    this.lastPositionsRequestTime = now;

    try {
      // 分发开始获取数据的action，如果不是静默刷新
      if (!silent) {
        store.dispatch(fetchOrdersStart());
      }

      // 获取数据
      const data = await getPositions(
        this.positionSortBy,
        this.positionSortOrder,
        this.positionFilter
      );

      // 更新缓存
      this.positionsCache = {
        data,
        timestamp: now,
        sortBy: this.positionSortBy,
        sortOrder: this.positionSortOrder,
        filter: this.positionFilter
      };

      // 分发获取数据成功的action
      store.dispatch(fetchPositionsSuccess({
        positions: data.positions,
        totalPnl: data.total_pnl
      }));
    } catch (error) {
      console.error('获取持仓列表失败:', error);

      // 分发获取数据失败的action，如果不是静默刷新
      if (!silent) {
        store.dispatch(fetchOrdersFailure(error instanceof Error ? error.message : '未知错误'));

        // 显示错误通知
        notificationManager.error('获取持仓列表失败', '请检查网络连接');
      }
    } finally {
      this.isLoadingPositions = false;
    }
  }

  // 用于跟踪历史订单数据的缓存
  private historyCache: {
    data: any;
    timestamp: number;
    page: number;
    pageSize: number;
    sortBy: string;
    sortOrder: 'asc' | 'desc';
    filter: any;
  } | null = null;

  // 缓存过期时间（毫秒）
  private historyCacheExpiry: number = 60000; // 1分钟

  // 是否正在加载历史订单
  private isLoadingHistory: boolean = false;

  /**
   * 获取历史订单列表
   * @param page 页码
   * @param pageSize 每页数量
   * @param reset 是否重置（加载第一页）
   * @param sortBy 排序字段
   * @param sortOrder 排序方向
   * @param filter 筛选条件
   * @param forceRefresh 是否强制刷新，忽略缓存
   */
  public async fetchHistoryOrders(
    page: number = 1,
    pageSize: number = 10,
    reset: boolean = false,
    sortBy?: string,
    sortOrder?: 'asc' | 'desc',
    filter?: {
      direction?: 1 | -1,
      status?: 'closed' | 'cancelled' | 'error',
      startDate?: string,
      endDate?: string,
      minProfit?: number,
      maxProfit?: number,
      search?: string
    },
    forceRefresh: boolean = false
  ): Promise<void> {
    // 记录请求参数，便于调试
    console.log('fetchHistoryOrders 参数:', {
      page, pageSize, reset, sortBy, sortOrder, filter, forceRefresh,
      isLoadingHistory: this.isLoadingHistory
    });

    // 防止重复请求
    if (this.isLoadingHistory) {
      console.log('已有历史订单请求正在进行中，跳过此次请求');
      return;
    }

    // 更新排序和筛选状态
    if (sortBy !== undefined) {
      this.historySortBy = sortBy;
    }
    if (sortOrder !== undefined) {
      this.historySortOrder = sortOrder;
    }
    if (filter !== undefined) {
      this.historyFilter = filter;
    }

    // 检查是否可以使用缓存
    if (!forceRefresh && this.historyCache && !reset) {
      const now = Date.now();
      const cacheAge = now - this.historyCache.timestamp;

      // 如果缓存未过期，且请求参数与缓存参数相同，则使用缓存
      if (cacheAge < this.historyCacheExpiry &&
          this.historyCache.page === page &&
          this.historyCache.pageSize === pageSize &&
          this.historyCache.sortBy === this.historySortBy &&
          this.historyCache.sortOrder === this.historySortOrder &&
          JSON.stringify(this.historyCache.filter) === JSON.stringify(this.historyFilter)) {

        console.log('使用历史订单缓存数据');

        // 分发缓存数据
        store.dispatch(fetchHistorySuccess({
          orders: this.historyCache.data.orders,
          totalCount: this.historyCache.data.total_count,
          page: this.historyCache.data.page,
          pageSize: this.historyCache.data.page_size
        }));

        return;
      }
    }

    // 标记为正在加载
    this.isLoadingHistory = true;
    console.log('开始加载历史订单数据');

    try {
      // 分发开始获取数据的action
      store.dispatch(fetchOrdersStart());
      console.log('已分发 fetchOrdersStart action');

      // 检查认证状态
      const token = await tokenManager.getAccessToken();
      if (!token) {
        console.warn('未找到访问令牌，可能未登录');
        // 尝试刷新令牌
        const refreshed = await tokenManager.refreshToken();
        if (!refreshed) {
          console.error('刷新令牌失败，用户可能需要重新登录');
          throw new Error('认证失败，请重新登录');
        }
      }

      // 获取数据
      console.log('开始调用 getHistoryOrders API');
      const data = await getHistoryOrders(
        page,
        pageSize,
        this.historySortBy,
        this.historySortOrder,
        this.historyFilter
      );
      console.log('成功获取历史订单数据:', {
        totalCount: data.total_count,
        ordersCount: data.orders.length,
        page: data.page,
        pageSize: data.page_size
      });

      // 更新缓存
      this.historyCache = {
        data,
        timestamp: Date.now(),
        page,
        pageSize,
        sortBy: this.historySortBy,
        sortOrder: this.historySortOrder,
        filter: this.historyFilter
      };
      console.log('已更新历史订单缓存');

      // 分发获取数据成功的action
      store.dispatch(fetchHistorySuccess({
        orders: data.orders,
        totalCount: data.total_count,
        page: data.page,
        pageSize: data.page_size
      }));
      console.log('已分发 fetchHistorySuccess action');
    } catch (error) {
      console.error('获取历史订单列表失败:', error);

      // 记录详细错误信息
      if (error instanceof Error) {
        console.error('错误详情:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
      }

      // 分发获取数据失败的action
      store.dispatch(fetchOrdersFailure(error instanceof Error ? error.message : '未知错误'));
      console.log('已分发 fetchOrdersFailure action');

      // 错误日志已记录到控制台，不需要显示错误通知
      // notificationManager.error('获取历史订单列表失败', '请检查网络连接');
    } finally {
      // 重置加载状态
      this.isLoadingHistory = false;
      console.log('历史订单加载完成，重置加载状态');
    }
  }

  /**
   * 监控持仓盈亏
   * 检查持仓是否达到预警条件，并发送通知
   * @param positions 持仓列表
   * @param thresholds 预警阈值
   */
  public monitorPositions(
    positions: Order[],
    thresholds: { profitThreshold: number, lossThreshold: number }
  ): void {
    if (!positions || positions.length === 0) {
      return;
    }

    // 检查每个持仓的盈亏情况
    positions.forEach(position => {
      const netProfit = position.net_profit || 0;

      // 盈利预警
      if (netProfit >= thresholds.profitThreshold) {
        notificationManager.success(
          '盈利目标达成',
          `订单 ${position.id} 已达到盈利目标: ${netProfit.toFixed(2)}`
        );
      }

      // 亏损预警
      if (netProfit <= -thresholds.lossThreshold) {
        notificationManager.warning(
          '亏损预警',
          `订单 ${position.id} 已达到亏损阈值: ${netProfit.toFixed(2)}`
        );
      }
    });
  }

  /**
   * 批量平仓
   * @param orderIds 订单ID列表
   */
  public async batchClosePositions(orderIds: string[]): Promise<void> {
    if (!orderIds || orderIds.length === 0) {
      return;
    }

    // 显示确认对话框
    const confirmed = window.confirm(`确定要平仓选中的 ${orderIds.length} 个订单吗？`);
    if (!confirmed) {
      return;
    }

    try {
      // 依次平仓
      for (const orderId of orderIds) {
        await this.closePosition(orderId);
      }

      // 刷新持仓列表
      await refreshPositions();

      // 显示成功通知
      notificationManager.success('批量平仓成功', `已成功平仓 ${orderIds.length} 个订单`);
    } catch (error) {
      console.error('批量平仓失败:', error);

      // 显示错误通知
      notificationManager.error('批量平仓失败', '请稍后重试');
    }
  }

  /**
   * 平仓单个订单
   * @param orderId 订单ID
   */
  private async closePosition(orderId: string): Promise<void> {
    try {
      // 调用API平仓
      await closePosition(orderId);

      // 平仓成功后刷新持仓列表
      await refreshPositions();
    } catch (error) {
      console.error(`平仓订单 ${orderId} 失败:`, error);
      throw error;
    }
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    // 清除刷新定时器
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }

    // 重置初始化状态
    this.isInitialized = false;

    console.log('订单服务已清理');
  }
}

// 导出单例
export const orderService = new OrderService();
export default orderService;
