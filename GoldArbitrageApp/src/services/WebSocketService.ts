/**
 * WebSocket服务
 * 用于与后端WebSocket服务通信，接收实时数据更新和通知
 * 支持二进制数据传输和增量更新
 */
import { updateMarketData, updateBasisHighLow } from '../redux/slices/marketSlice';
import { fetchPositionsSuccess, updateOrderStatus } from '../redux/ordersSlice';
import notificationManager, { NotificationType, NotificationPriority } from '../utils/notificationManager';
import { addNotification } from '../redux/notificationSlice';
import { store } from '../redux/store';
// 内部中间层，用于避免循环依赖
class WebSocketServiceMiddleware {
  private dispatchCallback: ((action: any) => void) | null = null;

  registerDispatchCallback(callback: (action: any) => void): void {
    this.dispatchCallback = callback;
  }

  dispatch(action: any): void {
    if (this.dispatchCallback) {
      this.dispatchCallback(action);
    }
  }
}

// 创建中间层实例
const webSocketServiceMiddleware = new WebSocketServiceMiddleware();
import { Notification } from '../types/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import * as Haptics from 'expo-haptics';
import { Audio } from 'expo-av';
// import env from '../utils/env';
import serverConfig from '../utils/serverConfig';
import logManager from '../utils/logManager';
import { normalizeMessageType } from './WebSocketService.normalizeMessageType';
import { extractAndProcessBasisData as extractBasisData } from './WebSocketService.extractBasisData';

// 简化WebSocket通信，不使用二进制数据
logManager.info('WebSocket服务初始化，使用纯文本JSON通信');

// WebSocket连接状态
export enum WebSocketStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

// WebSocket消息类型
export enum WebSocketMessageType {
  MARKET_DATA = 'market_data',
  MARKET_DATA_INCREMENTAL = 'market_data_incremental', // 增量市场数据
  POSITION_UPDATE = 'position_update',
  TRADE_EXECUTION = 'trade_execution',
  TRADE_EXECUTION_RESULT = 'trade_execution_result', // 交易执行结果
  SYSTEM_NOTIFICATION = 'system_notification',
  RISK_ALERT = 'risk_alert',
  BASIS_TRIGGER = 'basis_trigger',
  PONG = 'pong',
  HEARTBEAT = 'heartbeat',
  ERROR = 'error',
  PREFERENCES_UPDATED = 'preferences_updated', // 用户偏好设置更新
  CONNECTION_RESET = 'connection_reset' // 连接重置
}

// 创建一个包含所有消息类型的数组，用于快速检查
const ALL_MESSAGE_TYPES = Object.freeze([
  // 标准消息类型
  WebSocketMessageType.MARKET_DATA,
  WebSocketMessageType.MARKET_DATA_INCREMENTAL,
  WebSocketMessageType.POSITION_UPDATE,
  WebSocketMessageType.TRADE_EXECUTION,
  WebSocketMessageType.TRADE_EXECUTION_RESULT,
  WebSocketMessageType.SYSTEM_NOTIFICATION,
  WebSocketMessageType.RISK_ALERT,
  WebSocketMessageType.BASIS_TRIGGER,
  WebSocketMessageType.PONG,
  WebSocketMessageType.HEARTBEAT,
  WebSocketMessageType.ERROR,
  WebSocketMessageType.PREFERENCES_UPDATED,
  WebSocketMessageType.CONNECTION_RESET,

  // 字符串形式的标准消息类型
  'market_data',
  'market_data_incremental',
  'position_update',
  'trade_execution',
  'trade_execution_result',
  'system_notification',
  'risk_alert',
  'basis_trigger',
  'pong',
  'heartbeat',
  'error',
  'preferences_updated',
  'connection_reset',

  // 添加可能的小写变体和变种
  'market data incremental',
  'marketDataIncremental',
  'marketdata_incremental',
  'market-data-incremental',
  'marketdataincremental',
  'market.data.incremental',
  'market_data.incremental',
  'market.data_incremental',

  // 添加可能的基差数据变体
  'basis_data',
  'basis_update',
  'basis_values',
  'basis_extremes',
  'basistrigger',
  'basis-trigger',
  'basis.trigger',
  'basisdata',
  'basis-data',
  'basis.data',

  // 添加可能的错误消息类型变体
  'error_message',
  'error_notification',
  'server_error',
  'error-message',
  'error.message',
  'errormessage',
  'error-notification',
  'error.notification',
  'errornotification',
  'server-error',
  'server.error',
  'servererror',

  // 添加简化的消息类型
  'incremental',
  'update',
  'data',
  'basis',
  'notification',
  'alert',
  'message'
]);

class WebSocketService {
  private socket: WebSocket | null = null;
  private status: WebSocketStatus = WebSocketStatus.DISCONNECTED;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 10; // 增加最大重试次数
  private reconnectInterval: number = 5000; // 5秒，增加重连间隔
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private connectionCheckInterval: NodeJS.Timeout | null = null; // 添加连接检查定时器
  private lastPingTime: number = 0;
  private lastPongTime: number = 0; // 添加最后一次收到pong的时间
  private userId: string = 'guest'; // 默认用户ID
  private apiBaseUrl: string = '';
  private sound: Audio.Sound | null = null;
  private basisSound: Audio.Sound | null = null;

  // 日志级别，控制日志输出详细程度
  private logLevel: 'debug' | 'info' | 'warn' | 'error' = 'info';

  // 最后一次接收消息的时间戳
  private lastMessageTime: number = 0;

  // 缓存的市场数据，用于增量更新
  private cachedMarketData: any = {};

  // WebSocket连接功能支持
  private features = {
    binarySupport: false,     // 完全禁用二进制数据
    incrementalUpdates: true, // 是否支持增量更新
    autoReconnect: true       // 是否支持自动重连
  };

  // 添加registerDispatchCallback方法，用于注册dispatch回调
  public registerDispatchCallback(callback: (action: any) => void): void {
    webSocketServiceMiddleware.registerDispatchCallback(callback);
  }

  /**
   * 检查WebSocket是否已连接
   * @returns 是否已连接
   */
  public isConnected(): boolean {
    return this.status === WebSocketStatus.CONNECTED && this.socket !== null;
  }

  // 通知历史
  private notificationHistory: Notification[] = [];
  private maxHistorySize: number = 100;

  // 订阅的通知类型
  private subscribedTypes: string[] = [
    'system', 'trade', 'alert', 'basis'
  ];

  // 通知设置
  private notificationSettings = {
    sound: true,
    vibration: true,
    tradeAlerts: true,
    systemAlerts: true
  };

  // 系统通知监听器
  private systemNotificationListeners: ((data: any) => void)[] = [];

  // WebSocket设置
  private webSocketSettings = {
    useBinary: false,         // 不使用二进制数据，避免解析问题
    useIncrementalUpdates: true, // 是否使用增量更新
    compressionEnabled: false,   // 是否启用压缩 (在React Native中不支持)
    reconnectAutomatically: true // 是否自动重连
  };

  /**
   * 初始化WebSocket服务
   */
  public async initialize(apiBaseUrl?: string): Promise<void> {
    try {
      if (apiBaseUrl) {
        this.apiBaseUrl = apiBaseUrl;
      }

      // 注册dispatch回调
      try {
        // 直接使用导入的store，而不是尝试从serviceRegistry获取
        if (store && typeof store.dispatch === 'function') {
          webSocketServiceMiddleware.registerDispatchCallback((action) => {
            try {
              store.dispatch(action);
              logManager.debug('成功分发WebSocket action');
            } catch (dispatchError) {
              logManager.error('分发WebSocket action失败:', dispatchError);
              // 记录详细错误信息，帮助调试
              if (dispatchError instanceof Error) {
                logManager.error('错误详情:', dispatchError.message, dispatchError.stack);
              }
            }
          });
          logManager.info('成功注册WebSocket dispatch回调');
        } else {
          // 尝试从serviceRegistry获取store作为备选方案
          try {
            const { serviceRegistry } = require('../services/serviceRegistry');
            const registryStore = serviceRegistry.get('store');

            if (registryStore && typeof registryStore.dispatch === 'function') {
              webSocketServiceMiddleware.registerDispatchCallback((action) => {
                try {
                  registryStore.dispatch(action);
                  logManager.debug('成功通过serviceRegistry分发WebSocket action');
                } catch (dispatchError) {
                  logManager.error('通过serviceRegistry分发WebSocket action失败:', dispatchError);
                }
              });
              logManager.info('成功通过serviceRegistry注册WebSocket dispatch回调');
            } else {
              logManager.warn('无法获取有效的store，WebSocket消息将无法更新Redux状态');
            }
          } catch (registryError) {
            logManager.warn('尝试从serviceRegistry获取store失败:', registryError);
          }
        }
      } catch (e) {
        logManager.error('注册dispatch回调失败:', e);
      }

      // 初始化缓存数据
      this.cachedMarketData = {
        spot_bid: 0,
        spot_ask: 0,
        future_bid: 0,
        future_ask: 0,
        forward_basis: 0,
        reverse_basis: 0,
        timestamp: new Date().toISOString(),
        data_quality: 'initializing',
        data_source: 'initializing'
      };

      // 加载通知历史
      try {
        await this.loadNotificationHistory();
      } catch (error) {
        logManager.error('加载通知历史失败:', error);
      }

      // 加载通知设置
      try {
        await this.loadNotificationSettings();
      } catch (error) {
        logManager.error('加载通知设置失败:', error);
      }

      // 加载WebSocket设置
      try {
        await this.loadWebSocketSettings();
      } catch (error) {
        logManager.error('加载WebSocket设置失败:', error);
      }

      // 加载通知声音
      try {
        await this.loadNotificationSound();
      } catch (error) {
        logManager.error('加载通知声音失败:', error);
      }

      logManager.info('WebSocket服务初始化完成');

      // 延迟连接，确保其他服务已初始化
      setTimeout(() => {
        this.connect();
      }, 1000);
    } catch (error) {
      logManager.error('WebSocket服务初始化失败:', error);
      // 即使初始化失败，也不抛出异常，让应用程序继续运行
    }
  }

  /**
   * 加载WebSocket设置
   */
  private async loadWebSocketSettings(): Promise<void> {
    try {
      const settings = await AsyncStorage.getItem('websocket_settings');

      if (settings) {
        this.webSocketSettings = {
          ...this.webSocketSettings,
          ...JSON.parse(settings)
        };
        logManager.info('WebSocket设置加载成功:', this.webSocketSettings);
      }
    } catch (error) {
      logManager.error('加载WebSocket设置失败:', error);
    }
  }

  /**
   * 更新WebSocket设置
   * @param settings WebSocket设置
   */
  public async updateWebSocketSettings(settings: any): Promise<void> {
    // 强制禁用二进制数据和压缩
    const updatedSettings = {
      ...settings,
      useBinary: false, // 禁用二进制数据
      compressionEnabled: false // 在React Native中不支持压缩
    };

    this.webSocketSettings = {
      ...this.webSocketSettings,
      ...updatedSettings
    };

    // 保存设置到本地存储
    await AsyncStorage.setItem(
      'websocket_settings',
      JSON.stringify(this.webSocketSettings)
    );

    // 发送设置到服务器
    if (this.status === WebSocketStatus.CONNECTED) {
      this.sendMessage({
        type: 'set_preferences',
        data: {
          use_binary: false, // 告诉服务器不使用二进制数据
          use_incremental: this.webSocketSettings.useIncrementalUpdates
        }
      });
    }

    logManager.info('WebSocket设置已更新:', this.webSocketSettings);
  }

  /**
   * 加载通知声音
   */
  private async loadNotificationSound(): Promise<void> {
    try {
      // 先卸载现有声音（如果存在）
      if (this.sound) {
        try {
          await this.sound.unloadAsync();
          this.sound = null;
        } catch (unloadError) {
          logManager.warn('卸载现有通知声音失败:', unloadError);
        }
      }

      // 使用本地声音文件
      const { sound } = await Audio.Sound.createAsync(
        require('../assets/sounds/notification.mp3'),
        { volume: 1.0 } // 设置最大音量
      );

      // 保存声音对象
      this.sound = sound;

      // 设置声音播放完成后的回调
      this.sound.setOnPlaybackStatusUpdate((status: any) => {
        if (status.didJustFinish) {
          logManager.debug('通知声音播放完成');
        }
      });

      logManager.info('通知声音加载成功');
    } catch (error) {
      logManager.error('加载通知声音失败:', error);
    }
  }

  /**
   * 播放通知声音
   * @param forcePlay 是否强制播放（忽略用户设置）
   */
  private async playNotificationSound(forcePlay: boolean = false): Promise<void> {
    // 如果没有声音对象或用户禁用了声音（且不是强制播放），则直接返回
    if (!this.sound || (!this.notificationSettings.sound && !forcePlay)) {
      logManager.debug('通知声音未播放: 声音对象不存在或用户已禁用声音');
      return;
    }

    try {
      // 确保声音从头开始播放
      await this.sound.setPositionAsync(0);

      // 播放声音
      const playbackStatus = await this.sound.playAsync();

      // 记录播放状态
      logManager.debug(`通知声音播放状态: ${JSON.stringify(playbackStatus)}`);

      // 如果声音没有开始播放，尝试重新加载并播放
      if (!(playbackStatus as any).isPlaying) {
        logManager.warn('通知声音未开始播放，尝试重新加载');
        await this.loadNotificationSound();
        await this.sound?.playAsync();
      }
    } catch (error) {
      logManager.error('播放通知声音失败:', error);

      // 如果播放失败，尝试重新加载声音
      try {
        logManager.info('尝试重新加载通知声音');
        await this.loadNotificationSound();
        if (this.sound) {
          await this.sound.playAsync();
          logManager.info('重新加载后通知声音播放成功');
        }
      } catch (reloadError) {
        logManager.error('重新加载通知声音失败:', reloadError);
      }
    }
  }

  /**
   * 触发震动
   */
  private triggerVibration(type: NotificationType): void {
    if (!this.notificationSettings.vibration || Platform.OS === 'web') return;

    try {
      switch (type) {
        case NotificationType.SUCCESS:
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
        case NotificationType.WARNING:
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
        case NotificationType.ERROR:
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
        default:
          Haptics.selectionAsync();
          break;
      }
    } catch (error) {
      logManager.error('触发震动失败:', error);
    }
  }

  /**
   * 连接WebSocket
   * @param userId 用户ID
   */
  public connect(userId: string = 'guest'): void {
    if (this.socket && this.status === WebSocketStatus.CONNECTED) {
      logManager.info('WebSocket已连接');
      return;
    }

    // 检查网络状态
    try {
      // 直接尝试连接，不依赖动态导入
      this.initializeConnection(userId);
    } catch (error) {
      logManager.error('初始化连接失败:', error);
    }
  }

  /**
   * 初始化WebSocket连接
   * @param userId 用户ID
   */
  private async initializeConnection(userId: string): Promise<void> {
    this.userId = userId;
    this.status = WebSocketStatus.CONNECTING;

    // 从 AsyncStorage 获取认证令牌
    try {
      // 使用已导入的AsyncStorage
      const token = await AsyncStorage.getItem('gold_auth_token');
      if (!token) {
        // 检查是否已登录
        const state = store.getState();
        const isAuthenticated = state.auth?.isAuthenticated || false;

        if (isAuthenticated) {
          logManager.warn('未找到认证令牌，无法建立WebSocket连接');
          notificationManager.warning(
            '认证错误',
            '请先登录以建立实时数据连接'
          );
        } else {
          // 未登录状态下，只记录日志，不显示通知
          logManager.info('用户未登录，不建立WebSocket连接');
        }
        return;
      }
      this.connectWithToken(token, userId);
    } catch (error) {
      logManager.error('获取认证令牌失败:', error);
    }
  }

  /**
   * 使用令牌连接WebSocket
   * @param token 认证令牌
   * @param userId 用户ID
   */
  private connectWithToken(token: string, _userId: string): void {
    // 使用硬编码的服务器配置，确保在移动设备上使用正确的服务器地址
    // 根据后端代码，正确的路径是 /ws 而不是 /api/ws
    const wsUrl = `${serverConfig.WS_URL}?token=${token}`;
    logManager.info('WebSocket URL:', wsUrl);

    try {
      // 设置连接超时
      const connectionTimeout = setTimeout(() => {
        if (this.status === WebSocketStatus.CONNECTING) {
          logManager.warn('WebSocket连接超时');
          if (this.socket) {
            this.socket.close();
            this.socket = null;
          }
          this.status = WebSocketStatus.ERROR;
          this.reconnect();
        }
      }, 15000); // 15秒超时

      this.socket = new WebSocket(wsUrl);

      // 连接打开事件
      this.socket.onopen = () => {
        // 清除连接超时定时器
        clearTimeout(connectionTimeout);

        logManager.info('WebSocket连接成功');
        this.status = WebSocketStatus.CONNECTED;
        this.reconnectAttempts = 0;
        // 心跳检测已移除
        // this.startHeartbeat();

        // 订阅通知类型
        this.subscribeToNotifications();

        // 显示连接成功通知
        notificationManager.success(
          '连接成功',
          '实时数据连接已建立'
        );
      };

      // 接收消息事件
      this.socket.onmessage = (event) => {
        try {
          // 记录最后一次消息接收时间
          this.lastMessageTime = Date.now();

          // 只处理文本消息
          if (typeof event.data !== 'string') {
            logManager.info('收到非文本数据，忽略');
            return;
          }

          let data = event.data;

          // 检查是否是空字符串
          if (!data || data.trim() === '') {
            logManager.warn('收到空消息，忽略');
            return;
          }

          // 检查是否是特殊格式的对象字符串
          if (data === '[object Object]' ||
              data.includes('[object Object]') ||
              data.startsWith('object') ||
              data.startsWith('[object')) {
            // 静默忽略这种特殊格式的字符串
            logManager.info('忽略特殊格式的对象字符串:', data);
            return;
          }

          // 处理以 "o" 开头的消息
          if (data.startsWith('o') && !data.startsWith('object')) {
            // 移除开头的 "o" 字符
            data = data.substring(1).trim();
            logManager.info('处理以"o"开头的消息，已移除前缀');
          }

          // 尝试解析JSON
          try {
            const message = JSON.parse(data);
            this.processMessage(message);
          } catch (jsonError) {
            // JSON解析失败，尝试提取JSON部分
            try {
              const jsonRegex = /{.*}/s;
              const match = jsonRegex.exec(data);
              if (match) {
                const jsonPart = match[0];
                try {
                  const message = JSON.parse(jsonPart);
                  logManager.info('成功从非标准格式消息中提取并解析JSON部分');
                  this.processMessage(message);
                } catch (parseError) {
                  logManager.info('解析提取的JSON部分失败:', parseError);

                  // 尝试作为完整市场数据处理
                  if (data.includes('future_bid') || data.includes('future_ask')) {
                    logManager.info('尝试作为完整市场数据处理');
                    // 简化处理，不再尝试提取数字
                    this.handleMarketData({
                      timestamp: new Date().toISOString(),
                      data_quality: 'realtime',
                      data_source: 'websocket'
                    });
                  }
                }
              } else {
                // 尝试提取基差数据
                if (this.extractAndProcessBasisData(data)) {
                  logManager.info('成功提取并处理基差数据');
                } else {
                  logManager.info('无法解析消息，尝试作为完整市场数据处理');

                  // 尝试作为完整市场数据处理
                  if (data.includes('future_bid') || data.includes('future_ask')) {
                    // 简化处理，不再尝试提取数字
                    this.handleMarketData({
                      timestamp: new Date().toISOString(),
                      data_quality: 'realtime',
                      data_source: 'websocket'
                    });
                  }
                }
              }
            } catch (extractError) {
              logManager.info('提取JSON部分失败:', extractError);
            }
          }
        } catch (messageError) {
          // 捕获并记录消息处理错误，但不中断WebSocket连接
          this.lastMessageTime = Date.now();
          logManager.info('处理WebSocket消息事件失败:', messageError);
        }
      };

      // 连接关闭事件
      this.socket.onclose = () => {
        logManager.info('WebSocket连接关闭');
        this.status = WebSocketStatus.DISCONNECTED;
        // 心跳检测已移除
        // this.stopHeartbeat();
        this.reconnect();

        // 显示连接断开通知
        notificationManager.warning(
          '连接断开',
          '实时数据连接已断开，正在尝试重连...'
        );
      };

      // 连接错误事件
      this.socket.onerror = (error) => {
        logManager.error('WebSocket连接错误:', error);
        this.status = WebSocketStatus.ERROR;

        // 显示连接错误通知，包含服务器地址信息
        notificationManager.error(
          '连接错误',
          `无法连接到服务器 (${wsUrl.split('/')[2]})，请检查网络或联系管理员`
        );

        // 连接错误时自动触发重连
        this.reconnect();
      };
    } catch (error) {
      logManager.error('创建WebSocket连接失败:', error);
      this.status = WebSocketStatus.ERROR;
    }
  }

  /**
   * 断开WebSocket连接
   */
  public disconnect(): void {
    if (this.socket) {
      // 心跳检测已移除
      // this.stopHeartbeat();
      this.socket.close();
      this.socket = null;
      this.status = WebSocketStatus.DISCONNECTED;
      logManager.info('WebSocket连接已断开');
    }
  }

  /**
   * 重新连接WebSocket
   * 使用指数退避策略增加重连间隔，但确保能持续重连
   */
  private reconnect(): void {
    // 重置重连尝试次数，确保能持续重连
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logManager.warn(`已达到最大重连尝试次数(${this.maxReconnectAttempts})，重置计数器并继续尝试`);
      this.reconnectAttempts = 0;
    }

    this.reconnectAttempts++;

    // 使用指数退避策略计算下一次重连间隔
    // 基本间隔 * (1.5 ^ 重试次数)，最大30秒
    const backoffFactor = Math.pow(1.5, this.reconnectAttempts - 1);
    const nextInterval = Math.min(this.reconnectInterval * backoffFactor, 30000);

    logManager.info(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})，等待 ${Math.round(nextInterval / 1000)} 秒...`);

    setTimeout(() => {
      try {
        // 从 AsyncStorage 获取认证令牌
        AsyncStorage.getItem('gold_auth_token').then(token => {
          if (token) {
            // 使用令牌重新连接
            this.connectWithToken(token, this.userId);
          } else {
            logManager.warn('未找到认证令牌，尝试从Redux获取');

            // 尝试从Redux获取令牌
            const state = store.getState();
            const reduxToken = state.auth?.token;

            if (reduxToken) {
              logManager.info('从Redux获取到令牌，尝试重新连接');
              this.connectWithToken(reduxToken, this.userId);
            } else {
              // 如果没有令牌，尝试使用游客模式连接
              logManager.info('尝试使用游客模式连接');
              this.connectWithToken('guest', 'guest');
            }
          }
        }).catch(error => {
          logManager.error('获取认证令牌失败:', error);
        });

      } catch (error) {
        logManager.error('重新连接失败:', error);

        // 即使出错，也继续尝试重连
        setTimeout(() => {
          this.reconnect();
        }, 5000); // 5秒后再次尝试
      }
    }, nextInterval);
  }

  /**
   * 发送消息
   * @param message 消息对象
   */
  public sendMessage(message: any): void {
    if (!this.socket || this.status !== WebSocketStatus.CONNECTED) {
      logManager.error('WebSocket未连接，无法发送消息');
      return;
    }

    try {
      // 添加时间戳
      const messageWithTimestamp = {
        ...message,
        timestamp: Date.now()
      };

      // 使用JSON格式发送消息
      this.socket.send(JSON.stringify(messageWithTimestamp));

      // 移除发送消息日志，只保留错误日志
    } catch (error) {
      logManager.error('发送消息失败:', error);
    }
  }

  /**
   * 处理解析后的消息对象
   * @param message 解析后的消息对象
   */
  private processMessage(message: any): void {
    try {
      // 检查消息是否有效
      if (!message || typeof message !== 'object') {
        logManager.debug('消息无效，忽略');
        return;
      }

      // 提取消息类型和数据
      let { type, data } = message;

      // 如果没有type字段，尝试从消息中推断
      if (!type) {
        type = normalizeMessageType(null, message);
        logManager.info(`推断消息类型: ${type}`);
      }

      // 检查是否包含期货价格数据，直接作为完整市场数据处理
      if (message.future_bid !== undefined || message.future_ask !== undefined) {
        try {
          logManager.info('检测到包含期货价格数据的消息，作为完整市场数据处理');
          this.handleMarketData(message);
          return;
        } catch (error) {
          logManager.error('处理包含期货价格数据的消息失败:', error);
          // 记录详细错误信息，帮助调试
          if (error instanceof Error) {
            logManager.error('错误详情:', error.message, error.stack);
            console.error('处理包含期货价格数据的消息失败:', {
              error: error,
              message: error.message,
              stack: error.stack,
              originalMessage: typeof message === 'object' ? JSON.stringify(message).substring(0, 200) + '...' : message
            });
          }
          // 继续执行，尝试其他处理方式
        }
      }

      // 如果没有data字段，使用整个消息作为数据
      if (!data && typeof message === 'object') {
        // 排除type字段
        const { type: _, ...messageData } = message;
        data = messageData;
      }

      // 处理不同类型的消息
      switch (type) {
        case 'market_data':
          try {
            this.handleMarketData(data);
          } catch (error) {
            logManager.error('处理market_data类型消息失败:', error);
            // 记录详细错误信息，帮助调试
            if (error instanceof Error) {
              logManager.error('错误详情:', error.message, error.stack);
            }
          }
          break;

        case 'market_data_incremental': // 将增量更新转发到完整市场数据处理
          try {
            // 移除WARN级别日志
            this.handleMarketData(data);
          } catch (error) {
            logManager.error('处理market_data_incremental类型消息失败:', error);
            // 记录详细错误信息，帮助调试
            if (error instanceof Error) {
              logManager.error('错误详情:', error.message, error.stack);
            }
          }
          break;

        case 'future_data':
          try {
            this.handleFutureData(data);
          } catch (error) {
            logManager.error('处理future_data类型消息失败:', error);
            if (error instanceof Error) {
              logManager.error('错误详情:', error.message, error.stack);
            }
          }
          break;

        case 'spot_data':
          try {
            this.handleSpotData(data);
          } catch (error) {
            logManager.error('处理spot_data类型消息失败:', error);
            if (error instanceof Error) {
              logManager.error('错误详情:', error.message, error.stack);
            }
          }
          break;

        case 'basis_trigger':
          try {
            this.handleBasisTrigger(data);
          } catch (error) {
            logManager.error('处理basis_trigger类型消息失败:', error);
            // 记录详细错误信息，帮助调试
            if (error instanceof Error) {
              logManager.error('错误详情:', error.message, error.stack);
            }
          }
          break;

        case 'position_update':
          try {
            this.handlePositionUpdate(data);
          } catch (error) {
            logManager.error('处理position_update类型消息失败:', error);
            // 记录详细错误信息，帮助调试
            if (error instanceof Error) {
              logManager.error('错误详情:', error.message, error.stack);
            }
          }
          break;

        case 'system_notification':
          try {
            this.handleSystemNotificationInternal(data);
          } catch (error) {
            logManager.error('处理system_notification类型消息失败:', error);
            // 记录详细错误信息，帮助调试
            if (error instanceof Error) {
              logManager.error('错误详情:', error.message, error.stack);
            }
          }
          break;

        case 'ping':
          // 心跳请求，发送响应
          try {
            this.sendMessage({ type: 'pong', timestamp: Date.now() });
            this.lastPingTime = Date.now();
          } catch (error) {
            logManager.error('处理ping消息失败:', error);
          }
          break;

        case 'pong':
          // 心跳响应，更新最后一次响应时间
          this.lastPongTime = Date.now();
          break;

        case 'error':
          // 处理错误消息
          try {
            const errorMessage = data?.message || '服务器错误';
            logManager.error('收到WebSocket错误消息:', errorMessage);

            // 显示错误通知
            notificationManager.error('WebSocket错误', errorMessage);
          } catch (error) {
            logManager.error('处理error类型消息失败:', error);
            // 记录详细错误信息，帮助调试
            if (error instanceof Error) {
              logManager.error('错误详情:', error.message, error.stack);
            }
          }
          break;

        default:
          // 尝试使用更智能的消息类型推断
          const inferredType = normalizeMessageType(type, message);
          if (inferredType !== type && inferredType !== 'unknown') {
            logManager.info(`重新推断消息类型: ${type} -> ${inferredType}`);
            // 递归调用自身，使用推断出的类型
            message.type = inferredType;
            this.processMessage(message);
          } else {
            logManager.warn(`未知的消息类型: ${type}`);
            // 记录消息结构，帮助调试
            if (message && typeof message === 'object') {
              const keys = Object.keys(message);
              logManager.debug(`消息结构: 键=${keys.join(', ')}`);

              // 如果消息包含data字段，尝试处理为市场数据
              if (message.data && typeof message.data === 'object') {
                try {
                  logManager.info('尝试将未知消息作为市场数据处理');
                  this.handleMarketDataIncremental(message);
                } catch (fallbackError) {
                  logManager.debug('尝试作为市场数据处理失败:', fallbackError);
                }
              }
            }
          }
          break;
      }
    } catch (error) {
      logManager.error('处理WebSocket消息失败:', error);
      // 记录详细错误信息，帮助调试
      if (error instanceof Error) {
        logManager.error('错误详情:', error.message, error.stack);
        console.error('处理WebSocket消息失败:', {
          error: error,
          message: error.message,
          stack: error.stack,
          originalMessage: typeof message === 'object' ? JSON.stringify(message).substring(0, 200) + '...' : message
        });
      }
    }
  }

  /**
   * 处理期货数据
   * @param data 期货数据
   */
  private handleFutureData(data: any): void {
    try {
      // 验证数据完整性
      if (!data) {
        logManager.warn('收到空的期货数据，忽略');
        return;
      }

      logManager.info(`🔥 收到独立期货数据推送: future_bid=${data.future_bid}, future_ask=${data.future_ask}, source=${data.source}`);

      // 创建期货数据更新对象
      const futureUpdate: any = {
        timestamp: data.timestamp || new Date().toISOString(),
        data_quality: data.data_quality || 'realtime',
        data_source: data.data_source || 'future_service',
        market_status: data.market_status || 'open',
        future_source: data.source || '天勤SDK'
      };

      // 直接更新期货价格，不进行数值验证
      if (data.future_bid !== undefined) {
        futureUpdate.future_bid = data.future_bid;
        logManager.info(`✅ 更新期货买入价: ${data.future_bid}`);
      }

      if (data.future_ask !== undefined) {
        futureUpdate.future_ask = data.future_ask;
        logManager.info(`✅ 更新期货卖出价: ${data.future_ask}`);
      }

      // 合并到缓存数据并更新Redux
      if (Object.keys(futureUpdate).length > 4) { // 除了基础字段外还有价格数据
        this.cachedMarketData = {
          ...this.cachedMarketData,
          ...futureUpdate
        };

        // 重新计算基差
        this.recalculateBasis();

        // 更新Redux
        webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
        logManager.info(`🔄 期货数据已更新到Redux`);
      }

    } catch (error) {
      logManager.error('处理期货数据失败:', error);
    }
  }

  /**
   * 处理现货数据
   * @param data 现货数据
   */
  private handleSpotData(data: any): void {
    try {
      // 验证数据完整性
      if (!data) {
        logManager.warn('收到空的现货数据，忽略');
        return;
      }

      logManager.info(`💰 收到独立现货数据推送: spot_bid=${data.spot_bid}, spot_ask=${data.spot_ask}, source=${data.source}`);

      // 创建现货数据更新对象
      const spotUpdate: any = {
        timestamp: data.timestamp || new Date().toISOString(),
        data_source: data.data_source || 'spot_service',
        spot_source: data.source || '现货爬虫'
      };

      // 直接更新现货价格，不进行数值验证
      if (data.spot_bid !== undefined) {
        spotUpdate.spot_bid = data.spot_bid;
        logManager.info(`✅ 更新现货买入价: ${data.spot_bid}`);
      }

      if (data.spot_ask !== undefined) {
        spotUpdate.spot_ask = data.spot_ask;
        logManager.info(`✅ 更新现货卖出价: ${data.spot_ask}`);
      }

      // 合并到缓存数据并更新Redux
      if (Object.keys(spotUpdate).length > 2) { // 除了基础字段外还有价格数据
        this.cachedMarketData = {
          ...this.cachedMarketData,
          ...spotUpdate
        };

        // 重新计算基差
        this.recalculateBasis();

        // 更新Redux
        webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
        logManager.info(`🔄 现货数据已更新到Redux`);
      }

    } catch (error) {
      logManager.error('处理现货数据失败:', error);
    }
  }

  /**
   * 重新计算基差
   */
  private recalculateBasis(): void {
    try {
      if (this.cachedMarketData) {
        const { future_bid, future_ask, spot_bid, spot_ask } = this.cachedMarketData;

        // 计算正向基差 (现货买入价 - 期货卖出价)
        if (spot_bid !== undefined && future_ask !== undefined && spot_bid > 0 && future_ask > 0) {
          this.cachedMarketData.forward_basis = parseFloat((spot_bid - future_ask).toFixed(2));
        }

        // 计算反向基差 (现货卖出价 - 期货买入价)
        if (spot_ask !== undefined && future_bid !== undefined && spot_ask > 0 && future_bid > 0) {
          this.cachedMarketData.reverse_basis = parseFloat((spot_ask - future_bid).toFixed(2));
        }
      }
    } catch (error) {
      logManager.error('重新计算基差失败:', error);
    }
  }



  /**
   * 处理市场数据
   * @param data 市场数据
   */
  private handleMarketData(data: any): void {
    try {
      // 验证数据完整性
      if (!data) {
        logManager.warn('收到空的市场数据，忽略');
        return;
      }

      // 检查期货价格字段是否存在并有效
      const hasFutureData = (data.future_bid !== undefined && data.future_bid > 0) ||
                           (data.future_ask !== undefined && data.future_ask > 0);

      if (hasFutureData) {
        logManager.info(`收到期货价格更新: future_bid=${data.future_bid}, future_ask=${data.future_ask}`);
      }

      // 只对缺失的现货字段进行补充，期货字段保持原样
      const spotFields = ['spot_bid', 'spot_ask'];
      const missingSpotFields = spotFields.filter(field => data[field] === undefined);

      if (missingSpotFields.length > 0) {
        logManager.warn(`现货数据缺少字段: ${missingSpotFields.join(', ')}`);

        // 使用缓存数据补充缺失的现货字段
        missingSpotFields.forEach(field => {
          if (this.cachedMarketData && this.cachedMarketData[field] !== undefined) {
            data[field] = this.cachedMarketData[field];
            logManager.info(`使用缓存数据补充现货字段 ${field}: ${data[field]}`);
          } else {
            // 使用默认值
            data[field] = 0;
            logManager.info(`使用默认值补充现货字段 ${field}: 0`);
          }
        });
      }

      // 添加时间戳
      if (!data.timestamp) {
        data.timestamp = new Date().toISOString();
      }

      // 添加数据质量标记
      if (!data.data_quality) {
        data.data_quality = 'realtime';
      }

      // 添加数据来源标记
      if (!data.data_source) {
        data.data_source = 'websocket';
      }

      // 确保市场状态正确
      // 如果有期货数据，则强制设置为开盘状态
      if ((data.future_bid > 0 || data.future_ask > 0) &&
          (data.market_status === 'closed' || !data.market_status)) {
        data.market_status = 'open';
        data.market_message = '交易中';
      }

      // 移除市场数据接收日志，只保留错误日志

      // 优先更新期货价格数据，确保期货价格能正确更新
      if (!this.cachedMarketData) {
        this.cachedMarketData = { ...data };
      } else {
        // 先保留现有数据
        const updatedData = { ...this.cachedMarketData };

        // 直接更新期货价格字段，不进行数值验证
        if (data.future_bid !== undefined) {
          updatedData.future_bid = data.future_bid;
          logManager.info(`更新期货买入价: ${data.future_bid}`);
        }
        if (data.future_ask !== undefined) {
          updatedData.future_ask = data.future_ask;
          logManager.info(`更新期货卖出价: ${data.future_ask}`);
        }

        // 更新其他字段
        Object.keys(data).forEach(key => {
          if (key !== 'future_bid' && key !== 'future_ask') {
            updatedData[key] = data[key];
          }
        });

        this.cachedMarketData = updatedData;
      }

      // 确保基差数据存在 - 修正基差计算公式
      if (data.future_bid !== undefined && data.spot_ask !== undefined && data.reverse_basis === undefined) {
        // 反向基差 = 现货卖出价 - 期货买入价 (spot_ask - future_bid)
        this.cachedMarketData.reverse_basis = parseFloat((data.spot_ask - data.future_bid).toFixed(2));
        // 移除INFO级别日志
      }

      if (data.future_ask !== undefined && data.spot_bid !== undefined && data.forward_basis === undefined) {
        // 正向基差 = 现货买入价 - 期货卖出价 (spot_bid - future_ask)
        this.cachedMarketData.forward_basis = parseFloat((data.spot_bid - data.future_ask).toFixed(2));
        // 移除INFO级别日志
      }

      // 更新Redux
      webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));

      // 记录最终更新的市场数据
      logManager.info(`🔄 Redux状态已更新: ` +
        `spot_bid=${this.cachedMarketData.spot_bid}, spot_ask=${this.cachedMarketData.spot_ask}, ` +
        `future_bid=${this.cachedMarketData.future_bid}, future_ask=${this.cachedMarketData.future_ask}, ` +
        `forward_basis=${this.cachedMarketData.forward_basis}, reverse_basis=${this.cachedMarketData.reverse_basis}`);
    } catch (error) {
      logManager.error('处理市场数据失败:', error);
    }
  }

  /**
   * 处理增量市场数据 - 已弃用，所有数据通过完整市场数据推送
   * 为了兼容性保留此方法，但将所有增量数据转发到handleMarketData处理
   * @param data 增量市场数据
   */
  private handleMarketDataIncremental(data: any): void {
    try {
      // 记录弃用警告
      logManager.warn('收到增量市场数据，但此功能已弃用。将转发到完整市场数据处理函数');

      // 验证数据完整性
      if (!data) {
        logManager.warn('收到空的增量市场数据，忽略');
        return;
      }

      // 检查数据是否为对象
      if (typeof data !== 'object') {
        logManager.warn(`收到的增量市场数据不是对象类型: ${typeof data}`);
        // 尝试将字符串转换为对象
        if (typeof data === 'string') {
          try {
            data = JSON.parse(data);
          } catch (e) {
            logManager.error('无法将字符串解析为JSON:', e);
            return;
          }
        } else {
          return;
        }
      }

      // 处理嵌套数据结构 - 如果数据在data字段中
      if (data.data && typeof data.data === 'object') {
        data = data.data;
      }

      // 移除INFO级别日志

      // 将增量数据与缓存数据合并，创建完整市场数据
      const completeData = {
        ...this.cachedMarketData,  // 使用现有缓存数据作为基础
        ...data,                   // 添加增量数据
        timestamp: data.timestamp || new Date().toISOString()
      };

      // 转发到handleMarketData处理
      this.handleMarketData(completeData);

    } catch (error) {
      logManager.error('处理增量市场数据失败:', error);
      // 记录详细错误信息，帮助调试
      if (error instanceof Error) {
        logManager.error('错误详情:', error.message, error.stack);
      }
    }
  }

  /**
   * 处理基差触发数据
   * @param data 基差触发数据
   */
  private handleBasisTrigger(data: any): void {
    try {
      // 验证数据完整性
      if (!data) {
        logManager.warn('收到空的基差触发数据，忽略');
        return;
      }

      // 规范化数据
      const normalizedData = {
        forward_high: parseFloat(data.forward_high || 0),
        forward_low: parseFloat(data.forward_low || 0),
        reverse_high: parseFloat(data.reverse_high || 0),
        reverse_low: parseFloat(data.reverse_low || 0),
        timestamp: data.timestamp || new Date().toISOString()
      };

      // 验证数据有效性
      const isValid = !isNaN(normalizedData.forward_high) &&
                      !isNaN(normalizedData.forward_low) &&
                      !isNaN(normalizedData.reverse_high) &&
                      !isNaN(normalizedData.reverse_low);

      if (!isValid) {
        logManager.warn('基差极值数据异常，不更新状态:', normalizedData);
        return;
      }

      // 完全禁用基差极值处理日志以减少冗余
      // logManager.debug('处理后的基差极值数据:', normalizedData);

      // 安全地更新Redux状态
      try {
        if (webSocketServiceMiddleware && typeof webSocketServiceMiddleware.dispatch === 'function') {
          webSocketServiceMiddleware.dispatch(updateBasisHighLow(normalizedData));
          // 禁用基差极值状态更新日志
          // logManager.info('成功更新Redux中的基差极值状态');
        } else {
          logManager.warn('webSocketServiceMiddleware未初始化或dispatch不可用');
        }
      } catch (dispatchError) {
        logManager.error('更新Redux状态失败:', dispatchError);
      }

      // 更新缓存的市场数据
      this.cachedMarketData = {
        ...this.cachedMarketData,
        forward_high: normalizedData.forward_high,
        forward_low: normalizedData.forward_low,
        reverse_high: normalizedData.reverse_high,
        reverse_low: normalizedData.reverse_low
      };

      // 安全地更新Redux
      try {
        if (webSocketServiceMiddleware && typeof webSocketServiceMiddleware.dispatch === 'function') {
          webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
          // 禁用基差极值数据更新日志
          // logManager.info('成功更新Redux中的基差极值数据');
        } else {
          logManager.warn('webSocketServiceMiddleware未初始化或dispatch不可用');
        }
      } catch (dispatchError) {
        logManager.error('更新Redux状态失败:', dispatchError);
      }
    } catch (error) {
      logManager.error('处理基差触发数据失败:', error);
    }
  }

  /**
   * 处理系统通知
   * @param data 系统通知数据
   */
  private handleSystemNotificationInternal(data: any): void {
    try {
      // 验证数据完整性
      if (!data) {
        logManager.warn('收到空的系统通知，忽略');
        return;
      }

      // 提取消息内容
      const message = data.message || '系统通知';
      const title = data.title || '系统消息';
      const type = data.type || 'info';

      // 通知所有监听器
      this.systemNotificationListeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          logManager.error('系统通知监听器执行失败:', error);
        }
      });

      // 显示通知
      switch (type) {
        case 'success':
          notificationManager.success(title, message);
          break;
        case 'warning':
          notificationManager.warning(title, message);
          break;
        case 'error':
          notificationManager.error(title, message);
          break;
        default:
          notificationManager.info(title, message);
          break;
      }

      // 禁用系统通知日志
      // logManager.info(`系统通知: ${title} - ${message}`);
    } catch (error) {
      logManager.error('处理系统通知失败:', error);
    }
  }

  /**
   * 处理持仓更新
   * @param data 持仓数据
   */
  private handlePositionUpdate(data: any): void {
    try {
      // 验证数据完整性
      if (!data) {
        logManager.warn('收到空的持仓更新，忽略');
        return;
      }

      // 更新Redux
      webSocketServiceMiddleware.dispatch(fetchPositionsSuccess({
        positions: data.positions || [],
        totalPnl: data.total_pnl || 0
      }));

      // 禁用持仓更新日志
      // logManager.info('成功处理持仓更新，已更新Redux');
    } catch (error) {
      logManager.error('处理持仓更新失败:', error);
    }
  }

  // 使用normalizeMessageType函数替代inferMessageType方法

  /**
   * 处理接收到的消息 - 已弃用，不再使用
   * @param data 消息数据，可能是字符串或二进制数据
   */
  private handleMessage_deprecated(data: string): void {
    // 设置处理开始时间，用于性能监控
    const startTime = performance.now();

    // 设置一个标志，用于控制是否记录详细日志
    const shouldLogDetails = __DEV__ && this.logLevel === 'debug';

    try {
      // 记录最后一次消息接收时间
      this.lastMessageTime = Date.now();

      let message: any;

      // 判断数据类型
      if (typeof data === 'string') {
        // 检查是否是空字符串或无效数据
        if (!data || data.trim() === '') {
          logManager.warn('收到空消息，忽略');
          return;
        }

        // 检查是否是特殊格式的对象字符串（如"[object Object]"）
        if (data === '[object Object]' || data.includes('[object Object]')) {
          // 静默忽略这种特殊格式的字符串，不记录日志
          return;
        }

        // 检查是否是对象字符串化后的结果
        if (data.startsWith('object') || data.startsWith('[object')) {
          // 静默忽略这种特殊格式的字符串，不记录日志
          return;
        }

        // 特殊处理以 "o" 开头的消息，这可能是导致 "Unexpected token: o" 错误的原因
        if (data.startsWith('o') && !data.startsWith('object')) {
          try {
            // 尝试移除开头的 "o" 字符
            const cleanedData = data.substring(1).trim();

            // 检查清理后的数据是否是有效的JSON
            if (cleanedData.startsWith('{') && cleanedData.endsWith('}')) {
              try {
                // 尝试解析JSON
                const jsonData = JSON.parse(cleanedData);
                message = jsonData;
                // 禁用消息处理日志
                // logManager.info('成功处理以"o"开头的消息');

                // 如果没有类型字段，尝试推断
                if (!message.type && typeof message === 'object') {
                  // 检查是否包含基差极值数据
                  if (message.forward_high !== undefined &&
                      message.forward_low !== undefined &&
                      message.reverse_high !== undefined &&
                      message.reverse_low !== undefined) {
                    message.type = 'basis_trigger';
                    // 禁用类型推断日志
                    // logManager.info('从以"o"开头的消息中推断类型: basis_trigger');
                  }
                  // 检查是否包含当前基差数据
                  else if (message.forward_basis !== undefined &&
                           message.reverse_basis !== undefined) {
                    message.type = 'market_data';
                    // 禁用类型推断日志
                    // logManager.info('从以"o"开头的消息中推断类型: market_data');
                  }
                  // 检查是否包含市场数据
                  else if (message.spot_price !== undefined ||
                           message.futures_price !== undefined ||
                           message.spot_bid !== undefined ||
                           message.spot_ask !== undefined) {
                    message.type = 'market_data';
                    // 禁用类型推断日志
                    // logManager.info('从以"o"开头的消息中推断类型: market_data');
                  }
                  // 默认为完整市场数据
                  else {
                    message.type = 'market_data';
                    // 禁用类型推断日志
                    // logManager.info('从以"o"开头的消息中设置默认类型: market_data');
                  }
                }
              } catch (jsonError) {
                // JSON解析失败，尝试提取基差数据
                if (this.extractAndProcessBasisData(cleanedData)) {
                  // 禁用基差数据处理日志
                  // logManager.info('从以"o"开头的消息中提取并处理了基差数据');
                  return;
                }

                // 如果提取失败，忽略此消息
                if (shouldLogDetails) {
                  logManager.debug('解析以"o"开头的消息失败:', jsonError);
                }
                return;
              }
            } else {
              // 不是有效的JSON格式，忽略此消息
              return;
            }
          } catch (oError) {
            // 处理失败，忽略此消息
            return;
          }
        }

        // 检查是否包含市场数据相关字符串
        if (data.toLowerCase().includes('market') &&
            data.toLowerCase().includes('data')) {
          // 构造一个有效的市场数据消息
          message = {
            type: 'market_data',
            data: {}
          };
          // 禁用市场数据检测日志
          // logManager.info('检测到市场数据消息字符串，转换为有效消息对象');

          // 尝试从字符串中提取数据
          try {
            // 使用正则表达式提取可能的JSON部分
            const jsonMatch = /{[^}]+}/g.exec(data);
            if (jsonMatch && jsonMatch[0]) {
              try {
                const jsonData = JSON.parse(jsonMatch[0]);
                message.data = jsonData;
                // 禁用JSON提取日志
                // logManager.info('成功从市场数据字符串中提取JSON数据');
              } catch (e) {
                // 忽略JSON解析错误
              }
            }
          } catch (e) {
            // 忽略提取错误
          }

          // 直接处理此消息，不需要进一步解析
          const msgData = message.data;

          // 合并数据到缓存
          this.cachedMarketData = {
            ...this.cachedMarketData,
            ...msgData,
            timestamp: new Date().toISOString()
          };

          // 使用合并后的完整数据更新Redux
          webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
          // 禁用市场数据处理日志
          // logManager.info('成功处理市场数据字符串消息');

          return;
        }

        try {
          // 文本数据，使用JSON解析
          const jsonData = JSON.parse(data);

          // 普通JSON数据
          message = jsonData;

          // 禁用JSON解析成功日志
          // if (message && message.type) {
          //   logManager.info(`成功解析JSON消息，类型: ${message.type}`);
          // } else {
          //   logManager.info('成功解析JSON消息，但没有类型字段');
          // }
        } catch (jsonError) {
          // 尝试使用 extractAndProcessBasisData 方法提取基差数据
          if (this.extractAndProcessBasisData(data)) {
            // 禁用基差数据提取日志
            // logManager.info('成功从JSON解析失败的消息中提取并处理了基差数据');
            return;
          }

          // 尝试使用正则表达式提取JSON部分
          try {
            const jsonRegex = /{.*}/s;
            const match = jsonRegex.exec(data);
            if (match) {
              const jsonPart = match[0];
              try {
                // 尝试解析提取的JSON部分
                message = JSON.parse(jsonPart);
                // 禁用JSON提取日志
                // logManager.info('成功从非标准格式消息中提取并解析JSON部分');

                // 如果没有类型字段，尝试推断
                if (!message.type && typeof message === 'object') {
                  // 检查是否包含基差极值数据
                  if (message.forward_high !== undefined &&
                      message.forward_low !== undefined &&
                      message.reverse_high !== undefined &&
                      message.reverse_low !== undefined) {
                    message.type = 'basis_trigger';
                    // 禁用类型推断日志
                    // logManager.info('从提取的JSON部分中推断类型: basis_trigger');
                  }
                  // 检查是否包含当前基差数据
                  else if (message.forward_basis !== undefined &&
                           message.reverse_basis !== undefined) {
                    message.type = 'market_data';
                    // 禁用类型推断日志
                    // logManager.info('从提取的JSON部分中推断类型: market_data');
                  }
                  // 检查是否包含市场数据
                  else if (message.spot_price !== undefined ||
                           message.futures_price !== undefined ||
                           message.spot_bid !== undefined ||
                           message.spot_ask !== undefined) {
                    message.type = 'market_data';
                    // 禁用类型推断日志
                    // logManager.info('从提取的JSON部分中推断类型: market_data');
                  }
                  // 默认为增量市场数据
                  else {
                    message.type = 'market_data_incremental';
                    // 禁用类型推断日志
                    // logManager.info('从提取的JSON部分中设置默认类型: market_data_incremental');
                  }
                }
              } catch (parseError) {
                // 如果解析失败，继续尝试提取基差数据
                logManager.debug('解析从非标准格式消息中提取的JSON部分失败');
              }
            }
          } catch (extractError) {
            // 如果提取失败，继续尝试提取基差数据
            logManager.debug('从非标准格式消息中提取JSON部分失败');
          }

          // JSON解析错误，可能是对象格式的字符串

          // 检查是否包含基差极值数据
          if (data.includes('forward') && data.includes('high') &&
              data.includes('low') && data.includes('reverse')) {
            try {
              // 尝试提取基差极值数据
              const basisData: any = {};

              // 使用正则表达式提取数值
              const forwardHighMatch = /forward\s*high"?\s*:\s*(-?\d+\.?\d*)/i.exec(data);
              const forwardLowMatch = /forward\s*low"?\s*:\s*(-?\d+\.?\d*)/i.exec(data);
              const reverseHighMatch = /reverse\s*high"?\s*:\s*(-?\d+\.?\d*)/i.exec(data);
              const reverseLowMatch = /reverse\s*low"?\s*:\s*(-?\d+\.?\d*)/i.exec(data);

              if (forwardHighMatch) basisData.forward_high = parseFloat(forwardHighMatch[1]);
              if (forwardLowMatch) basisData.forward_low = parseFloat(forwardLowMatch[1]);
              if (reverseHighMatch) basisData.reverse_high = parseFloat(reverseHighMatch[1]);
              if (reverseLowMatch) basisData.reverse_low = parseFloat(reverseLowMatch[1]);

              // 添加时间戳
              basisData.timestamp = new Date().toISOString();

              // 检查是否提取到了所有必要的数据
              if (basisData.forward_high !== undefined &&
                  basisData.forward_low !== undefined &&
                  basisData.reverse_high !== undefined &&
                  basisData.reverse_low !== undefined) {

                // 构造一个有效的消息对象
                message = {
                  type: 'basis_trigger',
                  data: basisData
                };

                // 禁用基差极值提取日志
                // logManager.info('成功从字符串中提取基差极值数据');

                // 更新市场数据
                this.cachedMarketData = {
                  ...this.cachedMarketData,
                  ...basisData
                };

                // 使用合并后的完整数据更新Redux
                webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
                logManager.info('成功更新基差极值数据');

                // 已处理，直接返回
                return;
              }
            } catch (extractError) {
              logManager.debug('从字符串中提取基差极值数据失败:', extractError);
            }
          }

          // 检查是否包含当前基差值
          if (data.includes('forward') && data.includes('reverse') &&
              !data.includes('high') && !data.includes('low')) {
            try {
              // 尝试提取当前基差值
              const basisData: any = {};

              // 使用正则表达式提取数值
              const forwardMatch = /forward"?\s*:\s*(-?\d+\.?\d*)/i.exec(data);
              const reverseMatch = /reverse"?\s*:\s*(-?\d+\.?\d*)/i.exec(data);

              if (forwardMatch) basisData.forward_basis = parseFloat(forwardMatch[1]);
              if (reverseMatch) basisData.reverse_basis = parseFloat(reverseMatch[1]);

              // 添加时间戳
              basisData.timestamp = new Date().toISOString();

              // 检查是否提取到了所有必要的数据
              if (basisData.forward_basis !== undefined &&
                  basisData.reverse_basis !== undefined) {

                // 构造一个有效的消息对象
                message = {
                  type: 'market_data',
                  data: basisData
                };

                logManager.info('成功从字符串中提取当前基差值');

                // 更新市场数据
                this.cachedMarketData = {
                  ...this.cachedMarketData,
                  ...basisData
                };

                // 使用合并后的完整数据更新Redux
                webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
                logManager.info('成功更新当前基差值');

                // 已处理，直接返回
                return;
              }
            } catch (extractError) {
              logManager.debug('从字符串中提取当前基差值失败:', extractError);
            }
          }

          // 尝试检查是否是JavaScript对象字符串
          if (data.startsWith('{') && data.endsWith('}')) {
            try {
              // 尝试修复常见的JSON格式问题
              // 替换单引号为双引号
              let fixedData = data.replace(/'/g, '"')
                // 修复没有引号的键名
                .replace(/([{,])\s*([a-zA-Z0-9_]+)\s*:/g, '$1"$2":')
                // 修复末尾多余的逗号
                .replace(/,\s*}/g, '}')
                .replace(/,\s*]/g, ']')
                // 修复错误的布尔值和null
                .replace(/:\s*true\s*([,}])/g, ':true$1')
                .replace(/:\s*false\s*([,}])/g, ':false$1')
                .replace(/:\s*null\s*([,}])/g, ':null$1')
                // 修复数字格式
                .replace(/:(\s*-?\d+\.?\d*)\s*([,}])/g, ':$1$2');

              try {
                // 尝试解析修复后的JSON
                message = JSON.parse(fixedData);
                logManager.info('修复JSON格式后成功解析消息');
              } catch (parseError) {
                // 如果仍然无法解析，尝试使用Function构造函数
                try {
                  const parseFn = new Function('return ' + fixedData);
                  message = parseFn();
                  logManager.info('使用替代方法成功解析消息');
                } catch (evalError) {
                  // 如果仍然失败，尝试更激进的修复
                  fixedData = fixedData
                    // 移除所有换行符和多余的空格
                    .replace(/\s+/g, ' ')
                    // 确保所有键名都有引号
                    .replace(/([{,])\s*([^"'\s]+)\s*:/g, '$1"$2":')
                    // 确保所有字符串值都有引号
                    .replace(/:\s*([^"'\d\{\}\[\]truefalsenull,\s][^,\}\]]*)\s*([,\}\]])/g, ':"$1"$2');

                  try {
                    message = JSON.parse(fixedData);
                    logManager.info('使用激进修复后成功解析JSON');
                  } catch (aggressiveError) {
                    logManager.debug('所有JSON修复方法都失败，忽略此消息');
                    // 静默失败，不记录错误
                    return;
                  }
                }
              }
            } catch (fixError) {
              // 静默失败，不记录错误
              return;
            }
          } else if (data.includes('market') && data.includes('data')) {
            // 特殊处理包含市场数据关键字的消息
            try {
              // 尝试构造一个有效的JSON对象
              message = {
                type: 'market_data_incremental',
                data: {
                  timestamp: new Date().toISOString(),
                  data_quality: 'simulated',
                  data_source: 'extracted'
                }
              };

              logManager.info('从包含市场数据关键字的消息中创建默认数据');

              // 更新市场数据
              const msgData = message.data || {};
              this.cachedMarketData = {
                ...this.cachedMarketData,
                ...msgData
              };

              // 使用合并后的完整数据更新Redux
              webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));

              // 已处理，直接返回
              return;
            } catch (e) {
              // 静默失败，不记录错误
              return;
            }
          } else {
            // 不是JSON也不是对象字符串，无法处理
            // 静默失败，不记录错误
            return;
          }
        }
      } else {
        // 未知数据类型
        logManager.warn('收到未知类型的数据，忽略');
        return;
      }

      // 检查消息是否有效
      if (!message || typeof message !== 'object') {
        logManager.debug('解析后的消息无效，忽略');
        return;
      }

      // 提取消息类型和数据
      let { type, data: messageData } = message;

      // 检查消息类型是否有效
      if (!type) {
        // 如果没有type字段，尝试从消息中提取类型
        if (message.market_data_incremental || message.marketDataIncremental || message.data?.is_incremental) {
          type = 'market_data_incremental';
          messageData = message.market_data_incremental || message.marketDataIncremental || message.data;
        } else if (message.market_data) {
          type = 'market_data';
          messageData = message.market_data;
        } else if (message.basis_data) {
          type = 'basis_trigger';
          messageData = message.basis_data;
        } else if (message.data && typeof message.data === 'object') {
          // 尝试从data字段中推断消息类型
          if (message.data.forward_basis !== undefined || message.data.reverse_basis !== undefined) {
            // 可能是市场数据
            type = 'market_data';
            messageData = message.data;
          } else if (message.data.is_incremental) {
            // 可能是增量市场数据
            type = 'market_data_incremental';
            messageData = message.data;
          }
        }
      }

      // 使用规范化函数处理消息类型
      const originalType = type;
      type = normalizeMessageType(type, message);

      // 如果类型发生了变化，记录日志
      if (originalType !== type && originalType) {
        logManager.info(`规范化消息类型: ${originalType} -> ${type}`);
      }

      // 如果消息数据为空但消息本身包含数据字段，尝试提取
      if ((!messageData || Object.keys(messageData).length === 0) &&
          message && typeof message === 'object') {
        // 检查是否包含基差极值数据
        if (message.forward_high !== undefined &&
            message.forward_low !== undefined &&
            message.reverse_high !== undefined &&
            message.reverse_low !== undefined) {
          messageData = {
            forward_high: message.forward_high,
            forward_low: message.forward_low,
            reverse_high: message.reverse_high,
            reverse_low: message.reverse_low,
            timestamp: message.timestamp || new Date().toISOString()
          };
          logManager.info('从消息中提取基差极值数据');
        }
        // 检查是否包含当前基差数据
        else if (message.forward_basis !== undefined &&
                 message.reverse_basis !== undefined) {
          messageData = {
            forward_basis: message.forward_basis,
            reverse_basis: message.reverse_basis,
            timestamp: message.timestamp || new Date().toISOString()
          };
          logManager.info('从消息中提取当前基差数据');
        }
      }

      // 如果类型是 market_data_incremental，确保它被正确处理
      // 但不要在这里直接返回，让它进入 switch 语句进行更完整的处理
      if (type === 'market_data_incremental') {
        // 确保 messageData 是一个有效的对象
        if (!messageData || typeof messageData !== 'object') {
          messageData = {};
        }

        // 添加时间戳
        if (!messageData.timestamp) {
          messageData.timestamp = new Date().toISOString();
        }

        // 添加数据质量标记
        if (!messageData.data_quality) {
          messageData.data_quality = 'realtime'; // 标记为实时数据
        }

        // 添加数据来源标记
        if (!messageData.data_source) {
          messageData.data_source = 'websocket'; // 标记数据来源
        }

        // 记录接收到的消息类型
        logManager.info(`接收到增量市场数据更新，类型: ${type}`);

        // 确保 messageData 是一个有效的对象
        if (!messageData || typeof messageData !== 'object') {
          messageData = {};
        }

        // 添加时间戳
        if (!messageData.timestamp) {
          messageData.timestamp = new Date().toISOString();
        }

        // 添加数据质量标记
        if (!messageData.data_quality) {
          messageData.data_quality = 'realtime'; // 标记为实时数据
        }

        // 添加数据来源标记
        if (!messageData.data_source) {
          messageData.data_source = 'websocket'; // 标记数据来源
        }

        // 合并增量数据到缓存
        this.cachedMarketData = {
          ...this.cachedMarketData,
          ...messageData
        };

        // 使用合并后的完整数据更新Redux
        webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
        // 移除INFO级别日志
      }

      // 检查消息类型是否在已知类型列表中
      const isKnownType = type && ALL_MESSAGE_TYPES.includes(type);

      // 如果是未知类型，但消息中包含基差数据，则尝试处理
      if ((!isKnownType || type === 'basis_data' || type === 'market_data_incremental') &&
          messageData && typeof messageData === 'object') {
        if (messageData.forward_high !== undefined &&
            messageData.forward_low !== undefined &&
            messageData.reverse_high !== undefined &&
            messageData.reverse_low !== undefined) {

          // 构造基差极值更新消息
          const basisData = {
            forward_high: messageData.forward_high,
            forward_low: messageData.forward_low,
            reverse_high: messageData.reverse_high,
            reverse_low: messageData.reverse_low,
            timestamp: messageData.timestamp || new Date().toISOString()
          };

          // 移除INFO级别日志

          // 处理基差极值更新
          const basisUpdateData = {
            forward_high: basisData.forward_high,
            forward_low: basisData.forward_low,
            reverse_high: basisData.reverse_high,
            reverse_low: basisData.reverse_low
          };

          // 更新市场数据
          this.cachedMarketData = {
            ...this.cachedMarketData,
            ...basisUpdateData
          };

          // 使用合并后的完整数据更新Redux
          webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
          // 移除DEBUG级别日志

          // 已处理，不需要进入switch
          return;
        }
      }

      switch (type) {
        case WebSocketMessageType.MARKET_DATA:
        case 'market_data': // 添加小写的类型处理，兼容后端发送的消息
          // 更新市场数据
          logManager.info(`处理市场数据消息，类型: ${type}`);
          this.handleMarketData(messageData);
          break;

        case WebSocketMessageType.MARKET_DATA_INCREMENTAL:
        case 'market_data_incremental': // 添加小写的类型处理，兼容后端发送的消息
        case 'marketDataIncremental': // 添加驼峰命名的类型处理
        case 'market-data-incremental': // 添加连字符分隔的类型处理
        case 'marketdata_incremental': // 添加无下划线的类型处理
          // 处理增量市场数据
          logManager.info(`处理增量市场数据消息，类型: ${type}, 数据: ${JSON.stringify(messageData)}`);
          this.handleMarketDataIncremental(messageData);
          break;

              // 移除二进制消息处理相关代码
        case 'market_data_binary':
          // 将二进制消息转换为普通市场数据处理
          logManager.info('收到market_data_binary类型消息，转为普通市场数据处理');
          this.handleMarketData(messageData);
          break;

        // 以下case已在前面处理过，这里是重复的代码，注释掉
        /*
        case WebSocketMessageType.MARKET_DATA_INCREMENTAL:
        case 'market_data_incremental': // 添加小写的类型处理，兼容后端发送的消息
        case 'market data incremental': // 添加空格分隔的类型处理，兼容后端发送的消息
        case 'marketDataIncremental': // 添加驼峰命名的类型处理，兼容后端发送的消息
        case 'marketdata_incremental': // 添加无下划线的类型处理，兼容后端发送的消息
        case 'market-data-incremental': // 添加连字符分隔的类型处理，兼容后端发送的消息
        case 'marketdataincremental': // 添加无分隔符的类型处理
        case 'market.data.incremental': // 添加点分隔的类型处理
        case 'market_data.incremental': // 添加混合分隔的类型处理
        case 'market.data_incremental': // 添加混合分隔的类型处理
        case 'basis_data': // 处理基差数据作为增量更新
        case 'basis_update': // 处理基差更新
        case 'incremental': // 简化的增量更新类型
        case 'update': // 更简化的更新类型
        case 'data': // 最简化的数据类型
        */
        case 'fallback_case':
          // 这是一个不会被匹配到的case，用于避免语法错误
          break;

        case WebSocketMessageType.POSITION_UPDATE:
          // 更新持仓数据
          webSocketServiceMiddleware.dispatch(fetchPositionsSuccess({
            positions: messageData.positions,
            totalPnl: messageData.total_pnl
          }));
          break;

        case WebSocketMessageType.TRADE_EXECUTION:
          // 处理交易执行通知
          this.handleTradeNotification(messageData);
          break;

        case WebSocketMessageType.TRADE_EXECUTION_RESULT:
        case 'trade_execution_result': // 添加小写的类型处理，兼容后端发送的消息
          // 处理交易执行结果
          this.handleTradeExecutionResult(messageData);
          break;

        case WebSocketMessageType.SYSTEM_NOTIFICATION:
          // 处理系统通知
          this.handleSystemNotificationInternal(messageData);
          break;

        case WebSocketMessageType.RISK_ALERT:
          // 处理风控警报
          this.handleRiskAlert(messageData);
          break;

        case WebSocketMessageType.BASIS_TRIGGER:
        case 'basis_trigger': // 添加小写的类型处理，兼容后端发送的消息
          // 处理基差触发通知
          try {
            // 检查消息数据是否有效
            if (messageData && typeof messageData === 'object') {
              // 处理基差触发通知
              this.handleBasisTrigger(messageData);

              // 检查是否包含基差极值数据
              if (messageData.forward_high !== undefined &&
                  messageData.forward_low !== undefined &&
                  messageData.reverse_high !== undefined &&
                  messageData.reverse_low !== undefined) {

                // 构造基差极值更新消息
                const basisData = {
                  forward_high: messageData.forward_high,
                  forward_low: messageData.forward_low,
                  reverse_high: messageData.reverse_high,
                  reverse_low: messageData.reverse_low,
                  timestamp: messageData.timestamp || new Date().toISOString()
                };

                // 完全禁用基差极值更新日志以减少冗余
                // logManager.debug('收到基差极值更新:', basisData);

                // 更新市场数据
                this.cachedMarketData = {
                  ...this.cachedMarketData,
                  forward_high: basisData.forward_high,
                  forward_low: basisData.forward_low,
                  reverse_high: basisData.reverse_high,
                  reverse_low: basisData.reverse_low
                };

                // 使用合并后的完整数据更新Redux
                webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
                logManager.debug('处理后的基差极值数据:', {
                  forward_high: basisData.forward_high,
                  forward_low: basisData.forward_low,
                  reverse_high: basisData.reverse_high,
                  reverse_low: basisData.reverse_low
                });
              }
            } else {
              logManager.debug('基差触发数据无效或为空');
            }
          } catch (basisTriggerError) {
            logManager.error('处理基差触发失败:', basisTriggerError);
          }
          break;

        case WebSocketMessageType.PONG:
        case 'pong': // 添加小写的类型处理，兼容后端发送的消息
          // 处理心跳响应
          const now = Date.now();
          const latency = now - this.lastPingTime;
          // 更新最后一次收到pong的时间
          this.lastPongTime = now;
          logManager.debug(`WebSocket延迟: ${latency}ms`);
          break;

        case WebSocketMessageType.HEARTBEAT:
          // 处理服务器发送的心跳消息
          // 心跳消息用于保持连接活跃，不需要特殊处理
          logManager.debug('收到服务器心跳消息');
          break;

        case WebSocketMessageType.PREFERENCES_UPDATED:
          // 禁用偏好设置更新日志
          // logManager.info('服务器确认用户偏好设置已更新:', messageData);
          break;

        case WebSocketMessageType.CONNECTION_RESET:
          // 禁用连接重置日志
          // logManager.info('WebSocket连接已重置');
          // 清空缓存的市场数据
          this.cachedMarketData = {};
          break;

        case WebSocketMessageType.ERROR:
        case 'error': // 添加小写的'error'类型处理，兼容后端发送的消息
        case 'error_message': // 添加更多可能的错误消息类型
        case 'error_notification':
        case 'server_error':
        case 'error-message':
        case 'error.message':
        case 'errormessage':
        case 'error-notification':
        case 'error.notification':
        case 'errornotification':
        case 'server-error':
        case 'server.error':
        case 'servererror':
          // 处理错误消息
          let errorMessage = '';

          // 尝试从不同的字段中提取错误消息
          if (messageData?.message) {
            errorMessage = messageData.message;
          } else if (messageData?.error) {
            errorMessage = messageData.error;
          } else if (messageData?.error_message) {
            errorMessage = messageData.error_message;
          } else if (typeof messageData === 'string') {
            errorMessage = messageData;
          } else if (typeof messageData === 'object' && messageData !== null) {
            // 尝试将对象转换为字符串
            try {
              errorMessage = JSON.stringify(messageData);
            } catch (e) {
              errorMessage = '未知错误对象';
            }
          } else {
            errorMessage = '未知错误';
          }

          // 禁用WebSocket错误日志
          // logManager.info('WebSocket错误:', errorMessage);

          // 只有在消息不为空时才显示通知，并且不是常见的无害错误
          if (errorMessage &&
              errorMessage !== '未知消息类型: ping' &&
              !errorMessage.includes('未知消息类型') &&
              !errorMessage.includes('Unexpected token: o') &&
              !errorMessage.includes('JSON Parse error') &&
              !errorMessage.includes('SyntaxError')) {
            notificationManager.error(
              'WebSocket错误',
              errorMessage
            );
          } else {
            // 对于常见的无害错误，只记录日志，不显示通知
            logManager.debug('WebSocket常见错误，不显示通知:', errorMessage);
          }
          break;

        default:
          try {
            // 不记录ping消息的未知类型警告，减少日志噪音
            if (type !== 'ping') {
              // 检查是否可能是增量市场数据 - 更宽松的条件
              const isMarketDataType = type && typeof type === 'string' && (
                type.toLowerCase().includes('incremental') ||
                type.toLowerCase().includes('market') ||
                type.toLowerCase().includes('data') ||
                type.toLowerCase().includes('basis') ||
                type.toLowerCase().includes('update') ||
                type.toLowerCase().includes('price') ||
                type.toLowerCase().includes('bid') ||
                type.toLowerCase().includes('ask') ||
                type.toLowerCase().includes('forward') ||
                type.toLowerCase().includes('reverse')
              );

              // 检查消息内容是否包含market_data_incremental相关关键字 - 更宽松的条件
              const messageString = JSON.stringify(message).toLowerCase();
              const hasMarketDataKeywords = (
                messageString.includes('market') ||
                messageString.includes('data') ||
                messageString.includes('basis') ||
                messageString.includes('forward') ||
                messageString.includes('reverse') ||
                messageString.includes('incremental') ||
                messageString.includes('update') ||
                messageString.includes('high') ||
                messageString.includes('low') ||
                messageString.includes('price') ||
                messageString.includes('bid') ||
                messageString.includes('ask')
              );

              // 检查消息是否包含基差数据字段
              const hasBasisFields = messageData && typeof messageData === 'object' && (
                messageData.forward_high !== undefined ||
                messageData.forward_low !== undefined ||
                messageData.reverse_high !== undefined ||
                messageData.reverse_low !== undefined ||
                messageData.forward_basis !== undefined ||
                messageData.reverse_basis !== undefined
              );

              if (isMarketDataType || hasMarketDataKeywords || hasBasisFields) {
                // 尝试将其视为增量市场数据处理
                type = 'market_data_incremental';

                // 禁用类型转换日志
                // logManager.info(`将未知消息类型 "${originalType}" 转换为 "market_data_incremental"`);

                // 重新处理为增量市场数据
                try {
                  // 检查消息数据是否有效
                  if (messageData && typeof messageData === 'object') {
                    // 合并增量数据到缓存
                    this.cachedMarketData = {
                      ...this.cachedMarketData,
                      ...messageData
                    };

                    // 使用合并后的完整数据更新Redux
                    webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
                    // 禁用增量数据处理日志
                    // logManager.info('成功处理转换后的增量市场数据');

                    // 已处理，跳过后续代码
                    break;
                  } else if (message && typeof message === 'object') {
                    // 尝试从message中提取数据
                    const possibleDataFields = ['data', 'market_data', 'marketData', 'market_data_incremental', 'marketDataIncremental'];
                    let extractedData = null;

                    for (const field of possibleDataFields) {
                      if (message[field] && typeof message[field] === 'object') {
                        extractedData = message[field];
                        // 禁用数据提取日志
                        // logManager.info(`从message.${field}中提取到数据`);
                        break;
                      }
                    }

                    if (extractedData) {
                      // 合并提取的数据到缓存
                      this.cachedMarketData = {
                        ...this.cachedMarketData,
                        ...extractedData
                      };

                      // 使用合并后的完整数据更新Redux
                      webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
                      // 禁用增量数据处理日志
                      // logManager.info('成功处理从message中提取的增量市场数据');

                      // 已处理，跳过后续代码
                      break;
                    } else {
                      // 使用默认值
                      const defaultData = {
                        timestamp: new Date().toISOString(),
                        data_quality: 'simulated',
                        data_source: 'default'
                      };

                      // 合并默认数据到缓存
                      this.cachedMarketData = {
                        ...this.cachedMarketData,
                        ...defaultData
                      };

                      // 使用合并后的完整数据更新Redux
                      webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
                      // 禁用默认值更新日志
                      // logManager.info('使用默认值更新增量市场数据');

                      // 已处理，跳过后续代码
                      break;
                    }
                  }
                } catch (conversionError) {
                  logManager.error('转换为增量市场数据处理失败:', conversionError);
                }
              }

              // 检查是否是 market_data_incremental 类型或相关类型
              if (type === 'market_data_incremental' ||
                  type === 'incremental' ||
                  type === 'update' ||
                  type === 'data' ||
                  (type && type.toLowerCase().includes('market')) ||
                  (type && type.toLowerCase().includes('data')) ||
                  (type && type.toLowerCase().includes('incremental'))) {
                // 尝试将其视为增量市场数据处理
                try {
                  // 确保 messageData 是一个有效的对象
                  if (!messageData || typeof messageData !== 'object') {
                    messageData = {};
                  }

                  // 添加时间戳
                  if (!messageData.timestamp) {
                    messageData.timestamp = new Date().toISOString();
                  }

                  // 添加数据质量标记
                  if (!messageData.data_quality) {
                    messageData.data_quality = 'realtime'; // 标记为实时数据
                  }

                  // 添加数据来源标记
                  if (!messageData.data_source) {
                    messageData.data_source = 'websocket'; // 标记数据来源
                  }

                  // 合并增量数据到缓存
                  this.cachedMarketData = {
                    ...this.cachedMarketData,
                    ...messageData
                  };

                  // 使用合并后的完整数据更新Redux
                  webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
                  // 禁用增量数据处理日志
                  // logManager.info(`成功处理消息类型: ${type} 作为增量市场数据`);
                } catch (error) {
                  logManager.error(`处理消息类型: ${type} 作为增量市场数据失败:`, error);
                }
              } else if (type === 'ping') {
                // 忽略ping消息
              } else if (type && ALL_MESSAGE_TYPES.includes(type)) {
                // 已知类型，但在switch中没有处理
                logManager.debug(`已知但未处理的消息类型: ${type}`);
              } else {
                // 对于未知消息类型，尝试作为增量市场数据处理
                if (type === 'market_data_incremental') {
                  try {
                    this.handleMarketDataIncremental(messageData || {});
                    // 禁用增量数据处理日志
                    // logManager.info('成功处理market_data_incremental类型消息');
                  } catch (error) {
                    // 禁用增量数据处理错误日志
                    // logManager.info('处理market_data_incremental类型消息失败:', error);
                  }
                } else {
                  // 禁用未处理消息类型日志
                  // logManager.info('收到未处理的消息类型:', type);
                }
              }

              // 尝试处理可能的数据更新
              if (messageData && typeof messageData === 'object') {
                // 检查是否包含市场数据相关字段
                const marketDataFields = [
                  'forward_basis', 'reverse_basis', 'spot_price', 'futures_price',
                  'forward_high', 'forward_low', 'reverse_high', 'reverse_low',
                  'basis_data', 'market_data', 'spot_bid', 'spot_ask', 'future_bid', 'future_ask',
                  'timestamp', 'data_quality', 'data_source'
                ];

                const hasMarketData = Object.keys(messageData).some(key =>
                  marketDataFields.includes(key)
                );

                if (hasMarketData) {
                  // 可能是市场数据更新，尝试合并到缓存
                  this.cachedMarketData = {
                    ...this.cachedMarketData,
                    ...messageData
                  };

                  // 使用合并后的完整数据更新Redux
                  webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
                  logManager.debug('从未知类型消息中提取并更新了市场数据');
                }

                // 检查是否是基差极值更新
                if (messageData.forward_high !== undefined &&
                    messageData.forward_low !== undefined &&
                    messageData.reverse_high !== undefined &&
                    messageData.reverse_low !== undefined) {

                  // 构造基差极值更新消息
                  const basisData = {
                    forward_high: messageData.forward_high,
                    forward_low: messageData.forward_low,
                    reverse_high: messageData.reverse_high,
                    reverse_low: messageData.reverse_low,
                    timestamp: messageData.timestamp || new Date().toISOString()
                  };

                  // 完全禁用基差极值更新日志以减少冗余
                  // logManager.debug('收到基差极值更新:', basisData);

                  // 处理基差极值更新
                  const basisUpdateData = {
                    forward_high: basisData.forward_high,
                    forward_low: basisData.forward_low,
                    reverse_high: basisData.reverse_high,
                    reverse_low: basisData.reverse_low
                  };

                  // 更新市场数据
                  this.cachedMarketData = {
                    ...this.cachedMarketData,
                    ...basisUpdateData
                  };

                  // 使用合并后的完整数据更新Redux
                  webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
                  logManager.debug('处理后的基差极值数据:', basisUpdateData);
                }
              }
            }
          } catch (defaultError) {
            logManager.error('处理未知类型消息失败:', defaultError);
          }
      }
    } catch (error) {
      // 记录错误，但不中断应用程序
      // 只记录前100个字符，避免日志过大
      const truncatedData = typeof data === 'string' ?
        (data.length > 100 ? data.substring(0, 100) + '...' : data) :
        '二进制数据';

      // 记录消息处理时间，用于性能监控
      const endTime = performance.now();
      // 计算处理时间，但仅在调试模式下记录
      if (__DEV__ && this.logLevel === 'debug') {
        const processingTime = endTime - startTime;
        logManager.debug(`消息处理耗时: ${processingTime.toFixed(2)}ms`);
      }

      // 降级为debug级别，减少日志噪音
      // 只在开发环境和调试模式下记录详细错误
      if (__DEV__ && this.logLevel === 'debug') {
        logManager.debug('处理消息失败:', error, truncatedData);
      } else {
        // 禁用生产环境错误日志
        // logManager.info('WebSocket消息处理失败，将在下次更新时重试');
      }

      // 尝试从错误中恢复
      try {
        // 如果是字符串数据，尝试提取有用信息
        if (typeof data === 'string') {
          // 检查是否包含基差极值数据
          if (data.includes('forward_high') && data.includes('forward_low') &&
              data.includes('reverse_high') && data.includes('reverse_low')) {

            // 尝试使用正则表达式提取基差极值数据
            const basisDataMatch = /"forward_high"\s*:\s*([\d.-]+)\s*,\s*"forward_low"\s*:\s*([\d.-]+)\s*,\s*"reverse_high"\s*:\s*([\d.-]+)\s*,\s*"reverse_low"\s*:\s*([\d.-]+)/.exec(data);

            if (basisDataMatch) {
              const basisData = {
                forward_high: parseFloat(basisDataMatch[1]),
                forward_low: parseFloat(basisDataMatch[2]),
                reverse_high: parseFloat(basisDataMatch[3]),
                reverse_low: parseFloat(basisDataMatch[4])
              };

              // 更新市场数据
              this.cachedMarketData = {
                ...this.cachedMarketData,
                ...basisData
              };

              // 使用合并后的完整数据更新Redux
              webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
              // 完全禁用基差极值日志以减少冗余
              // logManager.debug('收到基差极值更新:', basisData);
              // logManager.debug('处理后的基差极值数据:', basisData);
            }
          }

          // 检查是否包含市场数据
          else if (data.includes('forward_basis') || data.includes('reverse_basis') ||
                   data.includes('spot_price') || data.includes('futures_price')) {

            // 尝试提取市场数据
            try {
              // 使用正则表达式提取市场数据
              const extractMarketData = (field: string): number | undefined => {
                const regex = new RegExp(`"${field}"\\s*:\\s*([\\d.-]+)`);
                const match = regex.exec(data);
                return match ? parseFloat(match[1]) : undefined;
              };

              const marketData: any = {};

              // 尝试提取各个字段
              const fields = ['forward_basis', 'reverse_basis', 'spot_price', 'futures_price'];
              for (const field of fields) {
                const value = extractMarketData(field);
                if (value !== undefined) {
                  marketData[field] = value;
                }
              }

              // 如果提取到了数据，更新市场数据
              if (Object.keys(marketData).length > 0) {
                // 更新市场数据
                this.cachedMarketData = {
                  ...this.cachedMarketData,
                  ...marketData
                };

                // 使用合并后的完整数据更新Redux
                webSocketServiceMiddleware.dispatch(updateMarketData(this.cachedMarketData));
                logManager.debug('从错误中提取并更新了市场数据:', marketData);
              }
            } catch (extractError) {
              // 提取失败，忽略
              logManager.debug('从错误中提取市场数据失败');
            }
          }
        }
      } catch (recoveryError) {
        // 恢复失败，但不影响应用程序运行
        logManager.debug('尝试从错误中恢复失败，忽略此消息');
      }
    } finally {
      // 记录消息处理时间，用于性能监控
      const endTime = performance.now();
      const processingTime = endTime - startTime;

      // 如果处理时间超过100毫秒，记录警告
      if (processingTime > 100) {
        logManager.warn(`WebSocket消息处理时间过长: ${processingTime.toFixed(2)}ms`);
      } else if (shouldLogDetails) {
        logManager.debug(`WebSocket消息处理时间: ${processingTime.toFixed(2)}ms`);
      }
    }
  }

  /**
   * 处理交易执行通知
   * @param data 通知数据
   */
  private handleTradeNotification(data: any): void {
    if (!this.notificationSettings.tradeAlerts) return;

    const notificationType = data.success ? NotificationType.SUCCESS : NotificationType.ERROR;
    const title = data.success ? '交易成功' : '交易失败';
    const message = `${data.direction === 1 ? '正向' : '反向'}交易 ${data.order_id}: ${data.message || ''}`;

    // 显示通知
    notificationManager.show({
      title,
      message,
      type: notificationType,
      priority: NotificationPriority.HIGH,
      action: data.success ? {
        text: '查看详情',
        onPress: () => {
          // 跳转到订单详情页面
          // 这里需要导航逻辑，可以通过回调或事件实现
        }
      } : undefined
    });

    // 播放声音和震动
    this.playNotificationSound();
    this.triggerVibration(notificationType);

    // 保存通知到历史记录
    this.saveNotification({
      id: data.order_id || `TRADE_${Date.now()}`,
      type: 'trade',
      title,
      content: message,
      created_at: new Date().toISOString(),
      is_read: false,
      user_id: this.userId
    });
  }

  /**
   * 处理交易执行结果
   * @param data 交易执行结果数据
   */
  private handleTradeExecutionResult(data: any): void {
    if (!data) {
      logManager.warn('收到空的交易执行结果，忽略');
      return;
    }

    // 移除交易执行结果日志，只保留错误日志

    // 获取订单ID
    const orderId = data.order_id;
    if (!orderId) {
      logManager.warn('交易执行结果缺少订单ID，忽略');
      return;
    }

    // 根据执行结果更新订单状态
    try {
      // 确定订单状态
      const status = data.success ? 'completed' : 'failed';

      // 更新Redux中的订单状态
      webSocketServiceMiddleware.dispatch(updateOrderStatus({
        id: orderId,
        status: status
      }));

      // 显示通知
      const notificationType = data.success ? NotificationType.SUCCESS : NotificationType.ERROR;
      const title = data.success ? '交易成功' : '交易失败';
      const message = data.message || (data.success ? '交易已成功执行' : '交易执行失败');

      notificationManager.show({
        title,
        message,
        type: notificationType,
        priority: NotificationPriority.HIGH
      });

      // 播放声音和震动
      this.playNotificationSound();
      this.triggerVibration(notificationType);

      // 如果交易成功，刷新持仓数据
      if (data.success) {
        this.requestPositions();
      }
    } catch (error) {
      logManager.error('处理交易执行结果失败:', error);
    }
  }

  /**
   * 处理系统通知
   * @param data 通知数据
   */
  // 移除重复的方法，避免编译错误
  // private handleSystemNotification(data: any): void {
  //   // 方法内容已移除
  // }

  /**
   * 处理风控警报
   * @param data 警报数据
   */
  private handleRiskAlert(data: any): void {
    const title = '系统通知';
    const message = data.message || `${data.reason}: ${data.value}`;

    // 显示通知
    notificationManager.show({
      title,
      message,
      type: NotificationType.INFO,
      priority: NotificationPriority.NORMAL
    });

    // 播放声音
    this.playNotificationSound();

    // 保存通知到历史记录
    this.saveNotification({
      id: data.id || `NOTICE_${Date.now()}`,
      type: 'system',
      title,
      content: message,
      created_at: new Date().toISOString(),
      is_read: false,
      user_id: this.userId
    });
  }

  /**
   * 从消息中提取基差数据
   * 用于处理无法正常解析的消息
   * @param data 消息数据
   * @returns 是否成功提取并处理了数据
   */
  private extractAndProcessBasisData(data: string): boolean {
    // 确保缓存的市场数据是一个有效的对象
    if (!this.cachedMarketData || typeof this.cachedMarketData !== 'object') {
      this.cachedMarketData = {};
    }

    // 调用提取函数
    const result = extractBasisData(data, this.cachedMarketData, (action) => {
      webSocketServiceMiddleware.dispatch(action);
    });

    // 如果提取成功，记录日志
    if (result) {
      logManager.info('成功从消息中提取并处理了基差数据');
    }

    return result;
  }

  /**
   * 处理基差触发通知
   * @param data 通知数据
   */
  // 移除有问题的方法，避免编译错误
  // private handleBasisTriggerUI(data: any): void {
  //   // 方法内容已移除
  // }

  /**
   * 开始心跳检测
   * 使用更短的心跳间隔，确保连接保持活跃
   */
  // 移除有问题的方法，避免编译错误
  // private startHeartbeat(): void {
  //   // 方法内容已移除
  // }

  /**
   * 停止心跳检测
   * 清除所有定时器
   */
  // 移除有问题的方法，避免编译错误
  // private stopHeartbeat(): void {
  //   // 方法内容已移除
  // }

  /**
   * 订阅通知类型
   */
  private subscribeToNotifications(): void {
    if (this.subscribedTypes && this.subscribedTypes.length > 0) {
      this.sendMessage({
        type: 'subscribe',
        notification_types: this.subscribedTypes
      });
    }
  }

  /**
   * 更新通知订阅
   * @param types 通知类型数组
   */
  public updateSubscription(types: string[]): void {
    if (types && Array.isArray(types)) {
      this.subscribedTypes = types;

      if (this.status === WebSocketStatus.CONNECTED) {
        this.subscribeToNotifications();
      }
    }
  }

  /**
   * 更新通知设置
   * @param settings 通知设置
   */
  public async updateNotificationSettings(settings: any): Promise<void> {
    this.notificationSettings = {
      ...this.notificationSettings,
      ...settings
    };

    // 保存设置到本地存储
    await AsyncStorage.setItem(
      'notification_settings',
      JSON.stringify(this.notificationSettings)
    );
  }

  /**
   * 加载通知设置
   */
  private async loadNotificationSettings(): Promise<void> {
    try {
      const settings = await AsyncStorage.getItem('notification_settings');

      if (settings) {
        this.notificationSettings = {
          ...this.notificationSettings,
          ...JSON.parse(settings)
        };
      }
    } catch (error) {
      console.error('加载通知设置失败:', error);
    }
  }

  /**
   * 保存通知到历史记录
   * @param notification 通知对象
   */
  private saveNotification(notification: Notification): void {
    // 添加到历史记录
    this.notificationHistory.unshift(notification);

    // 限制历史记录大小
    if (this.notificationHistory.length > this.maxHistorySize) {
      this.notificationHistory = this.notificationHistory.slice(0, this.maxHistorySize);
    }

    // 保存到本地存储
    this.saveNotificationHistory();

    // 添加到Redux状态
    webSocketServiceMiddleware.dispatch(addNotification(notification));
  }

  /**
   * 保存通知历史到本地存储
   */
  private async saveNotificationHistory(): Promise<void> {
    try {
      await AsyncStorage.setItem(
        'notification_history',
        JSON.stringify(this.notificationHistory)
      );
    } catch (error) {
      console.error('保存通知历史失败:', error);
    }
  }

  /**
   * 加载通知历史
   */
  private async loadNotificationHistory(): Promise<void> {
    try {
      const history = await AsyncStorage.getItem('notification_history');

      if (history) {
        try {
          this.notificationHistory = JSON.parse(history);
        } catch (parseError) {
          // 如果解析失败，使用空数组
          logManager.error('解析通知历史失败:', parseError);
          this.notificationHistory = [];
        }
      }
    } catch (error) {
      // 记录错误但不抛出异常
      logManager.error('加载通知历史失败:', error);
      // 使用空数组作为默认值
      this.notificationHistory = [];
    }
  }

  /**
   * 获取通知历史
   * @param page 页码
   * @param pageSize 每页数量
   */
  public getNotificationHistory(page: number = 1, pageSize: number = 20): {
    notifications: Notification[];
    total: number;
  } {
    const start = (page - 1) * pageSize;
    const end = start + pageSize;

    return {
      notifications: this.notificationHistory.slice(start, end),
      total: this.notificationHistory.length
    };
  }

  /**
   * 标记通知为已读
   * @param id 通知ID
   */
  public markNotificationAsRead(id: string): void {
    // 更新本地历史记录
    this.notificationHistory = this.notificationHistory.map(notification => {
      if (notification.id === id) {
        return {
          ...notification,
          is_read: true
        };
      }
      return notification;
    });

    // 保存到本地存储
    this.saveNotificationHistory();
  }

  /**
   * 标记所有通知为已读
   */
  public markAllNotificationsAsRead(): void {
    // 更新本地历史记录
    this.notificationHistory = this.notificationHistory.map(notification => ({
      ...notification,
      is_read: true
    }));

    // 保存到本地存储
    this.saveNotificationHistory();
  }

  /**
   * 清空通知历史
   */
  public clearNotificationHistory(): void {
    this.notificationHistory = [];

    // 保存到本地存储
    this.saveNotificationHistory();
  }

  /**
   * 获取未读通知数量
   */
  public getUnreadCount(): number {
    return this.notificationHistory.filter(notification => !notification.is_read).length;
  }

  /**
   * 获取WebSocket状态
   */
  public getStatus(): WebSocketStatus {
    return this.status;
  }

  /**
   * 请求最新市场数据
   */
  public requestMarketData(): void {
    this.sendMessage({
      type: 'get_market_data'
    });
  }

  /**
   * 请求当前持仓数据
   */
  public requestPositions(): void {
    this.sendMessage({
      type: 'get_positions'
    });
  }

  /**
   * 请求系统状态数据
   */
  public requestSystemStatus(): void {
    this.sendMessage({
      type: 'get_system_status'
    });
  }

  /**
   * 添加系统通知监听器
   * @param listener 监听器函数
   */
  public addSystemNotificationListener(listener: (data: any) => void): void {
    this.systemNotificationListeners.push(listener);
  }

  /**
   * 移除系统通知监听器
   * @param listener 监听器函数
   */
  public removeSystemNotificationListener(listener: (data: any) => void): void {
    const index = this.systemNotificationListeners.indexOf(listener);
    if (index > -1) {
      this.systemNotificationListeners.splice(index, 1);
    }
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    // 断开WebSocket连接
    this.disconnect();

    // 清空系统通知监听器
    this.systemNotificationListeners = [];

    // 卸载通知声音
    if (this.sound) {
      try {
        this.sound.unloadAsync();
        logManager.info('通知声音资源已卸载');
      } catch (error) {
        logManager.error('卸载通知声音资源失败:', error);
      } finally {
        this.sound = null;
      }
    }
  }
}

// 创建单例实例
export const webSocketService = new WebSocketService();

export default webSocketService;
