/**
 * 从消息中提取基差数据
 * 用于处理无法正常解析的消息
 */
import { updateMarketData } from '../redux/slices/marketSlice';
import logManager from '../utils/logManager';

/**
 * 从消息中提取基差极值数据
 * @param data 消息数据
 * @param cachedMarketData 缓存的市场数据
 * @param dispatch 派发函数
 * @returns 是否成功提取并处理了数据
 */
export function extractAndProcessBasisData(
  data: string,
  cachedMarketData: any,
  dispatch: (action: any) => void
): boolean {
  try {
    // 尝试提取基差极值数据
    const basisData: any = {};

    // 使用正则表达式提取数值 - 增强匹配模式以处理更多格式
    const forwardHighMatch = /forward[\s_\-\.]*high"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const forwardLowMatch = /forward[\s_\-\.]*low"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const reverseHighMatch = /reverse[\s_\-\.]*high"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const reverseLowMatch = /reverse[\s_\-\.]*low"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const forwardBasisMatch = /forward[\s_\-\.]*basis"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const reverseBasisMatch = /reverse[\s_\-\.]*basis"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);

    // 尝试提取更多市场数据 - 增强匹配模式
    // 现货价格相关
    const spotBidMatch = /spot[\s_\-\.]*bid"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const spotAskMatch = /spot[\s_\-\.]*ask"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const spotPriceMatch = /spot[\s_\-\.]*price"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const spotMatch = /spot"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);

    // 期货价格相关
    const futureBidMatch = /future[\s_\-\.]*bid"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const futureAskMatch = /future[\s_\-\.]*ask"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const futurePriceMatch = /future[\s_\-\.]*price"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const futureMatch = /future"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);

    // 黄金价格相关
    const goldPriceMatch = /gold[\s_\-\.]*price"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const goldBidMatch = /gold[\s_\-\.]*bid"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const goldAskMatch = /gold[\s_\-\.]*ask"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);

    // 价格相关（通用）
    const priceMatch = /price"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const bidMatch = /bid"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);
    const askMatch = /ask"?\s*[:=]\s*(-?\d+\.?\d*)/i.exec(data);

    // 提取基差极值数据
    if (forwardHighMatch) basisData.forward_high = parseFloat(forwardHighMatch[1]);
    if (forwardLowMatch) basisData.forward_low = parseFloat(forwardLowMatch[1]);
    if (reverseHighMatch) basisData.reverse_high = parseFloat(reverseHighMatch[1]);
    if (reverseLowMatch) basisData.reverse_low = parseFloat(reverseLowMatch[1]);

    // 提取当前基差数据
    if (forwardBasisMatch) basisData.forward_basis = parseFloat(forwardBasisMatch[1]);
    if (reverseBasisMatch) basisData.reverse_basis = parseFloat(reverseBasisMatch[1]);

    // 提取价格数据 - 按优先级处理
    // 现货价格
    if (spotBidMatch) basisData.spot_bid = parseFloat(spotBidMatch[1]);
    if (spotAskMatch) basisData.spot_ask = parseFloat(spotAskMatch[1]);
    if (spotPriceMatch) {
      const spotPrice = parseFloat(spotPriceMatch[1]);
      if (!basisData.spot_bid) basisData.spot_bid = spotPrice;
      if (!basisData.spot_ask) basisData.spot_ask = spotPrice;
    }
    if (spotMatch) {
      const spotPrice = parseFloat(spotMatch[1]);
      if (!basisData.spot_bid) basisData.spot_bid = spotPrice;
      if (!basisData.spot_ask) basisData.spot_ask = spotPrice;
    }

    // 期货价格
    if (futureBidMatch) basisData.future_bid = parseFloat(futureBidMatch[1]);
    if (futureAskMatch) basisData.future_ask = parseFloat(futureAskMatch[1]);
    if (futurePriceMatch) {
      const futurePrice = parseFloat(futurePriceMatch[1]);
      if (!basisData.future_bid) basisData.future_bid = futurePrice;
      if (!basisData.future_ask) basisData.future_ask = futurePrice;
    }
    if (futureMatch) {
      const futurePrice = parseFloat(futureMatch[1]);
      if (!basisData.future_bid) basisData.future_bid = futurePrice;
      if (!basisData.future_ask) basisData.future_ask = futurePrice;
    }

    // 黄金价格
    if (goldBidMatch) {
      const goldBid = parseFloat(goldBidMatch[1]);
      if (!basisData.spot_bid) basisData.spot_bid = goldBid;
    }
    if (goldAskMatch) {
      const goldAsk = parseFloat(goldAskMatch[1]);
      if (!basisData.spot_ask) basisData.spot_ask = goldAsk;
    }
    if (goldPriceMatch) {
      const goldPrice = parseFloat(goldPriceMatch[1]);
      if (!basisData.spot_bid) basisData.spot_bid = goldPrice;
      if (!basisData.spot_ask) basisData.spot_ask = goldPrice;
    }

    // 通用价格（最低优先级）
    if (bidMatch && !basisData.spot_bid) basisData.spot_bid = parseFloat(bidMatch[1]);
    if (askMatch && !basisData.spot_ask) basisData.spot_ask = parseFloat(askMatch[1]);
    if (priceMatch) {
      const price = parseFloat(priceMatch[1]);
      if (!basisData.spot_bid) basisData.spot_bid = price;
      if (!basisData.spot_ask) basisData.spot_ask = price;
    }

    // 添加时间戳和数据质量标记
    basisData.timestamp = new Date().toISOString();
    basisData.data_quality = 'realtime'; // 标记为实时数据
    basisData.data_source = 'websocket'; // 标记数据来源

    // 计算基差（如果有足够的数据）- 修正基差计算公式
    if (basisData.spot_bid !== undefined && basisData.spot_ask !== undefined &&
        basisData.future_bid !== undefined && basisData.future_ask !== undefined) {
      // 正向基差 = 现货买入价 - 期货卖出价 (spot_bid - future_ask)
      if (basisData.forward_basis === undefined) {
        basisData.forward_basis = parseFloat((basisData.spot_bid - basisData.future_ask).toFixed(2));
        // 移除INFO级别日志
      }

      // 反向基差 = 现货卖出价 - 期货买入价 (spot_ask - future_bid)
      if (basisData.reverse_basis === undefined) {
        basisData.reverse_basis = parseFloat((basisData.spot_ask - basisData.future_bid).toFixed(2));
        // 移除INFO级别日志
      }
    }

    // 检查是否提取到了数据
    const hasExtractedData = Object.keys(basisData).length > 3; // 至少有一个数据字段、时间戳和数据质量标记

    if (hasExtractedData) {
      // 记录提取到的数据
      if (basisData.forward_high !== undefined ||
          basisData.forward_low !== undefined ||
          basisData.reverse_high !== undefined ||
          basisData.reverse_low !== undefined) {
        // 移除INFO级别日志
      }

      if (basisData.forward_basis !== undefined ||
          basisData.reverse_basis !== undefined) {
        // 移除INFO级别日志
      }

      if (basisData.spot_bid !== undefined ||
          basisData.spot_ask !== undefined ||
          basisData.future_bid !== undefined ||
          basisData.future_ask !== undefined) {
        // 移除INFO级别日志
      }

      // 更新市场数据 - 保留原有数据，只更新提取到的字段
      const updatedMarketData = {
        ...cachedMarketData,
        ...basisData
      };

      // 使用合并后的完整数据更新Redux
      dispatch(updateMarketData(updatedMarketData));
      // 移除INFO级别日志

      return true;
    }

    return false;
  } catch (error) {
    logManager.error('提取基差数据失败:', error);
    return false;
  }
}
