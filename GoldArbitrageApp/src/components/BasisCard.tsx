import React, { useState, useMemo, memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { formatBasis } from '../utils/format';
import { Colors, Typography, CommonStyles, ButtonStyles } from '../utils/styles';
import BasisEditModal from './BasisEditModal';
import { BASIS_PRECISION } from '../utils/constants';
import { preciseRound, calculateForwardBasis, calculateReverseBasis } from '../utils/math';
import FastTouchable from './common/FastTouchable';
import { LinearGradient } from 'expo-linear-gradient';

interface BasisCardProps {
  type: 'forward' | 'reverse';
  value: number;
  highValue: number;
  lowValue: number;
  triggerRanges: Array<{
    min?: number;
    max?: number;
    condition?: string;
    enabled?: boolean;
    autoTrade?: boolean;
    positionSize?: number;
    id?: string;
  }>;
  onOpenPosition: () => void;
  previousValue?: number;
  onEditRanges?: (type: 'forward' | 'reverse', ranges: Array<{
    min?: number;
    max?: number;
    condition?: string;
    enabled?: boolean;
    autoTrade?: boolean;
    positionSize?: number;
    id?: string;
  }>) => void;
}

// 使用React.memo包装组件，避免不必要的重新渲染
const BasisCard = ({
  type,
  value,
  highValue,
  lowValue,
  triggerRanges,
  onOpenPosition,
  previousValue,
  onEditRanges,
}: BasisCardProps) => {
  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);

  // 打开编辑弹窗
  const openEditModal = () => {
    setModalVisible(true);
  };

  // 保存编辑的阈值
  const handleSaveRanges = (ranges: Array<{ min?: number; max?: number; condition?: string }>) => {
    if (onEditRanges) {
      onEditRanges(type, ranges);
    }
    setModalVisible(false);
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setModalVisible(false);
  };

  // 使用useMemo缓存计算结果，避免每次渲染重新计算
  // 判断基差颜色 - 修复：基差符号与盈利无关，统一显示为中性色
  const basisColor = useMemo(() => {
    if (value === null || value === undefined) return { color: Colors.text };
    // 基差可以是正数或负数，不应该根据符号判断盈利
    // 基差的盈利性取决于是否朝着目标方向变化，而不是符号
    return { color: Colors.text }; // 统一使用中性色显示基差
  }, [value, type]);

  // 判断是正向还是反向基差
  const title = useMemo(() => {
    return type === 'forward' ? '正向基差' : '反向基差';
  }, [type]);

  // 获取对应的按钮标题
  const buttonTitle = useMemo(() => {
    return type === 'forward' ? '正向开仓' : '反向开仓';
  }, [type]);

  // 获取基差说明
  const basisDescription = useMemo(() => {
    if (type === 'forward') {
      return '正向基差 = 现货买入价 - 期货卖出价';
    }
    return '反向基差 = 现货卖出价 - 期货买入价';
  }, [type]);

  // 安全地格式化数值，处理null或undefined值
  const formatValue = (val: number | null | undefined): string => {
    if (val === null || val === undefined || val === 0) {
      // 如果值无效，返回0.00而不是默认值
      return '0.00';
    }
    // 确保负数正确显示
    return val.toFixed(BASIS_PRECISION);
  };

  // 高值和低值的格式化结果缓存
  const formattedHighValue = useMemo(() => formatValue(highValue), [highValue]);
  const formattedLowValue = useMemo(() => formatValue(lowValue), [lowValue]);

  // 获取卡片背景色
  const cardBackground = useMemo(() => {
    // 返回适合渐变的浅色
    return type === 'forward'
      ? ['rgba(55, 178, 108, 0.06)', 'rgba(55, 178, 108, 0.01)']  // 利润颜色的浅色渐变
      : ['rgba(255, 77, 79, 0.06)', 'rgba(255, 77, 79, 0.01)'];   // 亏损颜色的浅色渐变
  }, [type]);

  // 价格变动指示器
  const basisIndicator = useMemo(() => {
    if (!previousValue || value === null || value === undefined || previousValue === value) return null;

    const isUp = value > previousValue;
    const indicatorText = isUp ? '↑' : '↓';
    const indicatorColor = isUp ? Colors.profit : Colors.loss;

    return (
      <Text style={[styles.indicator, { color: indicatorColor }]}>
        {indicatorText}
      </Text>
    );
  }, [value, previousValue]);

  // 判断当前基差是否在触发区间内
  const isInTriggerRange = useMemo(() => {
    if (value === null || value === undefined) return false;

    // 确保triggerRanges是一个数组
    const ranges = Array.isArray(triggerRanges) ? triggerRanges : [];

    for (const range of ranges) {
      // 检查是否启用
      const isEnabled = range.enabled !== undefined ? range.enabled : true;
      if (!isEnabled) continue; // 如果未启用，跳过此区间

      // 处理区间范围
      if (range.min !== undefined && range.max !== undefined) {
        if (range.min <= value && value <= range.max) {
          return true;
      }
      }
      // 处理条件表达式
      else if (range.condition) {
        if (range.condition.startsWith('>=')) {
          const threshold = parseFloat(range.condition.substring(2));
          if (!isNaN(threshold) && value >= threshold) {
            return true;
            }
        } else if (range.condition.startsWith('<=')) {
          const threshold = parseFloat(range.condition.substring(2));
          if (!isNaN(threshold) && value <= threshold) {
            return true;
          }
        }
      }
    }

    return false;
  }, [value, triggerRanges]);

  // 触发范围可视化
  const renderTriggerRanges = useMemo(() => {
    if (!Array.isArray(triggerRanges) || triggerRanges.length === 0) {
      return (
        <View style={styles.rangesContainer}>
          <Text style={styles.rangesTitle}>触发区间</Text>
          <Text style={styles.rangeItem}>未设置</Text>
        </View>
      );
      }

    return (
      <View style={styles.rangesContainer}>
        <Text style={styles.rangesTitle}>触发区间</Text>
        <View style={styles.rangesList}>
          {triggerRanges.map((range, index) => {
            // 检查是否启用和自动交易
            const isEnabled = range.enabled !== undefined ? range.enabled : true;
            const isAutoTrade = range.autoTrade !== undefined ? range.autoTrade : false;

            // 优先处理条件表达式
            if (range.condition) {
              return (
                <View style={styles.rangeItemContainer} key={`condition-${index}`}>
                  <View style={[
                    styles.rangeBullet,
                    { backgroundColor: type === 'forward' ? Colors.profit : Colors.loss },
                    !isEnabled && styles.disabledRangeBullet
                  ]} />
                  <Text style={[
                    styles.rangeItem,
                    !isEnabled && styles.disabledRangeItem
                  ]}>
                    {range.condition}
                  </Text>
                  {isAutoTrade && (
                    <View style={styles.autoTradeIndicator}>
                      <Text style={styles.autoTradeText}>自动</Text>
                    </View>
                  )}
                </View>
              );
            } else if (range.min !== undefined && range.max !== undefined) {
              // 仅无condition时渲染区间
              return (
                <View style={styles.rangeItemContainer} key={`range-${index}`}>
                  <View style={[
                    styles.rangeBullet,
                    { backgroundColor: type === 'forward' ? Colors.profit : Colors.loss },
                    !isEnabled && styles.disabledRangeBullet
                  ]} />
                  <Text style={[
                    styles.rangeItem,
                    !isEnabled && styles.disabledRangeItem
                  ]}>
                    {preciseRound(range.min, BASIS_PRECISION).toFixed(BASIS_PRECISION)} 到 {preciseRound(range.max, BASIS_PRECISION).toFixed(BASIS_PRECISION)}
                  </Text>
                  {isAutoTrade && (
                    <View style={styles.autoTradeIndicator}>
                      <Text style={styles.autoTradeText}>自动</Text>
                    </View>
                  )}
                </View>
              );
            }
            return null;
          })}
        </View>
      </View>
    );
  }, [triggerRanges, type]);

  // 是否高亮按钮
  const shouldHighlightButton = isInTriggerRange;

  // 获取按钮说明文本
  const buttonDescription = useMemo(() => {
    if (type === 'forward') {
      return '现货买跌-期货买涨';
    }
    return '现货买涨-期货买跌';
  }, [type]);

  // 格式化基差值
  const formattedBasisValue = useMemo(() => formatBasis(value), [value]);

  return (
    <>
      <View style={styles.card}>
        <LinearGradient
          colors={cardBackground}
          style={styles.gradientBackground}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
        <View style={styles.header}>
            <View>
          <View style={styles.titleContainer}>
                <Text style={styles.title}>{title}</Text>
                {shouldHighlightButton && (
            <View style={styles.triggerIndicator}>
              <Text style={styles.triggerText}>可开仓</Text>
            </View>
          )}
                <FastTouchable onPress={openEditModal} style={styles.editButton}>
                  <Text style={styles.editButtonText}>编辑</Text>
                </FastTouchable>
              </View>
              <Text style={styles.description}>{basisDescription}</Text>
            </View>
        </View>

        <View style={styles.mainContent}>
          <View style={styles.basisContainer}>
            <View style={styles.basisValueContainer}>
                <Text style={[styles.basisValue, basisColor]}>
                  {formattedBasisValue}
              </Text>
                {basisIndicator}
            </View>

            <View style={styles.historyContainer}>
              <View style={styles.historyItem}>
                <Text style={styles.historyLabel}>历史最高:</Text>
                  <Text style={styles.historyValue}>{formattedHighValue}</Text>
              </View>
              <View style={styles.historyItem}>
                <Text style={styles.historyLabel}>历史最低:</Text>
                  <Text style={styles.historyValue}>{formattedLowValue}</Text>
              </View>
            </View>
          </View>

          <View style={styles.rightSection}>
              {renderTriggerRanges}
            <View style={styles.buttonContainer}>
              <FastTouchable
                style={[
                  type === 'forward' ? styles.forwardButton : styles.reverseButton,
                    shouldHighlightButton && styles.highlightButton
                ]}
                onPress={onOpenPosition}
                activeOpacity={0.7}
                rippleColor={type === 'forward' ? Colors.profit : Colors.loss}
              >
                  <Text style={styles.buttonText}>{buttonTitle}</Text>
                  <Text style={styles.buttonDescription}>{buttonDescription}</Text>
              </FastTouchable>
            </View>
          </View>
        </View>
        </LinearGradient>
      </View>

      {/* 使用新的基差编辑弹窗组件 */}
      <BasisEditModal
        visible={modalVisible}
        type={type}
        initialRanges={triggerRanges}
        onSave={handleSaveRanges}
        onCancel={handleCancelEdit}
      />
    </>
  );
};

// 自定义比较函数，只在重要的props变化时重新渲染
function areBasisPropsEqual(prevProps: BasisCardProps, nextProps: BasisCardProps) {
  return (
    prevProps.type === nextProps.type &&
    prevProps.value === nextProps.value &&
    prevProps.highValue === nextProps.highValue &&
    prevProps.lowValue === nextProps.lowValue &&
    prevProps.previousValue === nextProps.previousValue &&
    JSON.stringify(prevProps.triggerRanges) === JSON.stringify(nextProps.triggerRanges)
    // 不比较回调函数，因为它们通常是稳定的引用
  );
}

// 使用memo包装组件，并传入自定义比较函数
export default memo(BasisCard, areBasisPropsEqual);

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.cardBackground,
    borderRadius: 10,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 2,
    overflow: 'hidden'
  },
  gradientBackground: {
    padding: 12,
    borderRadius: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  title: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.text,
    marginRight: 8,
  },
  description: {
    fontSize: 11,
    fontWeight: 'normal',
    color: Colors.textSecondary,
    marginTop: 2,
  },
  mainContent: {
    flexDirection: 'row',
    marginTop: 5,
  },
  basisContainer: {
    flex: 1,
    marginRight: 8,
  },
  basisValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  basisValue: {
    fontSize: 22,
    fontWeight: 'bold',
    fontFamily: 'monospace',
    letterSpacing: -0.5,
  },
  indicator: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 5,
  },
  historyContainer: {
    marginBottom: 5,
    marginTop: 2,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  historyLabel: {
    marginRight: 5,
    fontSize: 11,
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  historyValue: {
    fontWeight: '600',
    fontSize: 11,
    color: Colors.text,
  },
  rightSection: {
    flex: 1,
    justifyContent: 'space-between',
  },
  rangesContainer: {
    marginBottom: 6,
  },
  rangesTitle: {
    marginBottom: 3,
    fontSize: 11,
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  rangesList: {
    marginLeft: 3,
  },
  rangeItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 1,
  },
  rangeBullet: {
    width: 5,
    height: 5,
    borderRadius: 2.5,
    marginRight: 5,
  },
  rangeItem: {
    fontSize: 11,
    fontWeight: 'normal',
    color: Colors.text,
  },
  conditionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 4,
    paddingVertical: 1,
    paddingHorizontal: 5,
  },
  conditionOperator: {
    fontSize: 11,
    fontWeight: 'bold',
    color: Colors.text,
    marginRight: 2,
  },
  conditionValue: {
    fontSize: 11,
    fontWeight: 'normal',
    color: Colors.text,
  },
  triggerIndicator: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    marginRight: 6,
  },
  triggerText: {
    fontSize: 9,
    color: Colors.secondary,
    fontWeight: 'bold',
  },
  forwardButton: {
    backgroundColor: Colors.profit,
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  reverseButton: {
    backgroundColor: Colors.loss,
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  highlightButton: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 0,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.cardBackground,
    marginBottom: 2,
  },
  buttonDescription: {
    fontSize: 10,
    fontWeight: 'normal',
    color: Colors.cardBackground,
    opacity: 0.9,
  },
  buttonContainer: {
    width: '100%',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  editButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.08)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  editButtonText: {
    fontSize: 9,
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  // 新增样式
  disabledRangeBullet: {
    opacity: 0.4,
  },
  disabledRangeItem: {
    color: Colors.textSecondary,
    textDecorationLine: 'line-through',
    textDecorationStyle: 'solid',
  },
  autoTradeIndicator: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 4,
    marginLeft: 5,
  },
  autoTradeText: {
    fontSize: 8,
    fontWeight: 'bold',
    color: Colors.cardBackground,
  }
});