import React, { useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { Order } from '../types/api';
import {
  formatPrice,
  formatBasis,
  formatAmount,
  formatDateTime,
  getDirectionText,
  getPnlType
} from '../utils/format';
import { Colors, CommonStyles, ButtonStyles } from '../utils/styles';

interface PositionCardProps {
  order: Order;
  onClose: (orderId: string) => void;
}

const PositionCard: React.FC<PositionCardProps> = ({ order, onClose }) => {
  // 从Redux状态中获取账户信息和用户设置
  const spotAccount = useSelector((state: RootState) => state.settings.spotAccount);
  const futureAccount = useSelector((state: RootState) => state.settings.futureAccount);
  const userSettings = useSelector((state: RootState) => state.settings.userSettings);

  // 从Redux状态中获取实时市场数据
  const marketData = useSelector((state: RootState) => state.market.data);

  // 获取交易成本信息
  const spotCostPerGram = spotAccount?.cost_per_gram || 0.4; // 默认值为0.4
  const futureCostPerGram = futureAccount?.cost_per_gram || 0.01; // 默认值为0.01

  // 计算每克的总成本
  const totalCostPerGram = spotCostPerGram + futureCostPerGram;
  // 格式化后的每克总成本（两位小数）
  const formattedTotalCostPerGram = totalCostPerGram.toFixed(2);

  // 获取用户设置的盈利价差
  const profitDiff = userSettings?.target_profit_diff || 0.95; // 默认值为0.95

  // 计算每克的净预期盈利 = 盈利价差(元/克) - 每克总成本(元/克)
  const expectedProfitPerGram = profitDiff - totalCostPerGram;

  // 计算总净预期盈利 = 每克净预期盈利 * 克重
  const expectedProfit = expectedProfitPerGram * order.base_weight;

  // 直接从Redux中获取实时行情页面计算好的基差值
  const currentBasis = useMemo(() => {
    if (!marketData) {
      // 如果没有实时数据，使用订单中存储的基差
      return order.current_basis || order.open_basis;
    }

    // 根据订单的开仓方向选择对应的基差
    if (order.direction === 1) {
      // 正向开仓：使用正向基差
      return marketData.forward_basis || order.current_basis || order.open_basis;
    } else {
      // 反向开仓：使用反向基差
      return marketData.reverse_basis || order.current_basis || order.open_basis;
    }
  }, [marketData, order.direction, order.current_basis, order.open_basis]);

  // 获取实时现货和期货价格
  const currentSpotPrice = useMemo(() => {
    if (!marketData) {
      return order.current_spot_price || order.spot_open_price;
    }
    // 根据订单方向选择对应的价格
    return order.direction === 1 ? marketData.spot_bid : marketData.spot_ask;
  }, [marketData, order.direction, order.current_spot_price, order.spot_open_price]);

  const currentFuturePrice = useMemo(() => {
    if (!marketData) {
      return order.current_future_price || order.future_open_price;
    }
    // 根据订单方向选择对应的价格
    return order.direction === 1 ? marketData.future_ask : marketData.future_bid;
  }, [marketData, order.direction, order.current_future_price, order.future_open_price]);

  // 渲染开仓价格部分
  const renderOpenPrices = () => (
    <View style={styles.priceSection}>
      <Text style={styles.sectionTitle}>开仓价格</Text>
      <View style={[styles.row, styles.compactRow]}>
        <Text style={styles.priceLabel}>现货: </Text>
        <Text style={styles.priceValue}>{formatPrice(order.spot_open_price)}</Text>
      </View>
      <View style={[styles.row, styles.compactRow]}>
        <Text style={styles.priceLabel}>期货: </Text>
        <Text style={styles.priceValue}>{formatPrice(order.future_open_price)}</Text>
      </View>
      <View style={[styles.row, styles.compactRow]}>
        <Text style={styles.priceLabel}>开仓基差: </Text>
        <Text style={styles.priceValue}>{formatBasis(order.open_basis)}</Text>
      </View>
    </View>
  );

  // 🎯 优化：计算基差进度和平仓建议
  const basisProgress = useMemo(() => {
    const targetDiff = order.target_basis - order.open_basis;
    const currentDiff = currentBasis - order.open_basis;

    if (Math.abs(targetDiff) < 0.01) return 0; // 避免除零

    const progress = (currentDiff / targetDiff) * 100;
    return Math.max(0, Math.min(100, progress)); // 限制在0-100%之间
  }, [currentBasis, order.open_basis, order.target_basis]);

  // 🎯 优化：平仓建议
  const closeRecommendation = useMemo(() => {
    if (!marketData) return null;

    // 检查是否达到目标基差
    const isTargetReached = order.direction === 1
      ? currentBasis >= order.target_basis
      : currentBasis <= order.target_basis;

    if (isTargetReached) {
      return { type: 'target', message: '已达目标，建议平仓', color: Colors.profit };
    }

    // 检查是否接近目标（90%进度）
    if (basisProgress >= 90) {
      return { type: 'near', message: '接近目标，可考虑平仓', color: Colors.warning };
    }

    return null;
  }, [currentBasis, order.target_basis, order.direction, basisProgress, marketData]);

  // 渲染目标基差部分
  const renderTargetBasis = () => (
    <View style={styles.targetBasisSection}>
      <View style={[styles.row, styles.compactRow]}>
        <Text style={styles.targetBasisLabel}>目标基差: </Text>
        <Text style={styles.targetBasisValue}>{formatBasis(order.target_basis)}</Text>
      </View>
      <View style={[styles.row, styles.compactRow]}>
        <Text style={styles.targetBasisLabel}>盈利价差: </Text>
        <Text style={styles.targetBasisValue}>{profitDiff.toFixed(2)}元/克</Text>
      </View>
      <View style={[styles.row, styles.compactRow]}>
        <Text style={styles.targetBasisLabel}>目标进度: </Text>
        <Text style={[
          styles.targetBasisValue,
          { color: basisProgress >= 90 ? Colors.profit : basisProgress >= 50 ? Colors.warning : Colors.text }
        ]}>
          {basisProgress.toFixed(1)}%
        </Text>
      </View>
      {closeRecommendation && (
        <View style={[styles.row, styles.compactRow]}>
          <Text style={[
            styles.recommendationText,
            { color: closeRecommendation.color }
          ]}>
            💡 {closeRecommendation.message}
          </Text>
        </View>
      )}
    </View>
  );

  // 渲染当前价格部分
  const renderCurrentPrices = () => (
    <View style={styles.priceSection}>
      <Text style={styles.sectionTitle}>当前价格</Text>
      <View style={[styles.row, styles.compactRow]}>
        <Text style={styles.priceLabel}>现货: </Text>
        <Text style={styles.priceValue}>
          {formatPrice(currentSpotPrice)}
        </Text>
      </View>
      <View style={[styles.row, styles.compactRow]}>
        <Text style={styles.priceLabel}>期货: </Text>
        <Text style={styles.priceValue}>
          {formatPrice(currentFuturePrice)}
        </Text>
      </View>
      <View style={[styles.row, styles.compactRow]}>
        <Text style={styles.priceLabel}>当前基差: </Text>
        <Text style={[
          styles.priceValue,
          {
            color: marketData ?
              (order.direction === 1 ?
                (currentBasis > 0 ? Colors.profit : Colors.loss) :
                (currentBasis < 0 ? Colors.profit : Colors.loss)
              ) : Colors.text
          }
        ]}>
          {formatBasis(currentBasis)}
        </Text>
      </View>
    </View>
  );

  // 渲染盈亏信息部分
  const renderPnlInfo = () => {
    // 计算每手的成本
    const spotCost = order.base_weight * spotCostPerGram;
    const futureCost = order.base_weight * futureCostPerGram;
    const totalCost = spotCost + futureCost;

    // 格式化每克成本（两位小数）
    const formattedSpotCostPerGram = spotCostPerGram.toFixed(2);
    const formattedFutureCostPerGram = futureCostPerGram.toFixed(2);

    // 计算现货盈亏和期货盈亏
    let spotPnl = 0;
    let futurePnl = 0;

    // 使用实时计算的当前价格

    // 根据开仓方向计算盈亏
    if (order.direction === 1) {
      // 正向: 现货买跌-期货买涨
      spotPnl = (order.spot_open_price - currentSpotPrice) * order.volume * order.base_weight;
      futurePnl = (currentFuturePrice - order.future_open_price) * order.volume * order.base_weight;
    } else {
      // 反向: 现货买涨-期货买跌
      spotPnl = (currentSpotPrice - order.spot_open_price) * order.volume * order.base_weight;
      futurePnl = (order.future_open_price - currentFuturePrice) * order.volume * order.base_weight;
    }

    // 计算总盈亏
    const totalPnl = spotPnl + futurePnl;

    // 计算实际净利润 = 总盈亏 - 总成本
    const netProfit = totalPnl - totalCost;

    return (
      <View style={styles.pnlSection}>
        <Text style={styles.sectionTitle}>盈亏信息</Text>

        <View style={styles.pnlRowFourColumns}>
          <View style={styles.pnlQuarterColumn}>
            <Text style={styles.pnlItemLabel}>现货盈亏:</Text>
            <Text style={[
              styles.pnlItemValue,
              { color: getPnlType(spotPnl) === 'profit' ? Colors.profit : Colors.loss }
            ]}>
              {formatAmount(spotPnl)}
            </Text>
          </View>

          <View style={styles.pnlQuarterColumn}>
            <Text style={styles.pnlItemLabel}>期货盈亏:</Text>
            <Text style={[
              styles.pnlItemValue,
              { color: getPnlType(futurePnl) === 'profit' ? Colors.profit : Colors.loss }
            ]}>
              {formatAmount(futurePnl)}
            </Text>
          </View>

          <View style={styles.pnlQuarterColumn}>
            <Text style={styles.pnlItemLabel}>总盈亏:</Text>
            <Text style={[
              styles.pnlItemValue,
              { color: getPnlType(totalPnl) === 'profit' ? Colors.profit : Colors.loss }
            ]}>
              {formatAmount(totalPnl)}
            </Text>
          </View>

          <View style={styles.pnlQuarterColumn}>
            <Text style={styles.pnlItemLabel}>总成本:</Text>
            <Text style={styles.pnlItemValue}>
              {formattedTotalCostPerGram}元/克 ({formatAmount(totalCost)})
            </Text>
          </View>
        </View>

        <View style={styles.pnlTotalRow}>
          <Text style={styles.pnlTotalLabel}>净利润: </Text>
          <Text style={[
            styles.pnlTotalValue,
            { color: getPnlType(netProfit) === 'profit' ? Colors.profit : Colors.loss }
          ]}>
            {formatAmount(netProfit)}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.card}>
      {/* 卡片头部 */}
      <View style={styles.header}>
        <View>
          <Text style={styles.orderId}>单号: {order.id}</Text>
          <Text style={styles.openTime}>
            开仓时间: {formatDateTime(order.open_time)}
          </Text>
        </View>
        <View style={styles.directionContainer}>
          <Text style={[
            styles.direction,
            {
              backgroundColor: order.direction === 1 ? Colors.profit : Colors.loss,
              color: Colors.cardBackground
            }
          ]}>
            {getDirectionText(order.direction)}
          </Text>
        </View>
      </View>

      <View style={styles.divider} />

      {/* 价格部分 */}
      <View style={styles.pricesContainer}>
        {renderOpenPrices()}
        {renderCurrentPrices()}
      </View>

      {/* 目标基差部分 */}
      {renderTargetBasis()}

      <View style={styles.divider} />

      {/* 盈亏信息 */}
      {renderPnlInfo()}

      {/* 平仓按钮 */}
      <TouchableOpacity
        style={styles.closeButton}
        onPress={() => onClose(order.id)}
      >
        <Text style={styles.closeButtonText}>平仓</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    ...CommonStyles.flatCard,
    marginVertical: 4,
    padding: 10,
    paddingVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  orderId: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: Colors.text,
  },
  openTime: {
    marginTop: 1,
    fontSize: 10,
    fontWeight: 'normal' as const,
    color: Colors.textSecondary,
  },
  directionContainer: {
    alignItems: 'flex-end',
  },
  direction: {
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    fontSize: 11,
    fontWeight: '600' as const,
    color: Colors.cardBackground,
  },
  divider: {
    ...CommonStyles.divider,
    marginVertical: 6,
    height: 1,
  },
  pricesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priceSection: {
    flex: 1,
    paddingHorizontal: 2,
  },
  pnlSection: {
    marginTop: 4,
  },
  pnlRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  pnlRowFourColumns: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
    flexWrap: 'wrap',
    paddingHorizontal: 2,
  },
  pnlHalfColumn: {
    width: '48%',
  },
  pnlQuarterColumn: {
    width: '23%',
    marginRight: 2,
  },
  pnlItemLabel: {
    color: Colors.textSecondary,
    fontSize: 10,
    marginBottom: 1,
    fontWeight: 'normal' as const,
  },
  pnlItemValue: {
    fontWeight: '500' as const,
    fontSize: 11,
    color: Colors.text,
  },
  pnlTotalRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 4,
    paddingTop: 4,
    borderTopWidth: 1,
    borderTopColor: Colors.divider,
    paddingRight: 4,
  },
  pnlTotalLabel: {
    color: Colors.textSecondary,
    fontSize: 12,
    fontWeight: '600' as const,
  },
  pnlTotalValue: {
    fontWeight: '700' as const,
    fontSize: 13,
    color: Colors.text,
  },
  sectionTitle: {
    fontWeight: '600' as const,
    marginBottom: 4,
    fontSize: 13,
    color: Colors.text,
  },
  row: {
    flexDirection: 'row',
    paddingVertical: 1,
    alignItems: 'center',
  },
  compactRow: {
    paddingVertical: 0,
    marginVertical: 1,
  },
  priceLabel: {
    color: Colors.textSecondary,
    fontSize: 12,
    fontWeight: 'normal' as const,
  },
  priceValue: {
    fontWeight: '500' as const,
    fontSize: 12,
    color: Colors.text,
  },
  pnlLabel: {
    color: Colors.textSecondary,
    fontSize: 12,
    fontWeight: 'normal' as const,
  },
  pnlValue: {
    fontWeight: '500' as const,
    fontSize: 12,
    color: Colors.text,
  },
  pnlTotal: {
    fontWeight: '700' as const,
    fontSize: 13,
    color: Colors.text,
  },
  closeButton: {
    ...ButtonStyles.danger,
    marginTop: 6,
    paddingVertical: 6,
  },
  closeButtonText: {
    ...ButtonStyles.buttonText,
    ...ButtonStyles.dangerText,
    fontSize: 13,
  },
  targetBasisSection: {
    marginVertical: 4,
    paddingHorizontal: 2,
    backgroundColor: Colors.backgroundLight,
    borderRadius: 4,
    paddingVertical: 3,
  },
  targetBasisLabel: {
    color: Colors.textSecondary,
    fontSize: 12,
    fontWeight: '600' as const,
  },
  targetBasisValue: {
    fontWeight: '600' as const,
    fontSize: 12,
    color: Colors.primary,
  },
});

export default PositionCard;