import axios, { AxiosResponse } from 'axios';
import {
  PositionsResponse,
  HistoryOrdersResponse,
  TradeResponse,
  OpenPositionRequest,
  ApiResponse,
  Order
} from '../types/api';
import notificationManager from '../utils/notificationManager';
import { apiClient } from './axiosConfig';
import { handleApiResponse, handleApiError } from '../utils/apiUtils';
import store from '../redux/store';
import { openPositionSuccess, fetchPositionsSuccess } from '../redux/ordersSlice';
import tokenManager from '../utils/tokenManager';
import logger, { LogType } from '../utils/logger';

/**
 * 获取当前持仓列表
 * @param sortBy 排序字段
 * @param sortOrder 排序方向
 * @param filter 筛选条件
 */
export const getPositions = async (
  sortBy: string = 'open_time',
  sortOrder: 'asc' | 'desc' = 'desc',
  filter?: { direction?: 1 | -1, minPnl?: number, maxPnl?: number }
): Promise<PositionsResponse> => {
  try {
    // 构建查询参数
    const params: Record<string, any> = {
      sort_by: sortBy,
      sort_order: sortOrder
    };

    // 添加筛选条件
    if (filter) {
      if (filter.direction !== undefined) {
        params.direction = filter.direction;
      }
      if (filter.minPnl !== undefined) {
        params.min_pnl = filter.minPnl;
      }
      if (filter.maxPnl !== undefined) {
        params.max_pnl = filter.maxPnl;
      }
    }

    // 发送API请求
    const response = await apiClient.get<ApiResponse<PositionsResponse>>('/orders/positions', { params });

    let result: PositionsResponse;
    // 检查handleApiResponse函数是否存在
    if (typeof handleApiResponse !== 'function') {
      console.error('handleApiResponse不是一个函数，可能未正确导入');
      // 如果handleApiResponse不可用，尝试直接使用响应数据
      if (response.data && typeof response.data === 'object') {
        if ('data' in response.data && response.data.data) {
          // 如果响应数据包含data字段，直接使用
          result = response.data.data as PositionsResponse;
        } else {
          // 否则构造一个默认的响应
          result = {
            positions: [],
            total_pnl: 0,
            count: 0
          } as PositionsResponse;
        }
      } else {
        // 如果响应数据无效，构造一个默认的响应
        result = {
          positions: [],
          total_pnl: 0,
          count: 0
        } as PositionsResponse;
      }
    } else {
      // 正常调用handleApiResponse函数
      result = handleApiResponse(response) as PositionsResponse;
    }

    // 处理返回的数据，确保所有必要的字段都存在
    const positions = result.positions.map((position: Order) => {
      // 确保当前价格信息存在，如果不存在则使用开仓价格
      return {
        ...position,
        current_spot_price: position.current_spot_price || position.spot_open_price,
        current_future_price: position.current_future_price || position.future_open_price,
        current_basis: position.current_basis || position.open_basis,
        // 计算盈亏信息（如果后端未提供）
        spot_pnl: position.spot_pnl || 0,
        future_pnl: position.future_pnl || 0,
        total_pnl: position.total_pnl || 0,
        total_cost: position.total_cost || 0,
        net_profit: position.net_profit || (position.total_pnl ? position.total_pnl - (position.total_cost || 0) : 0)
      };
    });

    return {
      positions,
      count: result.count,
      total_pnl: result.total_pnl
    };
  } catch (error) {
    console.error('获取持仓列表失败:', error);

    // 使用错误处理器处理API错误
    let formattedError: Error;
    try {
      // 检查handleApiError函数是否存在
      if (typeof handleApiError !== 'function') {
        console.error('handleApiError不是一个函数，可能未正确导入');
        // 创建一个默认的错误对象
        formattedError = new Error(error instanceof Error ? error.message : '获取持仓列表失败，请稍后重试');

        // 记录错误到日志系统
        logger.error(LogType.TRADE, 'handleApiError不是一个函数(获取持仓)', {
          error: error instanceof Error ? {
            name: error.name,
            message: error.message,
            stack: error.stack
          } : error,
          timestamp: new Date().toISOString()
        });
      } else {
        // 正常调用handleApiError函数
        formattedError = handleApiError(error, '获取持仓列表失败');
      }
    } catch (handlerError) {
      console.error('错误处理器处理获取持仓列表错误失败:', handlerError);
      // 如果错误处理器失败，创建一个新的错误对象
      formattedError = new Error(error instanceof Error ? error.message : '获取持仓列表失败，请稍后重试');
    }

    throw formattedError;
  }
};

/**
 * 获取历史订单列表
 * @param page 页码
 * @param pageSize 每页数量
 * @param sortBy 排序字段
 * @param sortOrder 排序方向
 * @param filter 筛选条件
 */
export const getHistoryOrders = async (
  page: number = 1,
  pageSize: number = 10,
  sortBy: string = 'close_time',
  sortOrder: 'asc' | 'desc' = 'desc',
  filter?: {
    direction?: 1 | -1,
    status?: 'closed' | 'cancelled' | 'error',
    startDate?: string,
    endDate?: string,
    minProfit?: number,
    maxProfit?: number,
    search?: string
  }
): Promise<HistoryOrdersResponse> => {
  console.log('ordersApi.getHistoryOrders - 开始请求历史订单数据', {
    page, pageSize, sortBy, sortOrder, filter
  });

  try {
    // 构建查询参数
    const params: Record<string, any> = {
      page,
      page_size: pageSize,
      sort_by: sortBy,
      sort_order: sortOrder
    };

    // 添加筛选条件
    if (filter) {
      if (filter.direction !== undefined) {
        params.direction = filter.direction;
      }
      if (filter.status) {
        params.status = filter.status;
      }
      if (filter.startDate) {
        params.start_date = filter.startDate;
      }
      if (filter.endDate) {
        params.end_date = filter.endDate;
      }
      if (filter.minProfit !== undefined) {
        params.min_profit = filter.minProfit;
      }
      if (filter.maxProfit !== undefined) {
        params.max_profit = filter.maxProfit;
      }
      if (filter.search) {
        params.search = filter.search;
      }
    }

    console.log('ordersApi.getHistoryOrders - 请求参数:', params);
    console.log('ordersApi.getHistoryOrders - API URL:', `${apiClient.defaults.baseURL}/orders/history`);

    // 检查认证状态
    const token = await tokenManager.getAccessToken();
    console.log('ordersApi.getHistoryOrders - 当前认证状态:', token ? '已认证' : '未认证');

    // 发送API请求
    console.log('ordersApi.getHistoryOrders - 开始发送请求');
    const response = await apiClient.get<ApiResponse<HistoryOrdersResponse>>('/orders/history', { params });
    console.log('ordersApi.getHistoryOrders - 请求成功, 状态码:', response.status);

    let result: HistoryOrdersResponse;
    // 检查handleApiResponse函数是否存在
    if (typeof handleApiResponse !== 'function') {
      console.error('handleApiResponse不是一个函数，可能未正确导入');
      // 如果handleApiResponse不可用，尝试直接使用响应数据
      if (response.data && typeof response.data === 'object') {
        if ('data' in response.data && response.data.data) {
          // 如果响应数据包含data字段，直接使用
          result = response.data.data as HistoryOrdersResponse;
        } else {
          // 否则构造一个默认的响应
          result = {
            orders: [],
            total_count: 0,
            page: page,
            page_size: pageSize
          } as HistoryOrdersResponse;
        }
      } else {
        // 如果响应数据无效，构造一个默认的响应
        result = {
          orders: [],
          total_count: 0,
          page: page,
          page_size: pageSize
        } as HistoryOrdersResponse;
      }
    } else {
      // 正常调用handleApiResponse函数
      result = handleApiResponse(response) as HistoryOrdersResponse;
    }
    console.log('ordersApi.getHistoryOrders - 响应处理成功');

    // 处理返回的数据，确保所有必要的字段都存在
    console.log('ordersApi.getHistoryOrders - 开始处理订单数据, 订单数量:', result.orders.length);
    const orders = result.orders.map((order: Order) => {
      // 计算净利润（如果后端未提供）
      const netProfit = order.net_profit !== undefined
        ? order.net_profit
        : (order.total_pnl || 0) - (order.total_cost || 0);

      return {
        ...order,
        // 确保所有必要字段都有值
        spot_pnl: order.spot_pnl || 0,
        future_pnl: order.future_pnl || 0,
        total_pnl: order.total_pnl || 0,
        total_cost: order.total_cost || 0,
        net_profit: netProfit
      };
    });

    console.log('ordersApi.getHistoryOrders - 数据处理完成, 返回结果:', {
      totalCount: result.total_count,
      ordersCount: orders.length,
      page: result.page,
      pageSize: result.page_size
    });

    return {
      orders,
      total_count: result.total_count,
      page: result.page,
      page_size: result.page_size
    };
  } catch (error) {
    console.error('ordersApi.getHistoryOrders - 请求异常:', error);

    // 记录详细错误信息
    if (error instanceof Error) {
      console.error('错误详情:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });

      // 如果是 Axios 错误，记录更多信息
      if (axios.isAxiosError(error) && error.response) {
        console.error('Axios 错误详情:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers
        });
      }
    }

    // 使用错误处理器处理API错误
    let formattedError: Error;
    try {
      // 检查handleApiError函数是否存在
      if (typeof handleApiError !== 'function') {
        console.error('handleApiError不是一个函数，可能未正确导入');
        // 创建一个默认的错误对象
        formattedError = new Error(error instanceof Error ? error.message : '获取历史订单列表失败，请稍后重试');

        // 记录错误到日志系统
        logger.error(LogType.TRADE, 'handleApiError不是一个函数(获取历史订单)', {
          error: error instanceof Error ? {
            name: error.name,
            message: error.message,
            stack: error.stack
          } : error,
          timestamp: new Date().toISOString()
        });
      } else {
        // 正常调用handleApiError函数
        formattedError = handleApiError(error, '获取历史订单列表失败');
      }
    } catch (handlerError) {
      console.error('错误处理器处理获取历史订单列表错误失败:', handlerError);
      // 如果错误处理器失败，创建一个新的错误对象
      formattedError = new Error(error instanceof Error ? error.message : '获取历史订单列表失败，请稍后重试');
    }

    throw formattedError;
  }
};

/**
 * 开仓操作
 * @param direction 交易方向 1:正向 -1:反向
 * @param spotPrice 现货价格
 * @param futurePrice 期货价格
 * @param volume 手数
 * @param baseWeight 基础克重(g/手)
 * @param targetBasis 目标基差
 */
export const openPosition = async (
  direction: 1 | -1,
  spotPrice: number,
  futurePrice: number,
  volume: number = 1,
  baseWeight: number = 1000,
  targetBasis?: number
): Promise<TradeResponse> => {
  // 参数验证
  if (!direction || (direction !== 1 && direction !== -1)) {
    throw new Error('交易方向无效，必须为1(正向)或-1(反向)');
  }

  if (spotPrice <= 0 || futurePrice <= 0) {
    throw new Error('价格必须大于0');
  }

  if (volume <= 0 || !Number.isInteger(volume)) {
    throw new Error('交易手数必须为正整数');
  }

  if (baseWeight <= 0) {
    throw new Error('基础克重必须大于0');
  }

  try {
    // 禁用开仓请求准备日志
    // console.log('===== 开始准备开仓请求 =====');

    // 记录开仓请求到日志系统
    logger.info(LogType.TRADE, '开始准备开仓请求', {
      direction,
      spotPrice,
      futurePrice,
      volume,
      baseWeight,
      targetBasis
    });

    // 从Redux获取用户ID
    if (!store || typeof store.getState !== 'function') {
      throw new Error('Redux store未初始化，无法获取用户ID');
    }

    const state = store.getState();
    const userId = state.auth?.user?.id;

    // 验证用户ID是否存在
    if (!userId) {
      throw new Error('用户未登录或用户ID不可用');
    }

    // 计算开仓基差 - 修正基差计算公式
    // 注意：这里的spotPrice和futurePrice应该是对应交易方向的价格
    // 正向套利：卖出现货(bid价) + 买入期货(ask价) => 基差 = spot_bid - future_ask
    // 反向套利：买入现货(ask价) + 卖出期货(bid价) => 基差 = spot_ask - future_bid
    // 但这里传入的是单一价格，所以按照交易方向计算
    const openBasis = direction === 1
      ? spotPrice - futurePrice  // 正向基差：现货价格 - 期货价格
      : spotPrice - futurePrice; // 反向基差：现货价格 - 期货价格（保持一致的计算方式）

    // 计算交易成本
    const spotCost = 0.4 * baseWeight * volume / 100; // 现货交易成本
    const futureCost = 0.01 * baseWeight * volume / 100; // 期货交易成本
    const totalCost = spotCost + futureCost;

    // 计算目标基差
    const calculatedTargetBasis = targetBasis !== undefined
      ? targetBasis
      : openBasis + (direction === 1 ? 0.5 : -0.5);

    // 构建请求数据
    const openPositionData = {
      direction,
      spot_price: spotPrice,
      future_price: futurePrice,
      volume,
      base_weight: baseWeight,
      target_basis: calculatedTargetBasis,
      open_basis: openBasis,
      spot_cost: spotCost,
      future_cost: futureCost,
      total_cost: totalCost,
      user_id: userId
    };

    // 发送API请求
    const response = await apiClient.post<ApiResponse<TradeResponse>>(
      `/trade/open`,
      openPositionData
    );

    // 处理响应
    let result: TradeResponse;

    try {
      result = handleApiResponse<TradeResponse>(response);
    } catch (error) {
      // 如果handleApiResponse处理失败，尝试直接使用响应数据
      if (response.data && typeof response.data === 'object' && 'success' in response.data) {
        result = response.data as TradeResponse;
      } else {
        throw error;
      }
    }

    // 开仓成功后的处理
    if (result.success && result.order_id) {
      // 构建临时订单对象
      const newOrder = {
        id: result.order_id,
        user_id: userId,
        direction,
        status: 'open' as const,  // 显式指定status类型为字面量类型
        volume,
        base_weight: baseWeight,
        spot_open_price: spotPrice,
        future_open_price: futurePrice,
        open_basis: openBasis,
        target_basis: calculatedTargetBasis,
        open_time: new Date().toISOString(),
        spot_cost: spotCost,
        future_cost: futureCost,
        total_cost: totalCost,
        current_spot_price: spotPrice,    // 初始时当前价格等于开仓价格
        current_future_price: futurePrice, // 初始时当前价格等于开仓价格
        current_basis: openBasis,         // 初始时当前基差等于开仓基差
        spot_pnl: 0,                      // 初始化盈亏为0
        future_pnl: 0,
        total_pnl: 0,
        net_profit: 0
      };

      // 更新Redux状态
      store.dispatch(openPositionSuccess(newOrder));

      // 刷新持仓列表
      try {
        const positionsResponse = await getPositions();
        if (positionsResponse?.positions) {
          store.dispatch(fetchPositionsSuccess({
            positions: positionsResponse.positions,
            totalPnl: positionsResponse.total_pnl
          }));
        }
      } catch (refreshError) {
        logger.error(LogType.TRADE, '刷新持仓列表失败', {
          error: refreshError instanceof Error ? refreshError : new Error('未知错误')
        });
        // 继续执行，不影响主流程
      }
    }

    return result;
  } catch (error) {
    // 记录错误
    logger.error(LogType.TRADE, '开仓操作失败', {
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error
    });

    // 使用错误处理器处理API错误
    const formattedError = handleApiError(error, '开仓操作失败');

    // 显示错误通知
    notificationManager.error('开仓失败', formattedError.message);

    throw formattedError;
  }
};

/**
 * 平仓操作
 * @param orderId 订单ID
 * @param currentOrder 当前订单信息，用于计算盈亏
 */
export const closePosition = async (orderId: string, currentOrder?: Order): Promise<TradeResponse> => {
  // 参数验证
  if (!orderId || orderId.trim() === '') {
    throw new Error('订单ID不能为空');
  }

  try {
    // 发送API请求
    console.log(`发送平仓请求: /trade/close/${orderId}`);
    const response = await apiClient.post<ApiResponse<TradeResponse>>(`/trade/close/${orderId}`);

    // 处理响应
    let result: TradeResponse;
    try {
      // 首先检查响应是否直接包含 success 字段
      if (response.data && typeof response.data === 'object' && 'success' in response.data) {
        // 如果响应直接包含 success 字段，直接使用响应数据
        result = response.data as TradeResponse;
        console.log('使用原始响应数据(平仓):', result);
      } else {
        // 否则尝试使用 handleApiResponse 处理
        if (typeof handleApiResponse !== 'function') {
          console.error('handleApiResponse不是一个函数，可能未正确导入');
          // 如果handleApiResponse不可用，尝试直接使用响应数据
          if (response.data) {
            // 尝试从响应数据中提取close_time和order_id
            let closeTime = new Date().toISOString();
            // 使用传入的orderId参数，这是平仓操作的参数

            if (typeof response.data === 'object' && response.data !== null) {
              // 如果响应数据是对象，尝试从data字段中提取close_time
              if ('data' in response.data &&
                  typeof response.data.data === 'object' &&
                  response.data.data !== null) {

                if ('close_time' in response.data.data) {
                  closeTime = response.data.data.close_time;
                }

                if ('order_id' in response.data.data) {
                  orderId = response.data.data.order_id;
                }
              }
            }

            result = {
              success: true,
              message: '平仓请求成功',
              close_time: closeTime,
              order_id: orderId // 添加必需的order_id字段
            } as TradeResponse;
          } else {
            throw new Error('无法处理响应数据');
          }
        } else {
          result = handleApiResponse(response) as TradeResponse;
          console.log('处理后的平仓响应:', result);
        }
      }
    } catch (error) {
      console.error('处理平仓响应时出错:', error);
      // 如果处理失败，尝试直接使用响应数据
      if (response.data && typeof response.data === 'object' && 'success' in response.data) {
        result = response.data as TradeResponse;
        console.log('使用原始响应数据(平仓):', result);
      } else {
        throw error;
      }
    }

    // 平仓成功后，更新Redux状态
    if (result.success) {
      // 如果有当前订单信息，构建一个已平仓的订单对象
      if (currentOrder) {
        const closedOrder: Order = {
          ...currentOrder,
          status: 'closed',
          close_time: result.close_time || new Date().toISOString(),
          // 如果有当前价格信息，使用它们作为平仓价格
          spot_close_price: currentOrder.current_spot_price,
          future_close_price: currentOrder.current_future_price,
          close_basis: currentOrder.current_basis
        };

        // 更新Redux状态
        // 检查store是否存在且有dispatch方法
        if (!store || typeof store.dispatch !== 'function') {
          console.error('Redux store未初始化或dispatch方法不可用');
          console.warn('无法更新Redux状态，但不影响主流程');
        } else {
          store.dispatch({
            type: 'orders/closePositionSuccess',
            payload: {
              orderId,
              closedOrder
            }
          });
        }
      }

      // 刷新持仓列表
      try {
        // 检查getPositions函数是否存在
        if (typeof getPositions !== 'function') {
          console.error('getPositions不是一个函数，可能未正确定义');
          logger.error(LogType.TRADE, 'getPositions不是一个函数(平仓)', {
            timestamp: new Date().toISOString()
          });
        } else {
          // 直接调用getPositions函数来刷新持仓数据
          getPositions().then(positionsResponse => {
            if (positionsResponse && positionsResponse.positions) {
              // 更新Redux状态
              // 检查store是否存在且有dispatch方法
              if (!store || typeof store.dispatch !== 'function') {
                console.error('Redux store未初始化或dispatch方法不可用');
                console.warn('无法更新Redux状态，但不影响主流程');
              } else {
                // 检查fetchPositionsSuccess是否是一个函数
                if (typeof fetchPositionsSuccess !== 'function') {
                  console.error('fetchPositionsSuccess不是一个函数，可能未正确导入');
                  console.warn('无法更新Redux状态，但不影响主流程');
                } else {
                  store.dispatch(fetchPositionsSuccess({
                    positions: positionsResponse.positions,
                    totalPnl: positionsResponse.total_pnl
                  }));
                }
              }
              console.log(`平仓后持仓列表刷新完成，获取到 ${positionsResponse.positions.length} 个持仓`);
            } else {
              console.warn('平仓后刷新持仓列表成功，但返回数据为空');
            }
          }).catch(error => {
            console.error('平仓后刷新持仓列表失败:', error);
            logger.error(LogType.TRADE, '平仓后刷新持仓列表失败', {
              error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
              } : error
            });
          });
          console.log('平仓后持仓列表刷新已启动');
        }
      } catch (refreshError) {
        console.error('启动平仓后刷新持仓列表失败:', refreshError);
        logger.error(LogType.TRADE, '启动平仓后刷新持仓列表失败', {
          error: refreshError instanceof Error ? {
            name: refreshError.name,
            message: refreshError.message,
            stack: refreshError.stack
          } : refreshError
        });
        // 继续执行，不影响主流程
      }

      // 显示成功通知
      notificationManager.success('平仓成功', `订单 ${orderId} 已成功平仓`);
    }

    return result;
  } catch (error) {
    console.error('平仓操作失败:', error);

    // 显示错误通知
    notificationManager.error('平仓失败', error instanceof Error ? error.message : '请稍后重试');

    // 使用错误处理器处理API错误
    let formattedError: Error;
    try {
      // 检查handleApiError函数是否存在
      if (typeof handleApiError !== 'function') {
        console.error('handleApiError不是一个函数，可能未正确导入');
        // 创建一个默认的错误对象
        formattedError = new Error(error instanceof Error ? error.message : '平仓操作失败，请稍后重试');

        // 记录错误到日志系统
        logger.error(LogType.TRADE, 'handleApiError不是一个函数(平仓)', {
          error: error instanceof Error ? {
            name: error.name,
            message: error.message,
            stack: error.stack
          } : error,
          timestamp: new Date().toISOString()
        });
      } else {
        // 正常调用handleApiError函数
        formattedError = handleApiError(error, '平仓操作失败');
      }
    } catch (handlerError) {
      console.error('错误处理器处理平仓错误失败:', handlerError);
      // 如果错误处理器失败，创建一个新的错误对象
      formattedError = new Error(error instanceof Error ? error.message : '平仓操作失败，请稍后重试');

      // 记录错误处理器失败的情况
      logger.error(LogType.TRADE, '平仓错误处理器失败', {
        originalError: error instanceof Error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : error,
        handlerError: handlerError instanceof Error ? {
          name: handlerError.name,
          message: handlerError.message,
          stack: handlerError.stack
        } : handlerError,
        timestamp: new Date().toISOString()
      });
    }

    throw formattedError;
  }
};

/**
 * 刷新持仓列表
 * 用于在开仓或平仓后更新持仓数据
 * @param retryCount 重试次数，默认为3
 * @param retryDelay 重试延迟(毫秒)，默认为500ms
 */
export const refreshPositions = async (retryCount: number = 3, retryDelay: number = 500): Promise<void> => {
  let attempts = 0;
  let lastError: any = null;

  while (attempts < retryCount) {
    try {
      attempts++;
      console.log(`刷新持仓列表，尝试 ${attempts}/${retryCount}`);

      const response = await apiClient.get<ApiResponse<PositionsResponse>>('/orders/positions');

      let result: PositionsResponse;
      // 检查handleApiResponse函数是否存在
      if (typeof handleApiResponse !== 'function') {
        console.error('handleApiResponse不是一个函数，可能未正确导入');
        // 如果handleApiResponse不可用，尝试直接使用响应数据
        if (response.data && typeof response.data === 'object') {
          if ('data' in response.data && response.data.data) {
            // 如果响应数据包含data字段，直接使用
            result = response.data.data as PositionsResponse;
          } else {
            // 否则构造一个默认的响应
            result = {
              positions: [],
              total_pnl: 0,
              count: 0
            } as PositionsResponse;
          }
        } else {
          // 如果响应数据无效，构造一个默认的响应
          result = {
            positions: [],
            total_pnl: 0,
            count: 0
          } as PositionsResponse;
        }
      } else {
        // 正常调用handleApiResponse函数
        result = handleApiResponse(response) as PositionsResponse;
      }

      // 更新Redux状态
      // 检查store是否存在且有dispatch方法
      if (!store || typeof store.dispatch !== 'function') {
        console.error('Redux store未初始化或dispatch方法不可用');
        console.warn('无法更新Redux状态，但不影响主流程');
      } else {
        // 检查fetchPositionsSuccess是否是一个函数
        if (typeof fetchPositionsSuccess !== 'function') {
          console.error('fetchPositionsSuccess不是一个函数，可能未正确导入');
          console.warn('无法更新Redux状态，但不影响主流程');
        } else {
          store.dispatch(fetchPositionsSuccess({
            positions: result.positions,
            totalPnl: result.total_pnl
          }));
        }
      }

      console.log(`刷新持仓列表成功，获取到 ${result.positions.length} 个持仓`);
      return; // 成功，退出函数
    } catch (error) {
      lastError = error;
      console.error(`刷新持仓列表失败 (尝试 ${attempts}/${retryCount}):`, error);

      if (attempts < retryCount) {
        // 等待一段时间后重试
        console.log(`将在 ${retryDelay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        // 每次重试增加延迟时间
        retryDelay *= 1.5;
      }
    }
  }

  // 所有重试都失败
  console.error(`刷新持仓列表失败，已尝试 ${retryCount} 次:`, lastError);
  // 这里不抛出错误，因为这是一个后台刷新操作
};