2025-05-30 14:16:26 - test_default_settlement - INFO - 开始测试违约结算按钮功能...
2025-05-30 14:16:26 - test_default_settlement - INFO - 设置测试环境...
2025-05-30 14:16:48 - test_default_settlement - INFO - ✅ 测试环境设置成功
2025-05-30 14:16:48 - test_default_settlement - INFO - ============================================================
2025-05-30 14:16:48 - test_default_settlement - INFO - 测试查找所有有违约结算按钮的订单
2025-05-30 14:16:51 - test_default_settlement - INFO - 找到 2 个订单容器
2025-05-30 14:16:51 - test_default_settlement - INFO - ✅ 订单 B2025053014024825524 有违约结算按钮 (策略: 2 种)
2025-05-30 14:16:51 - test_default_settlement - INFO - ✅ 订单 S2025053002173230988 有违约结算按钮 (策略: 5 种)
2025-05-30 14:16:51 - test_default_settlement - INFO - 📊 结果统计:
2025-05-30 14:16:51 - test_default_settlement - INFO -    总订单数: 2
2025-05-30 14:16:51 - test_default_settlement - INFO -    有违约结算按钮: 2
2025-05-30 14:16:51 - test_default_settlement - INFO - 测试违约结算按钮可用性检查
2025-05-30 14:16:51 - test_default_settlement - INFO - 检查订单 B2025053014024825524 的违约结算按钮可用性...
2025-05-30 14:16:54 - test_default_settlement - INFO - ✅ 订单 B2025053014024825524: 违约结算按钮可用
2025-05-30 14:16:54 - test_default_settlement - INFO -    可用策略: ['text_based', 'fallback_text']
2025-05-30 14:16:54 - test_default_settlement - INFO - 检查订单 S2025053002173230988 的违约结算按钮可用性...
2025-05-30 14:16:57 - test_default_settlement - INFO - ✅ 订单 S2025053002173230988: 违约结算按钮可用
2025-05-30 14:16:57 - test_default_settlement - INFO -    可用策略: ['primary', 'with_data_v', 'full_path', 'text_based', 'class_text_combo', 'in_button_container', 'fallback_text']
2025-05-30 14:16:57 - test_default_settlement - INFO - 📊 违约结算按钮可用性检查成功率: 100.0% (2/2)
2025-05-30 14:16:57 - test_default_settlement - INFO - 测试违约结算按钮点击模拟
2025-05-30 14:16:57 - test_default_settlement - INFO - 模拟点击订单 B2025053014024825524 的违约结算按钮...
2025-05-30 14:17:00 - test_default_settlement - INFO - ✅ 使用 text_based 策略找到违约结算按钮
2025-05-30 14:17:00 - test_default_settlement - INFO - 📋 违约结算按钮属性:
2025-05-30 14:17:00 - test_default_settlement - INFO -    文本: 违约结算
2025-05-30 14:17:00 - test_default_settlement - INFO -    可见: True
2025-05-30 14:17:00 - test_default_settlement - INFO -    可用: True
2025-05-30 14:17:00 - test_default_settlement - INFO - 🎉 违约结算按钮验证成功: 订单 B2025053014024825524
2025-05-30 14:17:00 - test_default_settlement - INFO - ================================================================================
2025-05-30 14:17:00 - test_default_settlement - INFO - 违约结算按钮测试报告
2025-05-30 14:17:00 - test_default_settlement - INFO - 📊 测试结果摘要:
2025-05-30 14:17:00 - test_default_settlement - INFO -    有违约结算按钮的订单: 2
2025-05-30 14:17:00 - test_default_settlement - INFO -    违约结算按钮覆盖率: 100.0%
2025-05-30 14:17:00 - test_default_settlement - INFO -    按钮可用性检查成功率: 100.0%
2025-05-30 14:17:00 - test_default_settlement - INFO -    按钮点击模拟: ✅ 成功
2025-05-30 14:17:00 - test_default_settlement - INFO - 
📋 有违约结算按钮的订单详情:
2025-05-30 14:17:00 - test_default_settlement - INFO -    订单 B2025053014024825524: 2 种策略可用
2025-05-30 14:17:00 - test_default_settlement - INFO -      - text_based
2025-05-30 14:17:00 - test_default_settlement - INFO -      - fallback_text
2025-05-30 14:17:00 - test_default_settlement - INFO -    订单 S2025053002173230988: 5 种策略可用
2025-05-30 14:17:00 - test_default_settlement - INFO -      - full_path
2025-05-30 14:17:00 - test_default_settlement - INFO -      - class_text_combo
2025-05-30 14:17:00 - test_default_settlement - INFO -      - in_button_container
2025-05-30 14:17:00 - test_default_settlement - INFO - 
🎉 违约结算按钮测试成功！
2025-05-30 14:17:00 - test_default_settlement - INFO - ✅ 能够100%找到违约结算按钮
2025-05-30 14:17:00 - test_default_settlement - INFO - ✅ 按钮可用性检查100%成功
2025-05-30 14:17:00 - test_default_settlement - INFO - ✅ 按钮点击模拟成功
