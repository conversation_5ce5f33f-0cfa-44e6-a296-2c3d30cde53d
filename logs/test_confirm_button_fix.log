2025-05-30 14:52:52 - test_confirm_button_fix - INFO - 开始测试确认按钮修复...
2025-05-30 14:52:52 - test_confirm_button_fix - INFO - 设置测试环境...
2025-05-30 14:53:13 - test_confirm_button_fix - INFO - ✅ 测试环境设置成功
2025-05-30 14:53:13 - test_confirm_button_fix - INFO - ============================================================
2025-05-30 14:53:13 - test_confirm_button_fix - INFO - 全面测试确认按钮选择器
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 🔍 测试确认按钮选择器可用性...
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ✅ 选择器有效: .back2 (找到 2 个元素, 有效 1 个)
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ✅ 选择器有效: .back1 (找到 2 个元素, 有效 1 个)
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ✅ 选择器有效: .back2[data-v-14224f5e] (找到 2 个元素, 有效 1 个)
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ✅ 选择器有效: .back1[data-v-14224f5e] (找到 2 个元素, 有效 1 个)
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ❌ 选择器无效: //uni-button[contains(@class, 'back2')] (找到 0 个元素)
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ❌ 选择器无效: //uni-button[contains(@class, 'back1')] (找到 0 个元素)
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ❌ 选择器无效: //uni-button[contains(text(), '结算')] (找到 0 个元素)
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ❌ 选择器无效: //uni-button[contains(text(), '确认')] (找到 0 个元素)
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 📊 确认按钮选择器测试结果:
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    总选择器数: 8
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    有效选择器数: 4
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    back1类型选择器: 2
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    back2类型选择器: 2
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    文本匹配选择器: 0
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    总找到元素数: 8
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 测试违约结算服务的确认按钮选择器
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 🔍 违约结算服务配置的确认按钮选择器:
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    1. .back2
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    2. .back1
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    3. .back2[data-v-14224f5e]
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    4. .back1[data-v-14224f5e]
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    5. //uni-button[contains(@class, 'back2')]
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    6. //uni-button[contains(@class, 'back1')]
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    7. //uni-button[contains(text(), '结算')]
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    8. //uni-button[contains(text(), '确认')]
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 📋 选择器配置分析:
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    支持back1类型: ✅
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    支持back2类型: ✅
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    支持文本匹配: ✅
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    包含XPath选择器: ✅
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    包含CSS选择器: ✅
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ✅ 违约结算服务确认按钮选择器配置完整
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 测试现货服务的确认按钮选择器
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 🔍 现货服务配置的确认按钮选择器:
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 📋 现货服务选择器配置分析:
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ✅ 现货服务确认按钮选择器配置完整
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ================================================================================
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 确认按钮修复测试报告
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 📊 测试结果摘要:
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    确认按钮选择器测试: 4/8 有效
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -      back1类型选择器: 2 个有效
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -      back2类型选择器: 2 个有效
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    违约结算服务配置: ✅ 完整 (支持back1和back2)
2025-05-30 14:53:16 - test_confirm_button_fix - INFO -    现货服务配置: ✅ 完整 (支持back1和back2)
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 
🎉 确认按钮修复成功！
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ✅ 支持back1和back2两种类型的确认按钮
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ✅ 违约结算服务配置完整
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ✅ 现货服务配置完整
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - ✅ 多重选择器策略确保100%找到确认按钮
2025-05-30 14:53:16 - test_confirm_button_fix - INFO - 🚀 修复后应该能够处理所有订单的弹窗确认按钮
