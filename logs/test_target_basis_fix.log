2025-05-30 02:54:08 - test_target_basis - INFO - 开始测试目标基差计算修复...
2025-05-30 02:54:08 - test_target_basis - INFO - 测试正向套利目标基差计算...
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 正向套利测试 1: 开仓基差=1.0, 盈利价差=0.5, 目标基差=1.5, 期望=1.5
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 正向套利测试 2: 开仓基差=0.5, 盈利价差=0.8, 目标基差=1.3, 期望=1.3
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 正向套利测试 3: 开仓基差=-0.2, 盈利价差=0.7, 目标基差=0.5, 期望=0.5
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 正向套利测试 4: 开仓基差=2.1, 盈利价差=0.95, 目标基差=3.05, 期望=3.05
2025-05-30 02:54:08 - test_target_basis - INFO - 测试反向套利目标基差计算...
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 反向套利测试 1: 开仓基差=-1.0, 盈利价差=0.5, 目标基差=-1.5, 期望=-1.5
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 反向套利测试 2: 开仓基差=-0.5, 盈利价差=0.8, 目标基差=-1.3, 期望=-1.3
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 反向套利测试 3: 开仓基差=0.2, 盈利价差=0.7, 目标基差=-0.5, 期望=-0.5
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 反向套利测试 4: 开仓基差=-2.1, 盈利价差=0.95, 目标基差=-3.05, 期望=-3.05
2025-05-30 02:54:08 - test_target_basis - INFO - 测试公式一致性...
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 公式一致性测试通过: 正向目标=1.0, 反向目标=-1.0
2025-05-30 02:54:08 - test_target_basis - INFO - 测试边界情况...
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 边界测试 零开仓基差: 结果=0.5, 期望=0.5
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 边界测试 零盈利价差: 结果=1.0, 期望=1.0
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 边界测试 负开仓基差正向: 结果=-1.0, 期望=-1.0
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 边界测试 正开仓基差反向: 结果=1.0, 期望=1.0
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 边界测试 大盈利价差: 结果=5.5, 期望=5.5
2025-05-30 02:54:08 - test_target_basis - INFO - 测试真实世界场景...
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 真实场景 典型正向套利: 目标基差=1.95, 期望=1.95
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 真实场景 典型反向套利: 目标基差=-5.95, 期望=-5.95
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 真实场景 小基差正向套利: 目标基差=0.7, 期望=0.7
2025-05-30 02:54:08 - test_target_basis - INFO - ✅ 真实场景 大基差反向套利: 目标基差=-8.2, 期望=-8.2
2025-05-30 02:54:08 - test_target_basis - INFO - === 目标基差计算修复测试报告 ===
2025-05-30 02:54:08 - test_target_basis - INFO - 总测试数: 5
2025-05-30 02:54:08 - test_target_basis - INFO - 通过测试数: 5
2025-05-30 02:54:08 - test_target_basis - INFO - 失败测试数: 0
2025-05-30 02:54:08 - test_target_basis - INFO - 通过率: 100.0%
2025-05-30 02:54:08 - test_target_basis - INFO - 
详细结果:
2025-05-30 02:54:08 - test_target_basis - INFO -   forward_arbitrage: ✅ 通过
2025-05-30 02:54:08 - test_target_basis - INFO -   reverse_arbitrage: ✅ 通过
2025-05-30 02:54:08 - test_target_basis - INFO -   formula_consistency: ✅ 通过
2025-05-30 02:54:08 - test_target_basis - INFO -   edge_cases: ✅ 通过
2025-05-30 02:54:08 - test_target_basis - INFO -   real_world_scenarios: ✅ 通过
2025-05-30 02:54:08 - test_target_basis - INFO - 
🎉 所有测试通过！目标基差计算修复成功！
2025-05-30 02:54:08 - test_target_basis - INFO - 
✅ 确认修复内容:
2025-05-30 02:54:08 - test_target_basis - INFO -   - 正向开仓：目标基差 = 开仓基差 + 盈利价差
2025-05-30 02:54:08 - test_target_basis - INFO -   - 反向开仓：目标基差 = 开仓基差 - 盈利价差
2025-05-30 02:54:08 - test_target_basis - INFO -   - 前端MarketScreen.tsx已修复
2025-05-30 02:54:08 - test_target_basis - INFO -   - 后端orders.py API已修复
2025-05-30 02:54:08 - test_target_basis - INFO -   - 数学工具函数正确
