2025-05-30 13:36:34 - trade_close_api - INFO - [close_position:204] - === 平仓API请求开始 ===
2025-05-30 13:36:34 - trade_close_api - INFO - [close_position:205] - 用户ID: USR337855A0
2025-05-30 13:36:34 - trade_close_api - INFO - [close_position:206] - 订单ID: ORD20250530021730543492
2025-05-30 13:36:34 - trade_close_api - INFO - [close_position:207] - 请求时间: 2025-05-30T13:36:34.468012
2025-05-30 13:36:34 - trade_close_api - INFO - [close_position:216] - 开始执行平仓操作...
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1457] - === 平仓服务操作开始 ===
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1458] - 用户ID: USR337855A0
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1459] - 订单ID: ORD20250530021730543492
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1460] - 开始时间: 2025-05-30T13:36:34.468410
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1485] - 步骤1: 获取订单信息
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1502] - 订单验证成功: 方向=-1, 数量=1, 状态=open
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1503] - 开仓价格: 现货=769.1, 期货=774.1800000000001
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1504] - 开仓基差: -5.080000000000041
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1507] - 步骤2: 获取最新市场数据
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1512] - 当前市场数据: 现货买价=765.19, 现货卖价=765.88
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1513] - 当前市场数据: 期货买价=771.54, 期货卖价=771.65
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1516] - 步骤3: 获取用户账户信息
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1527] - 账户验证成功: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1530] - 步骤4: 计算平仓价格和方向
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1548] - 反向套利平仓: 现货卖出价=765.19, 期货买入价=771.65
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1550] - 平仓基差: -6.459999999999923
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1551] - 现货平仓方向: 卖出
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1552] - 期货平仓方向: 买入
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1555] - 步骤5: 计算盈亏
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1561] - 现货盈亏: -3909.999999999968
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1562] - 期货盈亏: 2530.0000000000864
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1563] - 总盈亏: -1379.9999999998818
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1564] - 净利润: -1789.9999999998818
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1567] - 步骤6: 开始执行平仓交易
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1580] - 步骤6.1: 执行现货平仓交易
2025-05-30 13:36:34 - trade_close_service - INFO - [close_position:1581] - 现货平仓参数: 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:36:53 - trade_close_service - INFO - [close_position:1589] - 现货平仓交易结果: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:36:53 - trade_close_service - ERROR - [close_position:1594] - 现货平仓失败: 执行现货平仓失败，已达到最大重试次数 (3)
2025-05-30 13:36:53 - trade_close_api - ERROR - [close_position:230] - === 平仓操作失败 ===
2025-05-30 13:36:53 - trade_close_api - ERROR - [close_position:231] - 订单ID: ORD20250530021730543492
2025-05-30 13:36:53 - trade_close_api - ERROR - [close_position:232] - 错误信息: 现货平仓交易执行失败: 执行现货平仓失败，已达到最大重试次数 (3)
2025-05-30 13:36:53 - trade_close_api - ERROR - [close_position:233] - 失败步骤: None
2025-05-30 13:36:53 - trade_close_api - INFO - [close_position:237] - === 平仓API请求结束 ===
2025-05-30 13:37:09 - trade_close_api - INFO - [close_position:204] - === 平仓API请求开始 ===
2025-05-30 13:37:09 - trade_close_api - INFO - [close_position:205] - 用户ID: USR337855A0
2025-05-30 13:37:09 - trade_close_api - INFO - [close_position:206] - 订单ID: ORD20250530021730543492
2025-05-30 13:37:09 - trade_close_api - INFO - [close_position:207] - 请求时间: 2025-05-30T13:37:09.009620
2025-05-30 13:37:09 - trade_close_api - INFO - [close_position:216] - 开始执行平仓操作...
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1457] - === 平仓服务操作开始 ===
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1458] - 用户ID: USR337855A0
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1459] - 订单ID: ORD20250530021730543492
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1460] - 开始时间: 2025-05-30T13:37:09.010175
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1485] - 步骤1: 获取订单信息
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1502] - 订单验证成功: 方向=-1, 数量=1, 状态=open
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1503] - 开仓价格: 现货=769.1, 期货=774.1800000000001
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1504] - 开仓基差: -5.080000000000041
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1507] - 步骤2: 获取最新市场数据
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1512] - 当前市场数据: 现货买价=765.15, 现货卖价=765.84
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1513] - 当前市场数据: 期货买价=771.54, 期货卖价=771.65
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1516] - 步骤3: 获取用户账户信息
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1527] - 账户验证成功: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1530] - 步骤4: 计算平仓价格和方向
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1548] - 反向套利平仓: 现货卖出价=765.15, 期货买入价=771.65
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1550] - 平仓基差: -6.5
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1551] - 现货平仓方向: 卖出
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1552] - 期货平仓方向: 买入
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1555] - 步骤5: 计算盈亏
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1561] - 现货盈亏: -3950.0000000000455
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1562] - 期货盈亏: 2530.0000000000864
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1563] - 总盈亏: -1419.999999999959
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1564] - 净利润: -1829.999999999959
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1567] - 步骤6: 开始执行平仓交易
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1580] - 步骤6.1: 执行现货平仓交易
2025-05-30 13:37:09 - trade_close_service - INFO - [close_position:1581] - 现货平仓参数: 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:37:28 - trade_close_service - INFO - [close_position:1589] - 现货平仓交易结果: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:37:28 - trade_close_service - ERROR - [close_position:1594] - 现货平仓失败: 执行现货平仓失败，已达到最大重试次数 (3)
2025-05-30 13:37:28 - trade_close_api - ERROR - [close_position:230] - === 平仓操作失败 ===
2025-05-30 13:37:28 - trade_close_api - ERROR - [close_position:231] - 订单ID: ORD20250530021730543492
2025-05-30 13:37:28 - trade_close_api - ERROR - [close_position:232] - 错误信息: 现货平仓交易执行失败: 执行现货平仓失败，已达到最大重试次数 (3)
2025-05-30 13:37:28 - trade_close_api - ERROR - [close_position:233] - 失败步骤: None
2025-05-30 13:37:28 - trade_close_api - INFO - [close_position:237] - === 平仓API请求结束 ===
2025-05-30 13:37:37 - trade_close_api - INFO - [close_position:204] - === 平仓API请求开始 ===
2025-05-30 13:37:37 - trade_close_api - INFO - [close_position:205] - 用户ID: USR337855A0
2025-05-30 13:37:37 - trade_close_api - INFO - [close_position:206] - 订单ID: ORD20250530021730539173
2025-05-30 13:37:37 - trade_close_api - INFO - [close_position:207] - 请求时间: 2025-05-30T13:37:37.957335
2025-05-30 13:37:37 - trade_close_api - INFO - [close_position:216] - 开始执行平仓操作...
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1457] - === 平仓服务操作开始 ===
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1458] - 用户ID: USR337855A0
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1459] - 订单ID: ORD20250530021730539173
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1460] - 开始时间: 2025-05-30T13:37:37.957919
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1485] - 步骤1: 获取订单信息
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1502] - 订单验证成功: 方向=-1, 数量=1, 状态=open
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1503] - 开仓价格: 现货=769.11, 期货=774.2
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1504] - 开仓基差: -5.090000000000032
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1507] - 步骤2: 获取最新市场数据
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1512] - 当前市场数据: 现货买价=765.33, 现货卖价=766.02
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1513] - 当前市场数据: 期货买价=771.54, 期货卖价=771.65
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1516] - 步骤3: 获取用户账户信息
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1527] - 账户验证成功: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1530] - 步骤4: 计算平仓价格和方向
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1548] - 反向套利平仓: 现货卖出价=765.33, 期货买入价=771.65
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1550] - 平仓基差: -6.319999999999936
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1551] - 现货平仓方向: 卖出
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1552] - 期货平仓方向: 买入
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1555] - 步骤5: 计算盈亏
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1561] - 现货盈亏: -3779.9999999999727
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1562] - 期货盈亏: 2550.000000000068
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1563] - 总盈亏: -1229.9999999999045
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1564] - 净利润: -1234.0999999999044
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1567] - 步骤6: 开始执行平仓交易
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1580] - 步骤6.1: 执行现货平仓交易
2025-05-30 13:37:37 - trade_close_service - INFO - [close_position:1581] - 现货平仓参数: 账户=SPOT337855A0, 订单ID=None
2025-05-30 13:37:57 - trade_close_service - INFO - [close_position:1589] - 现货平仓交易结果: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:37:57 - trade_close_service - ERROR - [close_position:1594] - 现货平仓失败: 执行现货平仓失败，已达到最大重试次数 (3)
2025-05-30 13:37:57 - trade_close_api - ERROR - [close_position:230] - === 平仓操作失败 ===
2025-05-30 13:37:57 - trade_close_api - ERROR - [close_position:231] - 订单ID: ORD20250530021730539173
2025-05-30 13:37:57 - trade_close_api - ERROR - [close_position:232] - 错误信息: 现货平仓交易执行失败: 执行现货平仓失败，已达到最大重试次数 (3)
2025-05-30 13:37:57 - trade_close_api - ERROR - [close_position:233] - 失败步骤: None
2025-05-30 13:37:57 - trade_close_api - INFO - [close_position:237] - === 平仓API请求结束 ===
2025-05-30 13:41:22 - trade_close_api - INFO - [close_position:204] - === 平仓API请求开始 ===
2025-05-30 13:41:22 - trade_close_api - INFO - [close_position:205] - 用户ID: USR337855A0
2025-05-30 13:41:22 - trade_close_api - INFO - [close_position:206] - 订单ID: ORD20250530021730543492
2025-05-30 13:41:22 - trade_close_api - INFO - [close_position:207] - 请求时间: 2025-05-30T13:41:22.523829
2025-05-30 13:41:22 - trade_close_api - INFO - [close_position:216] - 开始执行平仓操作...
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1457] - === 平仓服务操作开始 ===
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1458] - 用户ID: USR337855A0
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1459] - 订单ID: ORD20250530021730543492
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1460] - 开始时间: 2025-05-30T13:41:22.524210
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1485] - 步骤1: 获取订单信息
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1502] - 订单验证成功: 方向=-1, 数量=1, 状态=open
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1503] - 开仓价格: 现货=769.1, 期货=774.1800000000001
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1504] - 开仓基差: -5.080000000000041
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1507] - 步骤2: 获取最新市场数据
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1512] - 当前市场数据: 现货买价=765.41, 现货卖价=766.1
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1513] - 当前市场数据: 期货买价=771.54, 期货卖价=771.65
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1516] - 步骤3: 获取用户账户信息
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1527] - 账户验证成功: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1530] - 步骤4: 计算平仓价格和方向
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1548] - 反向套利平仓: 现货卖出价=765.41, 期货买入价=771.65
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1550] - 平仓基差: -6.240000000000009
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1551] - 现货平仓方向: 卖出
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1552] - 期货平仓方向: 买入
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1555] - 步骤5: 计算盈亏
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1561] - 现货盈亏: -3690.0000000000546
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1562] - 期货盈亏: 2530.0000000000864
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1563] - 总盈亏: -1159.9999999999682
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1564] - 净利润: -1569.9999999999682
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1567] - 步骤6: 开始执行平仓交易
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1580] - 步骤6.1: 执行现货平仓交易
2025-05-30 13:41:22 - trade_close_service - INFO - [close_position:1581] - 现货平仓参数: 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:41:41 - trade_close_service - INFO - [close_position:1589] - 现货平仓交易结果: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:41:41 - trade_close_service - ERROR - [close_position:1594] - 现货平仓失败: 执行现货平仓失败，已达到最大重试次数 (3)
2025-05-30 13:41:41 - trade_close_api - ERROR - [close_position:230] - === 平仓操作失败 ===
2025-05-30 13:41:41 - trade_close_api - ERROR - [close_position:231] - 订单ID: ORD20250530021730543492
2025-05-30 13:41:41 - trade_close_api - ERROR - [close_position:232] - 错误信息: 现货平仓交易执行失败: 执行现货平仓失败，已达到最大重试次数 (3)
2025-05-30 13:41:41 - trade_close_api - ERROR - [close_position:233] - 失败步骤: None
2025-05-30 13:41:41 - trade_close_api - INFO - [close_position:237] - === 平仓API请求结束 ===
