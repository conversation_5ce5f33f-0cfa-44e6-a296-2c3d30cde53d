2025-05-30 01:15:45 - trade_close_api - INFO - [close_position:204] - === 平仓API请求开始 ===
2025-05-30 01:15:45 - trade_close_api - INFO - [close_position:205] - 用户ID: USR337855A0
2025-05-30 01:15:45 - trade_close_api - INFO - [close_position:206] - 订单ID: ORD20250530010727880650
2025-05-30 01:15:45 - trade_close_api - INFO - [close_position:207] - 请求时间: 2025-05-30T01:15:45.162605
2025-05-30 01:15:45 - trade_close_api - INFO - [close_position:216] - 开始执行平仓操作...
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1323] - === 平仓服务操作开始 ===
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1324] - 用户ID: USR337855A0
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1325] - 订单ID: ORD20250530010727880650
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1326] - 开始时间: 2025-05-30T01:15:45.162949
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1351] - 步骤1: 获取订单信息
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1368] - 订单验证成功: 方向=-1, 数量=1, 状态=open
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1369] - 开仓价格: 现货=769.45, 期货=774.52
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1370] - 开仓基差: -2.089999999999918
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1373] - 步骤2: 获取最新市场数据
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1378] - 当前市场数据: 现货买价=769.31, 现货卖价=770.0
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1379] - 当前市场数据: 期货买价=771.54, 期货卖价=771.65
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1382] - 步骤3: 获取用户账户信息
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1393] - 账户验证成功: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1396] - 步骤4: 计算平仓价格和方向
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1414] - 反向套利平仓: 现货卖出价=769.31, 期货买入价=771.65
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1416] - 平仓基差: -2.340000000000032
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1417] - 现货平仓方向: 卖出
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1418] - 期货平仓方向: 买入
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1421] - 步骤5: 计算盈亏
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1427] - 现货盈亏: -140.00000000010004
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1428] - 期货盈亏: 2870.0000000000045
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1429] - 总盈亏: 2729.9999999999045
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1430] - 净利润: 2319.9999999999045
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1433] - 步骤6: 开始执行平仓交易
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1446] - 步骤6.1: 执行现货平仓交易
2025-05-30 01:15:45 - trade_close_service - INFO - [close_position:1447] - 现货交易参数: 账户=SPOT337855A0, 方向=卖出, 价格=769.31, 数量=1000
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1457] - 现货平仓交易结果: {'success': True, 'message': '现货交易成功', 'order_id': 'B2025053001154628029', 'price': 769.31, 'execution_time': 7.606328964233398}
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1469] - 现货平仓成功: 订单ID=B2025053001154628029
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1479] - 步骤6.2: 执行期货平仓交易
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1480] - 期货交易参数: 账户=FUTURE337855A0, 方向=买入, 价格=771.65, 数量=1000
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1491] - 期货平仓交易结果: {'success': True, 'message': '期货交易成功', 'order_id': 'PYSDK_insert_b8996b9b0905aa8ac4750e85b9f851c2', 'price': nan, 'execution_time': 0.1551518440246582}
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1506] - 期货平仓成功: 订单ID=PYSDK_insert_b8996b9b0905aa8ac4750e85b9f851c2
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1509] - 步骤7: 更新订单状态
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1528] - 订单状态更新成功
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1546] - === 平仓操作完成 ===
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1547] - 订单ID: ORD20250530010727880650
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1548] - 用户ID: USR337855A0
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1549] - 方向: 反向套利
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1550] - 现货平仓价格: 769.31
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1551] - 期货平仓价格: 771.65
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1552] - 平仓基差: -2.340000000000032
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1553] - 总盈亏: 2729.9999999999045
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1554] - 净利润: 2319.9999999999045
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1555] - 执行时间: 7.77秒
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1556] - 现货平仓订单ID: B2025053001154628029
2025-05-30 01:15:52 - trade_close_service - INFO - [close_position:1557] - 期货平仓订单ID: PYSDK_insert_b8996b9b0905aa8ac4750e85b9f851c2
2025-05-30 01:15:52 - trade_close_api - INFO - [close_position:223] - === 平仓操作成功 ===
2025-05-30 01:15:52 - trade_close_api - INFO - [close_position:224] - 订单ID: ORD20250530010727880650
2025-05-30 01:15:52 - trade_close_api - INFO - [close_position:225] - 平仓时间: None
2025-05-30 01:15:52 - trade_close_api - INFO - [close_position:226] - 响应消息: 平仓成功
2025-05-30 01:15:52 - trade_close_api - INFO - [close_position:237] - === 平仓API请求结束 ===
