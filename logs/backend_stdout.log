2025-05-30 13:31:56,645 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:31:56 - main - INFO - 已创建PID文件: /gold/backend/app.pid
2025-05-30 13:31:56 - main - INFO - 找到可用端口: 8000
2025-05-30 13:32:02,154 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:32:02 - main - INFO - 应用启动
2025-05-30 13:32:02 - main - INFO - 开始初始化数据库表
2025-05-30 13:32:02 - init_db - INFO - 所有数据库表已存在，无需创建
2025-05-30 13:32:02 - main - INFO - 开始执行数据库迁移脚本
2025-05-30 13:32:02 - main - INFO - 执行完整数据库迁移脚本
2025-05-30 13:32:02 - main - ERROR - 执行数据库迁移脚本失败: there is no unique or exclusion constraint matching the ON CONFLICT specification

2025-05-30 13:32:02 - main - INFO - 数据库迁移脚本执行完成
2025-05-30 13:32:02 - main - INFO - 开始检查和修复accounts表结构
2025-05-30 13:32:02 - main - INFO - accounts表结构完整，无需修复
2025-05-30 13:32:02 - main - INFO - 数据库表初始化完成
2025-05-30 13:32:02 - main - INFO - 开始初始化默认数据
2025-05-30 13:32:02 - main - INFO - 默认数据初始化完成
2025-05-30 13:32:02 - main - INFO - 使用天勤SDK获取实时行情数据，非交易时间使用模拟数据
2025-05-30 13:32:02 - main - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:02 - main - INFO - 交易时段，优先使用天勤SDK获取实时行情数据
2025-05-30 13:32:02 - main - INFO - 启动多源市场数据服务...
2025-05-30 13:32:02 -     INFO - TqSdk professional 版剩余 14 天到期，如需续费或升级请访问 https://account.shinnytech.com/ 或联系相关工作人员。
2025-05-30 13:32:03 -     INFO - 通知 : 与 wss://api.shinnytech.com/t/nfmd/front/mobile 的网络连接已建立
2025-05-30 13:32:06 - main - INFO - 已注册WebSocket服务回调函数
2025-05-30 13:32:06 - main - INFO - 多源市场数据服务启动成功，当前数据源: real_time
2025-05-30 13:32:06 - main - INFO - 初始化交易服务...
2025-05-30 13:32:06,025 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:32:06 - main - INFO - 交易服务初始化成功
2025-05-30 13:32:06 - main - INFO - 开始初始化全局现货服务...
2025-05-30 13:32:06 - spot_service_manager - INFO - 创建新的全局现货服务实例
2025-05-30 13:32:06 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:32:06.025622
2025-05-30 13:32:06 - spot_service_manager - INFO - 开始初始化全局现货平台服务
2025-05-30 13:32:06 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:32:06 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:32:06 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:06 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:06 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:32:07 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:32:08 - main - INFO - 📨 收到市场数据回调: 17 个合约
2025-05-30 13:32:08 - main - INFO - 使用合约 SHFE.au2508 的数据进行广播
2025-05-30 13:32:08 - main - INFO - 📡 准备广播转换后的数据: future_bid=769.98, future_ask=770.0, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:08 - spot_service - ERROR - 登录过程中发生异常: BrowserContext.new_page: Target page, context or browser has been closed
Browser logs:

<launching> /opt/google/chrome/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DeferRendererTasksAfterInput,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --enable-automation --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --disable-gpu --mute-audio --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI,BlinkGenPropertyTrees --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-automation --hide-scrollbars --metrics-recording-only --window-size=1200,900 --user-data-dir=/tmp/playwright_chromiumdev_profile-lnIxX2 --remote-debugging-pipe --no-startup-window
<launched> pid=2486891
[pid=2486891][err] [0530/133207.934507:WARNING:chrome/app/chrome_main_linux.cc:80] Read channel stable from /opt/google/chrome/CHROME_VERSION_EXTRA
[pid=2486891][err] [2486891:2486914:0530/133208.223491:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225630:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225653:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225660:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.424061:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428684:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428712:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.431157:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.526822:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.528023:ERROR:ui/base/accelerators/global_accelerator_listener/global_accelerator_listener_linux.cc:356] Failed to connect to signal: org.freedesktop.portal.GlobalShortcuts.Activated
[pid=2486891][err] [2486891:2486891:0530/133208.528042:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486914:0530/133208.528109:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.529884:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
2025-05-30 13:32:08 - spot_service - ERROR - Traceback (most recent call last):
  File "/gold/backend/services/spot_service.py", line 466, in login
    self.page = await context.new_page()
  File "/usr/local/lib/python3.10/dist-packages/playwright/async_api/_generated.py", line 12791, in new_page
    return mapping.from_impl(await self._impl_obj.new_page())
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_browser_context.py", line 325, in new_page
    return from_channel(await self._channel.send("newPage"))
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py", line 61, in send
    return await self._connection.wrap_api_call(
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py", line 528, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.TargetClosedError: BrowserContext.new_page: Target page, context or browser has been closed
Browser logs:

<launching> /opt/google/chrome/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DeferRendererTasksAfterInput,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --enable-automation --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --disable-gpu --mute-audio --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI,BlinkGenPropertyTrees --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-automation --hide-scrollbars --metrics-recording-only --window-size=1200,900 --user-data-dir=/tmp/playwright_chromiumdev_profile-lnIxX2 --remote-debugging-pipe --no-startup-window
<launched> pid=2486891
[pid=2486891][err] [0530/133207.934507:WARNING:chrome/app/chrome_main_linux.cc:80] Read channel stable from /opt/google/chrome/CHROME_VERSION_EXTRA
[pid=2486891][err] [2486891:2486914:0530/133208.223491:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225630:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225653:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225660:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.424061:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428684:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428712:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.431157:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.526822:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.528023:ERROR:ui/base/accelerators/global_accelerator_listener/global_accelerator_listener_linux.cc:356] Failed to connect to signal: org.freedesktop.portal.GlobalShortcuts.Activated
[pid=2486891][err] [2486891:2486891:0530/133208.528042:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486914:0530/133208.528109:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.529884:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 

2025-05-30 13:32:08 - spot_service - ERROR - 初始化时登录失败
2025-05-30 13:32:08 - spot_service_manager - ERROR - 初始化全局现货平台服务失败
2025-05-30 13:32:08 - main - ERROR - 全局现货服务初始化失败
2025-05-30 13:32:08 - main - INFO - 开始初始化全局期货服务...
2025-05-30 13:32:09 - main - INFO - 📡 准备广播转换后的数据: future_bid=770.0, future_ask=770.02, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:09 -     INFO - TqSdk professional 版剩余 14 天到期，如需续费或升级请访问 https://account.shinnytech.com/ 或联系相关工作人员。
2025-05-30 13:32:11 - main - INFO - 全局期货服务初始化成功
2025-05-30 13:32:11 - main - INFO - 验证服务状态...
2025-05-30 13:32:11 - main - WARNING - ⚠️ 部分交易服务初始化失败，请检查配置
2025-05-30 13:32:11 - main - WARNING - 现货服务: ✗
2025-05-30 13:32:11 - main - WARNING - 期货服务: ✓
2025-05-30 13:32:11 - main - INFO - 交易服务测试模式: False
2025-05-30 13:32:11 - main - INFO - 交易服务处于生产模式，将执行实际交易
2025-05-30 13:32:11 - main - INFO - 现货交易始终使用实际执行模式，期货交易在非交易时段使用模拟模式
2025-05-30 13:32:11 - main - INFO - 初始化市场服务...
2025-05-30 13:32:11 - main - INFO - 市场服务初始化成功
2025-05-30 13:32:11 - main - INFO - 启动WebSocket服务...
2025-05-30 13:32:11 - main - INFO - WebSocket服务启动成功，实时数据推送已激活
2025-05-30 13:32:11 - main - INFO - 期货数据服务已集成到多源市场数据服务中
2025-05-30 13:32:11 - main - INFO - 应用启动完成
2025-05-30 13:32:11 - main - INFO - 📡 准备广播转换后的数据: future_bid=769.98, future_ask=770.06, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:12 - main - INFO - 📡 准备广播转换后的数据: future_bid=770.02, future_ask=770.08, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:13 - main - INFO - 📡 准备广播转换后的数据: future_bid=770.02, future_ask=770.06, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:14 - main - INFO - 📡 准备广播转换后的数据: future_bid=770.06, future_ask=770.08, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:15 - main - INFO - 📡 准备广播转换后的数据: future_bid=770.04, future_ask=770.08, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:23 - main - INFO - 📡 准备广播转换后的数据: future_bid=770.0, future_ask=770.06, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:30 - main - INFO - 📡 准备广播转换后的数据: future_bid=770.08, future_ask=770.1, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:33 - main - INFO - 📡 准备广播转换后的数据: future_bid=770.06, future_ask=770.1, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:38 - main - INFO - 📡 准备广播转换后的数据: future_bid=770.0, future_ask=770.04, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:46 - main - INFO - 📡 准备广播转换后的数据: future_bid=769.96, future_ask=769.98, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:47 - main - INFO - 📡 准备广播转换后的数据: future_bid=769.9, future_ask=769.94, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:48 - main - INFO - 📡 准备广播转换后的数据: future_bid=769.88, future_ask=769.92, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:49 - main - INFO - 📡 准备广播转换后的数据: future_bid=769.86, future_ask=769.88, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:50 - main - INFO - 📡 准备广播转换后的数据: future_bid=769.56, future_ask=769.64, spot_bid=765.38, spot_ask=766.07
2025-05-30 13:32:51 - main - INFO - 📡 准备广播转换后的数据: future_bid=769.56, future_ask=769.66, spot_bid=765.38, spot_ask=766.07
