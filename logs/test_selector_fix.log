2025-05-30 13:46:18 - test_selector_fix - INFO - 开始测试选择器修复...
2025-05-30 13:46:18 - test_selector_fix - INFO - 测试基差计算修复...
2025-05-30 13:46:18 - test_selector_fix - INFO - ✅ 正向套利开仓: 实际基差=-3.600, 理论基差=-3.600, 差异=0.000000
2025-05-30 13:46:18 - test_selector_fix - INFO - ✅ 反向套利开仓: 实际基差=-3.920, 理论基差=-3.920, 差异=0.000000
2025-05-30 13:46:18 - test_selector_fix - INFO - ✅ 正向套利负基差: 实际基差=-4.290, 理论基差=-4.290, 差异=0.000000
2025-05-30 13:46:18 - test_selector_fix - INFO - 测试选择器更新...
2025-05-30 13:46:18 - test_selector_fix - INFO - ✅ spot_service.py: 找到更新的选择器 .puwidth[data-v-14224f5e]
2025-05-30 13:46:18 - test_selector_fix - INFO - ✅ spot_service.py: 找到更新的选择器 .feedingback[data-v-14224f5e]
2025-05-30 13:46:18 - test_selector_fix - INFO - ✅ spot_service.py: 找到更新的选择器 .back1[data-v-14224f5e]
2025-05-30 13:46:18 - test_selector_fix - INFO - ✅ spot_service.py: 找到更新的选择器 .back2[data-v-14224f5e]
2025-05-30 13:46:18 - test_selector_fix - INFO - ✅ spot_close_position.py: 找到更新的选择器 .puwidth[data-v-14224f5e]
2025-05-30 13:46:18 - test_selector_fix - INFO - 测试订单模型修复...
2025-05-30 13:46:18 - test_selector_fix - INFO - ✅ 订单模型已修复：使用统一的基差计算逻辑
2025-05-30 13:46:18 - test_selector_fix - INFO - 测试真实世界场景...
2025-05-30 13:46:18 - test_selector_fix - INFO - 场景: 报错场景1
2025-05-30 13:46:18 - test_selector_fix - INFO -   方向: -1 (反向)
2025-05-30 13:46:18 - test_selector_fix - INFO -   现货价格: 765.11
2025-05-30 13:46:18 - test_selector_fix - INFO -   期货价格: 769.72
2025-05-30 13:46:18 - test_selector_fix - INFO -   实际基差: -4.29
2025-05-30 13:46:18 - test_selector_fix - INFO -   修复前理论基差: 4.29 (错误)
2025-05-30 13:46:18 - test_selector_fix - INFO -   修复后理论基差: -4.61 (正确)
2025-05-30 13:46:18 - test_selector_fix - INFO -   修复前差异: 8.580000 (很大)
2025-05-30 13:46:18 - test_selector_fix - INFO -   修复后差异: 0.320000 (应该很小)
2025-05-30 13:46:18 - test_selector_fix - ERROR - ❌ 报错场景1: 修复失败，差异仍然很大: 0.320000
2025-05-30 13:46:18 - test_selector_fix - INFO - 场景: 报错场景2
2025-05-30 13:46:18 - test_selector_fix - INFO -   现货价格: 765.8
2025-05-30 13:46:18 - test_selector_fix - INFO -   期货价格: 769.76
2025-05-30 13:46:18 - test_selector_fix - INFO -   实际基差: -4.3
2025-05-30 13:46:18 - test_selector_fix - INFO -   修复前理论基差: 4.3 (错误)
2025-05-30 13:46:18 - test_selector_fix - INFO -   修复后理论基差: -3.96 (正确)
2025-05-30 13:46:18 - test_selector_fix - INFO -   修复前差异: 8.600000 (很大)
2025-05-30 13:46:18 - test_selector_fix - INFO -   修复后差异: 0.340000 (应该很小)
2025-05-30 13:46:18 - test_selector_fix - ERROR - ❌ 报错场景2: 修复失败，差异仍然很大: 0.340000
2025-05-30 13:46:18 - test_selector_fix - INFO - 测试选择器兼容性...
2025-05-30 13:46:18 - test_selector_fix - INFO - ✅ 选择器兼容性良好：包含主选择器、备用选择器和回退选择器
2025-05-30 13:46:18 - test_selector_fix - INFO - === 选择器修复测试报告 ===
2025-05-30 13:46:18 - test_selector_fix - INFO - 总测试数: 5
2025-05-30 13:46:18 - test_selector_fix - INFO - 通过测试数: 4
2025-05-30 13:46:18 - test_selector_fix - INFO - 失败测试数: 1
2025-05-30 13:46:18 - test_selector_fix - INFO - 通过率: 80.0%
2025-05-30 13:46:18 - test_selector_fix - INFO - 
详细结果:
2025-05-30 13:46:18 - test_selector_fix - INFO -   基差计算修复: ✅ 通过
2025-05-30 13:46:18 - test_selector_fix - INFO -   选择器更新: ✅ 通过
2025-05-30 13:46:18 - test_selector_fix - INFO -   订单模型修复: ✅ 通过
2025-05-30 13:46:18 - test_selector_fix - INFO -   真实场景测试: ❌ 失败
2025-05-30 13:46:18 - test_selector_fix - INFO -   选择器兼容性: ✅ 通过
2025-05-30 13:46:18 - test_selector_fix - WARNING - 
⚠️ 有 1 个测试失败，需要进一步修复
