2025-05-30 13:32:04 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:32:04.153488
2025-05-30 13:32:04 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:32:04 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:32:04 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:04 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:04 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:32:04 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:32:04 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:04 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:06 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:32:06.025622
2025-05-30 13:32:06 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:32:06 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:32:06 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:06 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:06 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:32:07 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:32:07 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:32:08 - spot_service - ERROR - 登录过程中发生异常: Page.query_selector: Connection closed while reading from the driver
2025-05-30 13:32:08 - spot_service - ERROR - Traceback (most recent call last):
  File "/gold/backend/services/spot_service.py", line 500, in login
    button = await self.page.query_selector(selector)
  File "/usr/local/lib/python3.10/dist-packages/playwright/async_api/_generated.py", line 8086, in query_selector
    await self._impl_obj.query_selector(selector=selector, strict=strict)
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_page.py", line 413, in query_selector
    return await self._main_frame.query_selector(selector, strict)
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_frame.py", line 304, in query_selector
    await self._channel.send("querySelector", locals_to_params(locals()))
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py", line 61, in send
    return await self._connection.wrap_api_call(
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py", line 528, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
Exception: Page.query_selector: Connection closed while reading from the driver

2025-05-30 13:32:08 - spot_service - ERROR - 初始化时登录失败
2025-05-30 13:32:08 - spot_service - ERROR - 登录过程中发生异常: BrowserContext.new_page: Target page, context or browser has been closed
Browser logs:

<launching> /opt/google/chrome/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DeferRendererTasksAfterInput,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --enable-automation --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --disable-gpu --mute-audio --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI,BlinkGenPropertyTrees --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-automation --hide-scrollbars --metrics-recording-only --window-size=1200,900 --user-data-dir=/tmp/playwright_chromiumdev_profile-lnIxX2 --remote-debugging-pipe --no-startup-window
<launched> pid=2486891
[pid=2486891][err] [0530/133207.934507:WARNING:chrome/app/chrome_main_linux.cc:80] Read channel stable from /opt/google/chrome/CHROME_VERSION_EXTRA
[pid=2486891][err] [2486891:2486914:0530/133208.223491:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225630:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225653:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225660:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.424061:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428684:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428712:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.431157:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.526822:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.528023:ERROR:ui/base/accelerators/global_accelerator_listener/global_accelerator_listener_linux.cc:356] Failed to connect to signal: org.freedesktop.portal.GlobalShortcuts.Activated
[pid=2486891][err] [2486891:2486891:0530/133208.528042:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486914:0530/133208.528109:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.529884:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
2025-05-30 13:32:08 - spot_service - ERROR - Traceback (most recent call last):
  File "/gold/backend/services/spot_service.py", line 466, in login
    self.page = await context.new_page()
  File "/usr/local/lib/python3.10/dist-packages/playwright/async_api/_generated.py", line 12791, in new_page
    return mapping.from_impl(await self._impl_obj.new_page())
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_browser_context.py", line 325, in new_page
    return from_channel(await self._channel.send("newPage"))
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py", line 61, in send
    return await self._connection.wrap_api_call(
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py", line 528, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.TargetClosedError: BrowserContext.new_page: Target page, context or browser has been closed
Browser logs:

<launching> /opt/google/chrome/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DeferRendererTasksAfterInput,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --enable-automation --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --disable-gpu --mute-audio --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI,BlinkGenPropertyTrees --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-automation --hide-scrollbars --metrics-recording-only --window-size=1200,900 --user-data-dir=/tmp/playwright_chromiumdev_profile-lnIxX2 --remote-debugging-pipe --no-startup-window
<launched> pid=2486891
[pid=2486891][err] [0530/133207.934507:WARNING:chrome/app/chrome_main_linux.cc:80] Read channel stable from /opt/google/chrome/CHROME_VERSION_EXTRA
[pid=2486891][err] [2486891:2486914:0530/133208.223491:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225630:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225653:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225660:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.424061:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428684:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428712:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.431157:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.526822:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.528023:ERROR:ui/base/accelerators/global_accelerator_listener/global_accelerator_listener_linux.cc:356] Failed to connect to signal: org.freedesktop.portal.GlobalShortcuts.Activated
[pid=2486891][err] [2486891:2486891:0530/133208.528042:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486914:0530/133208.528109:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.529884:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 

2025-05-30 13:32:08 - spot_service - ERROR - 初始化时登录失败
2025-05-30 13:33:08 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:33:08.927153
2025-05-30 13:33:08 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:33:08 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:33:08 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:33:08 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:33:08 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:33:09 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:33:09 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:33:09 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:33:12 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:33:12 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:33:12 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:33:12 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:33:12 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:33:13 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:33:13 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:33:15 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:33:15 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:33:15 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:33:15 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:33:15 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:33:25 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:33:30 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:33:30 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:33:30 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:33:30 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:33:30 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:36:34 - spot_service - INFO - 开始平仓操作: 订单ID=S2025053002173230988, 使用违约结算=False
2025-05-30 13:36:34 - spot_service - INFO - 导航到订单页面
2025-05-30 13:36:37 - spot_service - INFO - 成功导航到订单页面
2025-05-30 13:36:37 - spot_service - INFO - 查找订单: S2025053002173230988
2025-05-30 13:36:39 - spot_service - INFO - 找到 0 个订单项
2025-05-30 13:36:39 - spot_service - WARNING - 未找到匹配订单: S2025053002173230988
2025-05-30 13:37:37 - spot_service - INFO - 开始平仓操作: 订单ID=None, 使用违约结算=False
2025-05-30 13:37:40 - spot_service - INFO - 查找订单: None
2025-05-30 13:37:42 - spot_service - WARNING - 未找到匹配订单: None
2025-05-30 13:40:07 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:40:07.734687
2025-05-30 13:40:07 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:40:07 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:40:07 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:07 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:07 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:40:07 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:40:08 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:08 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:11 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:40:11 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:40:11 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:40:11 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:40:11 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:40:12 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:40:12 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:40:13 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:40:14 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:40:14 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:40:14 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:40:14 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:40:24 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:40:29 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:29 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:40:29 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:40:29 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:40:29 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:40:34 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:40:34.065676
2025-05-30 13:40:34 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:40:34 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:40:34 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:34 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:34 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:40:34 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:40:34 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:34 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:37 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:40:37 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:40:37 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:40:37 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:40:38 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:40:39 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:40:39 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:40:40 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:40:40 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:40:40 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:40:40 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:40:40 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:40:50 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:40:55 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:55 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:40:55 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:40:55 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:40:55 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:41:00 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:41:00.385322
2025-05-30 13:41:00 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:41:00 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:41:00 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:41:00 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:41:00 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:41:00 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:41:00 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:41:01 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:41:04 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:41:04 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:41:04 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:41:04 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:41:04 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:41:05 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:41:05 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:41:06 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:41:06 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:41:06 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:41:06 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:41:06 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:41:16 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:41:21 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:41:21 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:41:21 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:41:21 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:41:21 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:41:22 - spot_service - INFO - 开始平仓操作: 订单ID=S2025053002173230988, 使用违约结算=False
2025-05-30 13:41:22 - spot_service - INFO - 导航到订单页面
2025-05-30 13:41:25 - spot_service - INFO - 成功导航到订单页面
2025-05-30 13:41:25 - spot_service - INFO - 查找订单: S2025053002173230988
2025-05-30 13:41:27 - spot_service - INFO - 找到 0 个订单项
2025-05-30 13:41:27 - spot_service - WARNING - 未找到匹配订单: S2025053002173230988
2025-05-30 13:43:34 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:43:34.867697
2025-05-30 13:43:34 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:43:34 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:43:34 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:43:34 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:43:34 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:43:35 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:43:35 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:43:35 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:43:38 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:43:38 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:43:38 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:43:38 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:43:38 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:43:40 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:43:40 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:43:41 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:43:41 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:43:41 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:43:41 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:43:41 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:43:51 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:43:56 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:43:56 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:43:56 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:43:56 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:43:56 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:44:20 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:44:20.552149
2025-05-30 13:44:20 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:44:20 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:44:20 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:20 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:20 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:44:20 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:44:21 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:21 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:24 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:44:24 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:44:24 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:44:24 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:44:24 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:44:25 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:44:25 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:44:26 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:44:26 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:44:26 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:44:26 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:44:26 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:44:36 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:44:41 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:41 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:44:41 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:44:41 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:44:41 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:44:46 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:44:46.825592
2025-05-30 13:44:46 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:44:46 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:44:46 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:46 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:46 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:44:47 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:44:47 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:47 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:50 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:44:50 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:44:50 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:44:50 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:44:50 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:44:51 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:44:51 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:44:53 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:44:53 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:44:53 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:44:53 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:44:53 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:45:03 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:45:08 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:08 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:45:08 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:45:08 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:45:08 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:45:13 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:45:13.202136
2025-05-30 13:45:13 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:45:13 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:45:13 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:13 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:13 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:45:13 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:45:13 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:13 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:16 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:45:17 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:45:17 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:45:17 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:45:17 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:45:18 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:45:18 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:45:19 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:45:19 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:45:19 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:45:19 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:45:19 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:45:29 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:45:34 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:34 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:45:34 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:45:34 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:45:34 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
