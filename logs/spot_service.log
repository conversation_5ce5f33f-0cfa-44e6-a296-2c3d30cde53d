
2025-05-30 01:02:27 - spot_service - INFO - 现货服务实例创建: 2025-05-30 01:02:27.520860
2025-05-30 01:02:27 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 01:02:27 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 01:02:27 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:02:27 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:02:27 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 01:02:27 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 01:02:28 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:02:28 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:02:31 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 01:02:31 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 01:02:31 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 01:02:31 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 01:02:31 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 01:02:32 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 01:02:32 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 01:02:33 - spot_service - INFO - 已输入硬编码密码
2025-05-30 01:02:33 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 01:02:33 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 01:02:33 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 01:02:33 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 01:02:43 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 01:02:49 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:02:49 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 01:02:49 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 01:02:49 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 01:02:49 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 01:07:27 - spot_service - INFO - 开始下单: 方向=买入, 价格=769.45, 数量=1000克, 最大可接受滑点=0.2
2025-05-30 01:07:27 - spot_service - INFO - 确保在交易页面
2025-05-30 01:07:27 - spot_service - INFO - 开始获取当前页面价格，交易方向: 买入
2025-05-30 01:07:27 - spot_service - INFO - 买入操作，获取卖出价(红色)，主选择器: .amountsetbox .dflex.moneybox .amountlist.redcolor
2025-05-30 01:07:27 - spot_service - INFO - 通过选择器'.amountsetbox .dflex.moneybox .amountlist.redcolor'获取到当前卖出价: 769.45
2025-05-30 01:07:27 - spot_service - INFO - 获取到当前实时价格: 769.45, 传入的历史价格: 769.45
2025-05-30 01:07:27 - spot_service - INFO - 实时价格与历史价格差异: 0.0
2025-05-30 01:07:27 - spot_service - INFO - 使用实时价格进行交易: 769.45
2025-05-30 01:07:27 - spot_service - INFO - 设置交易数量: 1000克
2025-05-30 01:07:27 - spot_service - INFO - 已点击1000g按钮
2025-05-30 01:07:28 - spot_service - INFO - 最终设置的克重: 0克
2025-05-30 01:07:28 - spot_service - WARNING - 设置的克重(0)与目标克重(1000)不一致
2025-05-30 01:07:28 - spot_service - INFO - 执行买入操作
2025-05-30 01:07:28 - spot_service - INFO - 下买单: 价格=769.45, 数量=1000
2025-05-30 01:07:28 - spot_service - INFO - 最终选择的买入按钮选择器: '.amountbox .rgbtn[data-v-0fa31b49]'
2025-05-30 01:07:28 - spot_service - INFO - 已点击买入按钮
2025-05-30 01:07:28 - spot_service - INFO - 处理买入确认弹窗
2025-05-30 01:07:29 - spot_service - INFO - 最终选择的买入确认按钮: 缓存的确认按钮选择器
2025-05-30 01:07:29 - spot_service - INFO - 已点击买入确认按钮
2025-05-30 01:07:30 - spot_service - INFO - 尝试提取订单ID...
2025-05-30 01:07:30 - spot_service - INFO - 未能从确认消息中提取订单ID，尝试导航到订单页面获取最新订单。
2025-05-30 01:07:30 - spot_service - INFO - 导航到订单页面
2025-05-30 01:07:33 - spot_service - INFO - 成功导航到订单页面
2025-05-30 01:07:33 - spot_service - INFO - 等待订单列表加载
2025-05-30 01:07:35 - spot_service - INFO - 使用选择器 '[class*='order']' 找到 13 个订单项
2025-05-30 01:07:35 - spot_service - INFO - 获取到最新订单: ID=S2025053001072955876, 状态=已完成, 价格=***********.0, 数量=0.0
2025-05-30 01:07:35 - spot_service - INFO - 从订单页面获取到最新订单ID: S2025053001072955876
2025-05-30 01:07:35 - spot_service - INFO - 交易成功，订单ID: S2025053001072955876
2025-05-30 01:07:35 - spot_service - INFO - 下单总耗时: 7.91秒
2025-05-30 01:07:35 - spot_service - INFO - 开始取消订单: 订单ID=S2025053001072955876
2025-05-30 01:07:38 - spot_service - INFO - 查找订单: S2025053001072955876
2025-05-30 01:07:40 - spot_service - INFO - 找到 0 个订单项
2025-05-30 01:07:40 - spot_service - WARNING - 未找到匹配订单: S2025053001072955876
2025-05-30 01:11:35 - spot_service - INFO - 现货服务实例创建: 2025-05-30 01:11:35.465309
2025-05-30 01:11:35 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 01:11:35 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 01:11:35 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:11:35 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:11:35 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 01:11:35 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 01:11:35 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:11:36 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:11:39 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 01:11:39 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 01:11:39 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 01:11:39 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 01:11:39 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 01:11:40 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 01:11:40 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 01:11:41 - spot_service - INFO - 已输入硬编码密码
2025-05-30 01:11:41 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 01:11:41 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 01:11:41 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 01:11:41 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 01:11:51 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 01:11:56 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:11:56 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 01:11:56 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 01:11:56 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 01:11:56 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 01:12:01 - spot_service - INFO - 现货服务实例创建: 2025-05-30 01:12:01.744474
2025-05-30 01:12:01 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 01:12:01 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 01:12:01 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:12:01 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:12:01 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 01:12:02 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 01:12:02 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:12:02 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:12:05 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 01:12:05 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 01:12:05 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 01:12:05 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 01:12:05 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 01:12:06 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 01:12:06 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 01:12:07 - spot_service - INFO - 已输入硬编码密码
2025-05-30 01:12:08 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 01:12:08 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 01:12:08 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 01:12:08 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 01:12:18 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 01:12:23 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 01:12:23 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 01:12:23 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 01:12:23 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 01:12:23 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 01:15:45 - spot_service - INFO - 开始下单: 方向=卖出, 价格=769.31, 数量=1000克, 最大可接受滑点=0.2
2025-05-30 01:15:45 - spot_service - INFO - 确保在交易页面
2025-05-30 01:15:45 - spot_service - INFO - 开始获取当前页面价格，交易方向: 卖出
2025-05-30 01:15:45 - spot_service - INFO - 卖出操作，获取买入价(绿色)，主选择器: .amountsetbox .dflex.moneybox .amountlist.greecolor
2025-05-30 01:15:45 - spot_service - INFO - 通过选择器'.amountsetbox .dflex.moneybox .amountlist.greecolor'获取到当前买入价: 769.31
2025-05-30 01:15:45 - spot_service - INFO - 获取到当前实时价格: 769.31, 传入的历史价格: 769.31
2025-05-30 01:15:45 - spot_service - INFO - 实时价格与历史价格差异: 0.0
2025-05-30 01:15:45 - spot_service - INFO - 使用实时价格进行交易: 769.31
2025-05-30 01:15:45 - spot_service - INFO - 设置交易数量: 1000克
2025-05-30 01:15:45 - spot_service - INFO - 已点击1000g按钮
2025-05-30 01:15:46 - spot_service - INFO - 最终设置的克重: 0克
2025-05-30 01:15:46 - spot_service - WARNING - 设置的克重(0)与目标克重(1000)不一致
2025-05-30 01:15:46 - spot_service - INFO - 执行卖出操作
2025-05-30 01:15:46 - spot_service - INFO - 下卖单: 价格=769.31, 数量=1000
2025-05-30 01:15:46 - spot_service - INFO - 最终选择的卖出按钮选择器: '.amountbox .lfbtn[data-v-0fa31b49]'
2025-05-30 01:15:46 - spot_service - INFO - 已点击卖出按钮
2025-05-30 01:15:46 - spot_service - INFO - 处理卖出确认弹窗
2025-05-30 01:15:46 - spot_service - INFO - 最终选择的卖出确认按钮: 缓存的确认按钮选择器
2025-05-30 01:15:46 - spot_service - INFO - 已点击卖出确认按钮
2025-05-30 01:15:47 - spot_service - INFO - 尝试提取订单ID...
2025-05-30 01:15:47 - spot_service - INFO - 未能从确认消息中提取订单ID，尝试导航到订单页面获取最新订单。
2025-05-30 01:15:47 - spot_service - INFO - 导航到订单页面
2025-05-30 01:15:50 - spot_service - INFO - 成功导航到订单页面
2025-05-30 01:15:50 - spot_service - INFO - 等待订单列表加载
2025-05-30 01:15:52 - spot_service - INFO - 使用选择器 '[class*='order']' 找到 19 个订单项
2025-05-30 01:15:52 - spot_service - INFO - 获取到最新订单: ID=B2025053001154628029, 状态=已完成, 价格=***********.0, 数量=0.0
2025-05-30 01:15:52 - spot_service - INFO - 从订单页面获取到最新订单ID: B2025053001154628029
2025-05-30 01:15:52 - spot_service - INFO - 交易成功，订单ID: B2025053001154628029
2025-05-30 01:15:52 - spot_service - INFO - 下单总耗时: 7.61秒
