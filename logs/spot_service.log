2025-05-30 13:32:04 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:32:04.153488
2025-05-30 13:32:04 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:32:04 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:32:04 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:04 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:04 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:32:04 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:32:04 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:04 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:06 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:32:06.025622
2025-05-30 13:32:06 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:32:06 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:32:06 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:06 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:32:06 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:32:07 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:32:07 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:32:08 - spot_service - ERROR - 登录过程中发生异常: Page.query_selector: Connection closed while reading from the driver
2025-05-30 13:32:08 - spot_service - ERROR - Traceback (most recent call last):
  File "/gold/backend/services/spot_service.py", line 500, in login
    button = await self.page.query_selector(selector)
  File "/usr/local/lib/python3.10/dist-packages/playwright/async_api/_generated.py", line 8086, in query_selector
    await self._impl_obj.query_selector(selector=selector, strict=strict)
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_page.py", line 413, in query_selector
    return await self._main_frame.query_selector(selector, strict)
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_frame.py", line 304, in query_selector
    await self._channel.send("querySelector", locals_to_params(locals()))
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py", line 61, in send
    return await self._connection.wrap_api_call(
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py", line 528, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
Exception: Page.query_selector: Connection closed while reading from the driver

2025-05-30 13:32:08 - spot_service - ERROR - 初始化时登录失败
2025-05-30 13:32:08 - spot_service - ERROR - 登录过程中发生异常: BrowserContext.new_page: Target page, context or browser has been closed
Browser logs:

<launching> /opt/google/chrome/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DeferRendererTasksAfterInput,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --enable-automation --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --disable-gpu --mute-audio --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI,BlinkGenPropertyTrees --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-automation --hide-scrollbars --metrics-recording-only --window-size=1200,900 --user-data-dir=/tmp/playwright_chromiumdev_profile-lnIxX2 --remote-debugging-pipe --no-startup-window
<launched> pid=2486891
[pid=2486891][err] [0530/133207.934507:WARNING:chrome/app/chrome_main_linux.cc:80] Read channel stable from /opt/google/chrome/CHROME_VERSION_EXTRA
[pid=2486891][err] [2486891:2486914:0530/133208.223491:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225630:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225653:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225660:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.424061:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428684:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428712:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.431157:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.526822:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.528023:ERROR:ui/base/accelerators/global_accelerator_listener/global_accelerator_listener_linux.cc:356] Failed to connect to signal: org.freedesktop.portal.GlobalShortcuts.Activated
[pid=2486891][err] [2486891:2486891:0530/133208.528042:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486914:0530/133208.528109:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.529884:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
2025-05-30 13:32:08 - spot_service - ERROR - Traceback (most recent call last):
  File "/gold/backend/services/spot_service.py", line 466, in login
    self.page = await context.new_page()
  File "/usr/local/lib/python3.10/dist-packages/playwright/async_api/_generated.py", line 12791, in new_page
    return mapping.from_impl(await self._impl_obj.new_page())
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_browser_context.py", line 325, in new_page
    return from_channel(await self._channel.send("newPage"))
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py", line 61, in send
    return await self._connection.wrap_api_call(
  File "/usr/local/lib/python3.10/dist-packages/playwright/_impl/_connection.py", line 528, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.TargetClosedError: BrowserContext.new_page: Target page, context or browser has been closed
Browser logs:

<launching> /opt/google/chrome/chrome --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DeferRendererTasksAfterInput,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --enable-automation --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --disable-gpu --mute-audio --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-breakpad --disable-component-extensions-with-background-pages --disable-extensions --disable-features=TranslateUI,BlinkGenPropertyTrees --disable-ipc-flooding-protection --disable-renderer-backgrounding --enable-automation --hide-scrollbars --metrics-recording-only --window-size=1200,900 --user-data-dir=/tmp/playwright_chromiumdev_profile-lnIxX2 --remote-debugging-pipe --no-startup-window
<launched> pid=2486891
[pid=2486891][err] [0530/133207.934507:WARNING:chrome/app/chrome_main_linux.cc:80] Read channel stable from /opt/google/chrome/CHROME_VERSION_EXTRA
[pid=2486891][err] [2486891:2486914:0530/133208.223491:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225630:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225653:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.225660:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.424061:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428684:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486914:0530/133208.428712:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.431157:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.526822:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486891:0530/133208.528023:ERROR:ui/base/accelerators/global_accelerator_listener/global_accelerator_listener_linux.cc:356] Failed to connect to signal: org.freedesktop.portal.GlobalShortcuts.Activated
[pid=2486891][err] [2486891:2486891:0530/133208.528042:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 
[pid=2486891][err] [2486891:2486914:0530/133208.528109:ERROR:dbus/bus.cc:408] Failed to connect to the bus: Could not parse server address: Unknown address type (examples of valid types are "tcp" and on UNIX "unix")
[pid=2486891][err] [2486891:2486891:0530/133208.529884:ERROR:dbus/object_proxy.cc:590] Failed to call method: org.freedesktop.DBus.NameHasOwner: object_path= /org/freedesktop/DBus: unknown error type: 

2025-05-30 13:32:08 - spot_service - ERROR - 初始化时登录失败
2025-05-30 13:33:08 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:33:08.927153
2025-05-30 13:33:08 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:33:08 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:33:08 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:33:08 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:33:08 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:33:09 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:33:09 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:33:09 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:33:12 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:33:12 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:33:12 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:33:12 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:33:12 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:33:13 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:33:13 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:33:15 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:33:15 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:33:15 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:33:15 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:33:15 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:33:25 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:33:30 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:33:30 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:33:30 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:33:30 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:33:30 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:36:34 - spot_service - INFO - 开始平仓操作: 订单ID=S2025053002173230988, 使用违约结算=False
2025-05-30 13:36:34 - spot_service - INFO - 导航到订单页面
2025-05-30 13:36:37 - spot_service - INFO - 成功导航到订单页面
2025-05-30 13:36:37 - spot_service - INFO - 查找订单: S2025053002173230988
2025-05-30 13:36:39 - spot_service - INFO - 找到 0 个订单项
2025-05-30 13:36:39 - spot_service - WARNING - 未找到匹配订单: S2025053002173230988
2025-05-30 13:37:37 - spot_service - INFO - 开始平仓操作: 订单ID=None, 使用违约结算=False
2025-05-30 13:37:40 - spot_service - INFO - 查找订单: None
2025-05-30 13:37:42 - spot_service - WARNING - 未找到匹配订单: None
2025-05-30 13:40:07 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:40:07.734687
2025-05-30 13:40:07 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:40:07 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:40:07 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:07 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:07 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:40:07 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:40:08 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:08 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:11 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:40:11 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:40:11 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:40:11 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:40:11 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:40:12 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:40:12 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:40:13 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:40:14 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:40:14 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:40:14 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:40:14 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:40:24 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:40:29 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:29 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:40:29 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:40:29 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:40:29 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:40:34 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:40:34.065676
2025-05-30 13:40:34 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:40:34 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:40:34 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:34 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:34 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:40:34 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:40:34 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:34 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:37 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:40:37 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:40:37 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:40:37 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:40:38 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:40:39 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:40:39 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:40:40 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:40:40 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:40:40 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:40:40 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:40:40 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:40:50 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:40:55 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:40:55 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:40:55 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:40:55 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:40:55 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:41:00 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:41:00.385322
2025-05-30 13:41:00 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:41:00 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:41:00 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:41:00 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:41:00 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:41:00 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:41:00 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:41:01 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:41:04 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:41:04 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:41:04 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:41:04 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:41:04 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:41:05 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:41:05 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:41:06 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:41:06 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:41:06 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:41:06 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:41:06 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:41:16 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:41:21 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:41:21 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:41:21 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:41:21 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:41:21 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:41:22 - spot_service - INFO - 开始平仓操作: 订单ID=S2025053002173230988, 使用违约结算=False
2025-05-30 13:41:22 - spot_service - INFO - 导航到订单页面
2025-05-30 13:41:25 - spot_service - INFO - 成功导航到订单页面
2025-05-30 13:41:25 - spot_service - INFO - 查找订单: S2025053002173230988
2025-05-30 13:41:27 - spot_service - INFO - 找到 0 个订单项
2025-05-30 13:41:27 - spot_service - WARNING - 未找到匹配订单: S2025053002173230988
2025-05-30 13:43:34 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:43:34.867697
2025-05-30 13:43:34 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:43:34 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:43:34 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:43:34 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:43:34 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:43:35 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:43:35 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:43:35 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:43:38 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:43:38 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:43:38 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:43:38 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:43:38 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:43:40 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:43:40 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:43:41 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:43:41 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:43:41 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:43:41 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:43:41 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:43:51 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:43:56 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:43:56 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:43:56 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:43:56 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:43:56 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:44:20 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:44:20.552149
2025-05-30 13:44:20 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:44:20 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:44:20 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:20 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:20 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:44:20 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:44:21 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:21 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:24 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:44:24 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:44:24 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:44:24 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:44:24 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:44:25 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:44:25 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:44:26 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:44:26 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:44:26 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:44:26 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:44:26 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:44:36 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:44:41 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:41 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:44:41 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:44:41 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:44:41 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:44:46 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:44:46.825592
2025-05-30 13:44:46 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:44:46 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:44:46 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:46 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:46 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:44:47 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:44:47 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:47 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:44:50 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:44:50 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:44:50 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:44:50 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:44:50 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:44:51 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:44:51 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:44:53 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:44:53 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:44:53 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:44:53 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:44:53 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:45:03 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:45:08 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:08 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:45:08 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:45:08 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:45:08 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:45:13 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:45:13.202136
2025-05-30 13:45:13 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:45:13 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:45:13 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:13 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:13 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:45:13 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:45:13 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:13 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:16 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:45:17 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:45:17 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:45:17 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:45:17 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:45:18 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:45:18 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:45:19 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:45:19 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:45:19 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:45:19 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:45:19 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:45:29 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:45:34 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:45:34 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:45:34 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:45:34 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:45:34 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:49:19 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:49:19.446521
2025-05-30 13:49:19 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:49:19 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:49:19 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:49:19 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:49:19 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:49:19 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:49:19 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:49:20 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:49:23 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:49:23 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:49:23 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:49:23 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:49:23 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:49:24 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:49:24 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:49:25 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:49:25 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:49:25 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:49:25 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:49:25 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:49:35 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:49:40 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:49:40 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:49:40 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:49:40 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:49:40 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:49:45 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:49:45.857617
2025-05-30 13:49:45 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:49:45 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:49:45 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:49:45 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:49:45 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:49:46 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:49:46 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:49:46 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:49:49 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:49:49 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:49:49 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:49:49 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:49:49 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:49:50 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:49:50 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:49:52 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:49:52 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:49:52 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:49:52 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:49:52 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:50:02 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:50:07 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:07 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:50:07 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:50:07 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:50:07 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:50:12 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:50:12.241929
2025-05-30 13:50:12 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:50:12 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:50:12 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:12 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:12 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:50:12 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:50:12 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:13 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:16 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:50:16 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:50:16 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:50:16 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:50:16 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:50:17 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:50:17 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:50:18 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:50:18 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:50:18 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:50:18 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:50:18 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:50:28 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:50:33 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:33 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:50:33 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:50:33 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:50:33 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:50:45 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:50:45.681647
2025-05-30 13:50:45 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:50:45 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:50:45 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:45 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:45 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:50:45 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:50:46 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:46 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:49 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:50:49 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:50:49 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:50:49 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:50:49 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:50:50 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:50:50 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:50:51 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:50:51.918458
2025-05-30 13:50:51 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:50:51 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:50:51 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:51 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:51 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:50:51 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:50:51 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:50:51 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:50:52 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:50:52 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:50:52 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:50:52 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:52 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:50:55 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:50:55 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:50:55 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:50:55 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:50:55 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:50:57 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:50:57 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:50:58 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:50:58 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:50:58 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:50:58 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:50:58 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:51:02 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:51:07 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:51:07 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:51:07 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:51:07 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:51:07 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:51:08 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:51:13 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:51:13 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:51:13 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:51:13 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:51:13 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:51:53 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:51:53.699664
2025-05-30 13:51:53 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:51:53 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:51:53 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:51:53 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:51:53 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:51:53 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:51:54 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:51:54 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:51:57 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:51:57 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:51:57 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:51:57 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:51:57 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:51:58 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:51:58 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:51:59 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:52:00 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:52:00 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:52:00 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:52:00 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:52:10 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:52:15 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:52:15 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:52:15 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:52:15 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:52:15 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:57:08 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:57:08.907086
2025-05-30 13:57:08 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:57:08 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:57:08 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:57:08 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:57:08 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:57:09 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:57:09 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:57:09 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:57:12 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:57:12 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:57:12 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:57:12 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:57:12 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:57:13 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:57:13 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:57:15 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:57:15 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:57:15 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:57:15 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:57:15 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:57:25 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:57:30 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:57:30 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:57:30 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:57:30 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:57:30 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:59:16 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:59:16.056056
2025-05-30 13:59:16 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:59:16 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:59:16 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:59:16 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:59:16 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:59:16 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:59:16 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:59:16 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:59:19 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:59:19 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:59:19 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:59:19 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:59:19 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:59:21 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:59:21 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:59:22 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:59:22 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:59:22 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:59:22 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:59:22 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 13:59:32 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 13:59:37 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:59:37 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 13:59:37 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 13:59:37 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 13:59:37 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 13:59:52 - spot_service - INFO - 现货服务实例创建: 2025-05-30 13:59:52.011527
2025-05-30 13:59:52 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 13:59:52 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 13:59:52 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:59:52 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:59:52 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 13:59:52 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 13:59:52 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:59:52 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 13:59:56 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 13:59:56 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 13:59:56 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 13:59:56 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 13:59:56 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 13:59:57 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 13:59:57 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 13:59:58 - spot_service - INFO - 已输入硬编码密码
2025-05-30 13:59:58 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 13:59:58 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 13:59:58 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 13:59:58 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 14:00:08 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 14:00:13 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:00:13 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 14:00:13 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 14:00:13 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 14:00:13 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 14:00:18 - spot_service - INFO - 现货服务实例创建: 2025-05-30 14:00:18.660272
2025-05-30 14:00:18 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 14:00:18 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 14:00:18 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:00:18 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:00:18 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 14:00:18 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 14:00:19 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:00:19 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:00:22 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 14:00:22 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 14:00:22 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 14:00:22 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 14:00:22 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 14:00:23 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 14:00:23 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 14:00:24 - spot_service - INFO - 已输入硬编码密码
2025-05-30 14:00:24 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 14:00:24 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 14:00:24 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 14:00:24 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 14:00:34 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 14:00:40 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:00:40 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 14:00:40 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 14:00:40 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 14:00:40 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 14:00:45 - spot_service - INFO - 现货服务实例创建: 2025-05-30 14:00:45.051630
2025-05-30 14:00:45 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 14:00:45 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 14:00:45 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:00:45 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:00:45 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 14:00:45 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 14:00:45 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:00:45 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:00:48 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 14:00:48 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 14:00:48 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 14:00:48 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 14:00:48 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 14:00:50 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 14:00:50 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 14:00:51 - spot_service - INFO - 已输入硬编码密码
2025-05-30 14:00:51 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 14:00:51 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 14:00:51 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 14:00:51 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 14:01:01 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 14:01:06 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:01:06 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 14:01:06 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 14:01:06 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 14:01:06 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 14:01:18 - spot_service - INFO - 现货服务实例创建: 2025-05-30 14:01:18.579963
2025-05-30 14:01:18 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 14:01:18 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 14:01:18 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:01:18 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:01:18 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 14:01:18 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 14:01:19 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:01:19 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:01:22 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 14:01:22 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 14:01:22 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 14:01:22 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 14:01:22 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 14:01:23 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 14:01:23 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 14:01:25 - spot_service - INFO - 已输入硬编码密码
2025-05-30 14:01:25 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 14:01:25 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 14:01:25 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 14:01:25 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 14:01:35 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 14:01:40 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:01:40 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 14:01:40 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 14:01:40 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 14:01:40 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 14:02:46 - spot_service - INFO - 开始下单: 方向=卖出, 价格=766.1, 数量=1000克, 最大可接受滑点=0.2
2025-05-30 14:02:46 - spot_service - INFO - 确保在交易页面
2025-05-30 14:02:46 - spot_service - INFO - 开始获取当前页面价格，交易方向: 卖出
2025-05-30 14:02:46 - spot_service - INFO - 卖出操作，获取买入价(绿色)，主选择器: .amountsetbox .dflex.moneybox .amountlist.greecolor
2025-05-30 14:02:46 - spot_service - INFO - 通过选择器'.amountsetbox .dflex.moneybox .amountlist.greecolor'获取到当前买入价: 766.1
2025-05-30 14:02:46 - spot_service - INFO - 获取到当前实时价格: 766.1, 传入的历史价格: 766.1
2025-05-30 14:02:46 - spot_service - INFO - 实时价格与历史价格差异: 0.0
2025-05-30 14:02:46 - spot_service - INFO - 使用实时价格进行交易: 766.1
2025-05-30 14:02:46 - spot_service - INFO - 设置交易数量: 1000克
2025-05-30 14:02:46 - spot_service - INFO - 已点击1000g按钮
2025-05-30 14:02:47 - spot_service - INFO - 最终设置的克重: 0克
2025-05-30 14:02:47 - spot_service - WARNING - 设置的克重(0)与目标克重(1000)不一致
2025-05-30 14:02:47 - spot_service - INFO - 执行卖出操作
2025-05-30 14:02:47 - spot_service - INFO - 下卖单: 价格=766.1, 数量=1000
2025-05-30 14:02:47 - spot_service - INFO - 最终选择的卖出按钮选择器: '.amountbox .lfbtn[data-v-0fa31b49]'
2025-05-30 14:02:47 - spot_service - INFO - 已点击卖出按钮
2025-05-30 14:02:47 - spot_service - INFO - 处理卖出确认弹窗
2025-05-30 14:02:48 - spot_service - INFO - 最终选择的卖出确认按钮: 缓存的确认按钮选择器
2025-05-30 14:02:48 - spot_service - INFO - 已点击卖出确认按钮
2025-05-30 14:02:49 - spot_service - INFO - 尝试提取订单ID...
2025-05-30 14:02:49 - spot_service - INFO - 未能从确认消息中提取订单ID，尝试导航到订单页面获取最新订单。
2025-05-30 14:02:49 - spot_service - INFO - 导航到订单页面
2025-05-30 14:02:52 - spot_service - INFO - 成功导航到订单页面
2025-05-30 14:02:52 - spot_service - INFO - 等待订单列表加载
2025-05-30 14:02:54 - spot_service - INFO - 使用选择器 '.puwidth[data-v-14224f5e]' 找到 2 个订单项
2025-05-30 14:02:54 - spot_service - INFO - 获取到最新订单: ID=B2025053014024825524, 状态=已完成, 价格=2025.0, 数量=0.0
2025-05-30 14:02:54 - spot_service - INFO - 从订单页面获取到最新订单ID: B2025053014024825524
2025-05-30 14:02:54 - spot_service - INFO - 交易成功，订单ID: B2025053014024825524
2025-05-30 14:02:54 - spot_service - INFO - 下单总耗时: 7.63秒
2025-05-30 14:08:27 - spot_service - INFO - 现货服务实例创建: 2025-05-30 14:08:27.227385
2025-05-30 14:08:27 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 14:08:27 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 14:08:27 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:08:27 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:08:27 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 14:08:27 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 14:08:27 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:08:28 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:08:31 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 14:08:31 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 14:08:31 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 14:08:31 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 14:08:31 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 14:08:32 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 14:08:32 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 14:08:33 - spot_service - INFO - 已输入硬编码密码
2025-05-30 14:08:33 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 14:08:33 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 14:08:33 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 14:08:33 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 14:08:43 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 14:08:48 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:08:48 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 14:08:48 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 14:08:48 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 14:08:48 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 14:16:26 - spot_service - INFO - 现货服务实例创建: 2025-05-30 14:16:26.851643
2025-05-30 14:16:26 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 14:16:26 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 14:16:26 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:16:26 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:16:26 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 14:16:27 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 14:16:27 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:16:27 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:16:30 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 14:16:30 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 14:16:30 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 14:16:30 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 14:16:30 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 14:16:32 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 14:16:32 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 14:16:33 - spot_service - INFO - 已输入硬编码密码
2025-05-30 14:16:33 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 14:16:33 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 14:16:33 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 14:16:33 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 14:16:43 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 14:16:48 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:16:48 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 14:16:48 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 14:16:48 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 14:16:48 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
2025-05-30 14:19:43 - spot_service - INFO - 现货服务实例创建: 2025-05-30 14:19:43.616854
2025-05-30 14:19:43 - spot_service - INFO - 正在初始化现货平台服务，使用硬编码配置
2025-05-30 14:19:43 - spot_service - INFO - 现货平台服务初始化成功: 黄金交易平台
2025-05-30 14:19:43 - spot_service - INFO - 现货平台URL: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:19:43 - spot_service - INFO - 开始登录现货平台: https://j.jtd9999.vip/h5/#/?demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:19:43 - spot_service - INFO - 创建新的浏览器实例和页面
2025-05-30 14:19:43 - spot_service - INFO - 找到Chrome浏览器: /opt/google/chrome/chrome
2025-05-30 14:19:44 - spot_service - INFO - 直接导航到交易页面: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:19:44 - spot_service - INFO - 页面加载完成: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:19:47 - spot_service - INFO - 检查是否已在交易页面...
2025-05-30 14:19:47 - spot_service - INFO - 未检测到交易按钮，检查是否需要登录...
2025-05-30 14:19:47 - spot_service - INFO - 检测到登录表单元素: .tell-rule .uni-form-item .uni-input-input，确认在登录页面
2025-05-30 14:19:47 - spot_service - INFO - 已在登录页面，开始执行登录...
2025-05-30 14:19:47 - spot_service - INFO - 找到用户名输入框: .tell-rule .uni-form-item .uni-input-input
2025-05-30 14:19:48 - spot_service - INFO - 已输入硬编码用户名: ***********
2025-05-30 14:19:48 - spot_service - INFO - 找到密码输入框: input[type='password']
2025-05-30 14:19:50 - spot_service - INFO - 已输入硬编码密码
2025-05-30 14:19:50 - spot_service - INFO - 找到登录按钮: uni-button[data-v-06e88858]
2025-05-30 14:19:50 - spot_service - INFO - 尝试点击登录按钮
2025-05-30 14:19:50 - spot_service - INFO - 已点击登录按钮 (尝试 #1)
2025-05-30 14:19:50 - spot_service - INFO - 登录后等待页面加载和响应...
2025-05-30 14:20:00 - spot_service - INFO - 直接导航到交易页面检查登录状态...
2025-05-30 14:20:05 - spot_service - INFO - 导航后URL不包含登录页路径: https://j.jtd9999.vip/h5/#/pages/business/bookingorder?gold_code=AU&demp_code=944440b68743bdaaf6e4cf7b5745893e
2025-05-30 14:20:05 - spot_service - INFO - 找到交易页面元素: .amountbox .rgbtn[data-v-0fa31b49]
2025-05-30 14:20:05 - spot_service - INFO - 导航验证通过，登录成功
2025-05-30 14:20:05 - spot_service - INFO - 账户连接状态: connected (硬编码配置，跳过数据库更新)
2025-05-30 14:20:05 - spot_service - INFO - 初始化时登录成功，会话已准备就绪
