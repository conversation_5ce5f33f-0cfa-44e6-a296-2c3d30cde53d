2025-05-30 02:13:18 - test_close_fix - INFO - 开始测试平仓修复...
2025-05-30 02:13:18 - test_close_fix - INFO - 测试现货平仓方法是否存在...
2025-05-30 02:13:18 - test_close_fix - INFO - ✅ _execute_spot_close_position方法存在
2025-05-30 02:13:18 - test_close_fix - INFO - ✅ _execute_spot_close_position_with_retry方法存在
2025-05-30 02:13:18 - test_close_fix - INFO - 测试方法签名是否正确...
2025-05-30 02:13:18 - test_close_fix - ERROR - ❌ _execute_spot_close_position方法签名错误: 期望['self', 'account_id', 'order_id'], 实际['account_id', 'order_id']
2025-05-30 02:13:18 - test_close_fix - ERROR - ❌ _execute_spot_close_position_with_retry方法签名错误: 期望['self', 'account_id', 'order_id'], 实际['account_id', 'order_id']
2025-05-30 02:13:18 - test_close_fix - INFO - 测试平仓逻辑流程...
2025-05-30 02:13:18 - test_close_fix - INFO - ✅ close_position方法调用了正确的现货平仓方法
2025-05-30 02:13:18 - test_close_fix - INFO - ✅ close_position方法没有调用错误的开仓方法
2025-05-30 02:13:18 - test_close_fix - INFO - 测试现货服务集成...
2025-05-30 02:13:18 - test_close_fix - INFO - ✅ _execute_spot_close_position方法调用了spot_service.close_position
2025-05-30 02:13:18 - test_close_fix - INFO - ✅ _execute_spot_close_position方法没有调用spot_service.place_order
2025-05-30 02:13:18 - test_close_fix - INFO - === 平仓修复测试报告 ===
2025-05-30 02:13:18 - test_close_fix - INFO - 总测试数: 8
2025-05-30 02:13:18 - test_close_fix - INFO - 通过测试数: 6
2025-05-30 02:13:18 - test_close_fix - INFO - 失败测试数: 2
2025-05-30 02:13:18 - test_close_fix - INFO - 通过率: 75.0%
2025-05-30 02:13:18 - test_close_fix - INFO - 
详细结果:
2025-05-30 02:13:18 - test_close_fix - INFO -   method__execute_spot_close_position: ✅ 通过
2025-05-30 02:13:18 - test_close_fix - INFO -   method__execute_spot_close_position_with_retry: ✅ 通过
2025-05-30 02:13:18 - test_close_fix - INFO -   close_method_signature: ❌ 失败
2025-05-30 02:13:18 - test_close_fix - INFO -   close_retry_method_signature: ❌ 失败
2025-05-30 02:13:18 - test_close_fix - INFO -   close_logic_flow: ✅ 通过
2025-05-30 02:13:18 - test_close_fix - INFO -   no_wrong_method: ✅ 通过
2025-05-30 02:13:18 - test_close_fix - INFO -   spot_service_close: ✅ 通过
2025-05-30 02:13:18 - test_close_fix - INFO -   no_place_order: ✅ 通过
2025-05-30 02:13:18 - test_close_fix - WARNING - 
⚠️ 有 2 个测试失败，需要进一步修复
2025-05-30 02:14:01 - test_close_fix - INFO - 开始测试平仓修复...
2025-05-30 02:14:01 - test_close_fix - INFO - 测试现货平仓方法是否存在...
2025-05-30 02:14:01 - test_close_fix - INFO - ✅ _execute_spot_close_position方法存在
2025-05-30 02:14:01 - test_close_fix - INFO - ✅ _execute_spot_close_position_with_retry方法存在
2025-05-30 02:14:01 - test_close_fix - INFO - 测试方法签名是否正确...
2025-05-30 02:14:01 - test_close_fix - INFO - ✅ _execute_spot_close_position方法签名正确: ['account_id', 'order_id']
2025-05-30 02:14:01 - test_close_fix - INFO - ✅ _execute_spot_close_position_with_retry方法签名正确: ['account_id', 'order_id']
2025-05-30 02:14:01 - test_close_fix - INFO - 测试平仓逻辑流程...
2025-05-30 02:14:01 - test_close_fix - INFO - ✅ close_position方法调用了正确的现货平仓方法
2025-05-30 02:14:01 - test_close_fix - INFO - ✅ close_position方法没有调用错误的开仓方法
2025-05-30 02:14:01 - test_close_fix - INFO - 测试现货服务集成...
2025-05-30 02:14:01 - test_close_fix - INFO - ✅ _execute_spot_close_position方法调用了spot_service.close_position
2025-05-30 02:14:01 - test_close_fix - INFO - ✅ _execute_spot_close_position方法没有调用spot_service.place_order
2025-05-30 02:14:01 - test_close_fix - INFO - === 平仓修复测试报告 ===
2025-05-30 02:14:01 - test_close_fix - INFO - 总测试数: 8
2025-05-30 02:14:01 - test_close_fix - INFO - 通过测试数: 8
2025-05-30 02:14:01 - test_close_fix - INFO - 失败测试数: 0
2025-05-30 02:14:01 - test_close_fix - INFO - 通过率: 100.0%
2025-05-30 02:14:01 - test_close_fix - INFO - 
详细结果:
2025-05-30 02:14:01 - test_close_fix - INFO -   method__execute_spot_close_position: ✅ 通过
2025-05-30 02:14:01 - test_close_fix - INFO -   method__execute_spot_close_position_with_retry: ✅ 通过
2025-05-30 02:14:01 - test_close_fix - INFO -   close_method_signature: ✅ 通过
2025-05-30 02:14:01 - test_close_fix - INFO -   close_retry_method_signature: ✅ 通过
2025-05-30 02:14:01 - test_close_fix - INFO -   close_logic_flow: ✅ 通过
2025-05-30 02:14:01 - test_close_fix - INFO -   no_wrong_method: ✅ 通过
2025-05-30 02:14:01 - test_close_fix - INFO -   spot_service_close: ✅ 通过
2025-05-30 02:14:01 - test_close_fix - INFO -   no_place_order: ✅ 通过
2025-05-30 02:14:01 - test_close_fix - INFO - 
🎉 所有测试通过！平仓修复成功！
