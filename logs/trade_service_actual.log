2025-05-29 22:32:48,937 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:32:54,527 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:33:05,137 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:33:15,141 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:33:25,848 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:33:29,185 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-29 22:33:57,438 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:34:00,753 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-29 22:34:35,364 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:34:57,348 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:35:20,668 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:35:23,970 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-29 22:37:03,040 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:37:04,668 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:37:07,416 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-29 22:38:03,490 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:38:05,180 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 22:38:07,991 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-29 22:44:37,190 - trade_service_actual - FATAL - 开始执行开仓操作: user_id=USR337855A0, direction=-1
2025-05-29 22:44:37,190 - trade_service_actual - ERROR - 开仓请求接收: user_id=USR337855A0, direction=-1
2025-05-29 22:44:37,190 - trade_service_actual - WARNING - 处理开仓请求: user_id=USR337855A0, direction=-1
2025-05-29 22:44:37,190 - trade_service_actual - INFO - 开仓请求: user_id=USR337855A0, direction=-1
2025-05-29 22:44:37,190 - trade_service_actual - DEBUG - 开仓请求详情: user_id=USR337855A0, direction=-1, 时间戳=2025-05-29T22:44:37.190521
2025-05-29 22:44:37,191 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-29 22:44:37,191 - trade_service_actual - INFO - 获取到用户账户 - 现货账户ID: SPOT337855A0, 期货账户ID: FUTURE337855A0
2025-05-29 22:44:37,193 - trade_service_actual - INFO - [TradeService] 优先执行现货交易，订单ID: ORD20250529224437191387, 用户ID: USR337855A0
2025-05-29 22:44:37,193 - trade_service_actual - INFO - [SPOT:ba6b3e8c] [现货交易开始] 账户=SPOT337855A0, 方向=买入, 参考价格=766.78, 数量=1000克
2025-05-29 22:44:37,193 - trade_service_actual - INFO - [ba6b3e8c] 开始执行买入操作: 方向=买入, 价格=766.78, 数量=1000克
2025-05-29 22:44:37,193 - trade_service_actual - INFO - [SPOT:ba6b3e8c] 开始获取现货服务实例
2025-05-29 22:44:37,194 - trade_service_actual - INFO - [SPOT:ba6b3e8c] 成功获取现货服务实例，登录状态: True
2025-05-29 22:44:37,194 - trade_service_actual - FATAL - [SPOT:ba6b3e8c] 准备执行买入操作，数量=1000克
2025-05-29 22:44:37,194 - trade_service_actual - FATAL - [SPOT:ba6b3e8c] 调用spot_service.place_order: direction=1, price=766.78, amount=1000, max_slippage=0.2
2025-05-29 22:44:44,789 - trade_service_actual - FATAL - [SPOT:ba6b3e8c] spot_service.place_order返回结果: {'success': True, 'message': '下单成功', 'order_id': 'S2025052922443849847', 'direction': 1, 'price': 766.78, 'amount': 1000, 'execution_time': 7.594918489456177}
2025-05-29 22:44:44,789 - trade_service_actual - FATAL - [SPOT:ba6b3e8c] 现货交易成功: 订单ID=S2025052922443849847, 实际价格=766.78, 耗时=7.60秒
2025-05-29 22:44:44,789 - trade_service_actual - INFO - 现货交易成功: 账户=SPOT337855A0, 方向=买入, 参考价格=766.78, 实际价格=766.78, 数量=1000克, 订单ID=S2025052922443849847
2025-05-29 22:44:44,789 - trade_service_actual - INFO - [TradeService] 现货交易结果，订单ID ORD20250529224437191387: {'success': True, 'message': '现货交易成功', 'order_id': 'S2025052922443849847', 'price': 766.78, 'execution_time': 7.595545768737793}
2025-05-29 22:44:44,792 - trade_service_actual - INFO - [TradeService] 现货交易成功，开始执行期货交易，订单ID: ORD20250529224437191387
2025-05-29 22:44:44,792 - trade_service_actual - INFO - 执行期货开仓，现货已成功，期货使用市价单确保100%成交
2025-05-29 22:44:44,792 - trade_service_actual - INFO - [FUTURE:0cafa862] [期货开仓开始] 账户=FUTURE337855A0, 方向=卖出, 价格=771.54, 数量=1000克
2025-05-29 22:44:44,792 - trade_service_actual - INFO - [FUTURE:0cafa862] 使用市价单执行卖出操作，数量=1000克
2025-05-29 22:44:44,792 - trade_service_actual - INFO - [FUTURE:0cafa862] 现货已成功，期货采用市价单策略确保100%成交，避免撤单
2025-05-29 22:44:44,893 - trade_service_actual - INFO - [FUTURE:0cafa862] 期货交易成功: 订单ID=PYSDK_insert_029b11eefd157513f902bb2478741b61, 实际价格=771.5, 耗时=0.10秒
2025-05-29 22:44:44,893 - trade_service_actual - INFO - 期货交易成功: 账户=FUTURE337855A0, 方向=卖出, 参考价格=771.54, 实际价格=771.5, 数量=1000克, 订单ID=PYSDK_insert_029b11eefd157513f902bb2478741b61
2025-05-29 22:44:44,893 - trade_service_actual - INFO - 期货开仓成功: 期货交易成功
2025-05-29 22:44:44,893 - trade_service_actual - INFO - [TradeService] 期货交易结果，订单ID ORD20250529224437191387: {'success': True, 'message': '期货交易成功', 'order_id': 'PYSDK_insert_029b11eefd157513f902bb2478741b61', 'price': 771.5, 'execution_time': 0.1006474494934082}
2025-05-29 22:44:44,893 - trade_service_actual - ERROR - 交易执行过程中出错: value too long for type character varying(36)

2025-05-29 22:44:44,896 - trade_service_actual - ERROR - Traceback (most recent call last):
  File "/gold/backend/services/trade_service.py", line 748, in open_position
    await order_service.update_order_future_id(order.id, future_order_result["order_id"])
  File "/gold/backend/services/order_service.py", line 344, in update_order_future_id
    updated_order = self.postgres_client.fetch_one(query, (future_order_id, order_id))
  File "/gold/backend/data_storage/postgres_client.py", line 164, in fetch_one
    cur = self.execute(query, params)
  File "/gold/backend/data_storage/postgres_client.py", line 126, in execute
    cur.execute(query, params)
  File "/usr/local/lib/python3.10/dist-packages/psycopg2/extras.py", line 236, in execute
    return super().execute(query, vars)
psycopg2.errors.StringDataRightTruncation: value too long for type character varying(36)


2025-05-29 22:44:44,896 - trade_service_actual - INFO - [SPOT:5d099e62] [取消现货订单开始] 账户=SPOT337855A0, 订单ID=S2025052922443849847
2025-05-29 22:44:44,896 - trade_service_actual - INFO - [SPOT:5d099e62] 执行取消操作，订单ID=S2025052922443849847
2025-05-29 22:44:49,982 - trade_service_actual - ERROR - [SPOT:5d099e62] 现货订单取消失败: 未找到订单: S2025052922443849847, 耗时=5.09秒
2025-05-29 22:44:49,982 - trade_service_actual - ERROR - 现货订单取消失败: 账户=SPOT337855A0, 订单ID=S2025052922443849847, 错误=未找到订单: S2025052922443849847
2025-05-29 22:44:49,982 - trade_service_actual - WARNING - 取消现货订单失败 (尝试 1/3): 未找到订单: S2025052922443849847
2025-05-29 22:44:51,983 - trade_service_actual - INFO - [SPOT:0c9c8791] [取消现货订单开始] 账户=SPOT337855A0, 订单ID=S2025052922443849847
2025-05-29 22:44:51,983 - trade_service_actual - INFO - [SPOT:0c9c8791] 执行取消操作，订单ID=S2025052922443849847
2025-05-29 22:44:57,073 - trade_service_actual - ERROR - [SPOT:0c9c8791] 现货订单取消失败: 未找到订单: S2025052922443849847, 耗时=5.09秒
2025-05-29 22:44:57,073 - trade_service_actual - ERROR - 现货订单取消失败: 账户=SPOT337855A0, 订单ID=S2025052922443849847, 错误=未找到订单: S2025052922443849847
2025-05-29 22:44:57,073 - trade_service_actual - WARNING - 取消现货订单失败 (尝试 2/3): 未找到订单: S2025052922443849847
2025-05-29 22:44:59,074 - trade_service_actual - INFO - [SPOT:25371375] [取消现货订单开始] 账户=SPOT337855A0, 订单ID=S2025052922443849847
2025-05-29 22:44:59,075 - trade_service_actual - INFO - [SPOT:25371375] 执行取消操作，订单ID=S2025052922443849847
2025-05-29 22:45:04,106 - trade_service_actual - ERROR - [SPOT:25371375] 现货订单取消失败: 未找到订单: S2025052922443849847, 耗时=5.03秒
2025-05-29 22:45:04,106 - trade_service_actual - ERROR - 现货订单取消失败: 账户=SPOT337855A0, 订单ID=S2025052922443849847, 错误=未找到订单: S2025052922443849847
2025-05-29 22:45:04,106 - trade_service_actual - WARNING - 取消现货订单失败 (尝试 3/3): 未找到订单: S2025052922443849847
2025-05-29 22:45:06,106 - trade_service_actual - ERROR - 无法取消现货订单，请手动处理: S2025052922443849847
2025-05-29 22:45:06,107 - trade_service_actual - INFO - [FUTURE:3ba3c27c] [取消期货订单开始] 账户=FUTURE337855A0, 订单ID=PYSDK_insert_029b11eefd157513f902bb2478741b61
2025-05-29 22:45:06,107 - trade_service_actual - INFO - [FUTURE:3ba3c27c] 执行取消操作，订单ID=PYSDK_insert_029b11eefd157513f902bb2478741b61
2025-05-29 22:45:06,107 - trade_service_actual - INFO - [FUTURE:3ba3c27c] 期货订单取消成功: 订单ID=PYSDK_insert_029b11eefd157513f902bb2478741b61, 耗时=0.00秒
2025-05-29 22:45:06,107 - trade_service_actual - INFO - 期货订单取消成功: 账户=FUTURE337855A0, 订单ID=PYSDK_insert_029b11eefd157513f902bb2478741b61
2025-05-29 22:45:06,110 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-29 23:03:49,651 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 23:03:51,295 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 23:03:54,083 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-29 23:31:03,160 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 23:31:04,786 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-29 23:31:07,581 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-29 23:53:46,772 - trade_service_actual - FATAL - 开始执行开仓操作: user_id=USR337855A0, direction=-1
2025-05-29 23:53:46,772 - trade_service_actual - ERROR - 开仓请求接收: user_id=USR337855A0, direction=-1
2025-05-29 23:53:46,772 - trade_service_actual - WARNING - 处理开仓请求: user_id=USR337855A0, direction=-1
2025-05-29 23:53:46,772 - trade_service_actual - INFO - 开仓请求: user_id=USR337855A0, direction=-1
2025-05-29 23:53:46,772 - trade_service_actual - DEBUG - 开仓请求详情: user_id=USR337855A0, direction=-1, 时间戳=2025-05-29T23:53:46.772752
2025-05-29 23:53:46,773 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-29 23:53:46,773 - trade_service_actual - INFO - 获取到用户账户 - 现货账户ID: SPOT337855A0, 期货账户ID: FUTURE337855A0
2025-05-29 23:53:46,776 - trade_service_actual - INFO - [TradeService] 优先执行现货交易，订单ID: ORD20250529235346773889, 用户ID: USR337855A0
2025-05-29 23:53:46,776 - trade_service_actual - INFO - [SPOT:f5466a93] [现货交易开始] 账户=SPOT337855A0, 方向=买入, 参考价格=768.54, 数量=1000克
2025-05-29 23:53:46,776 - trade_service_actual - INFO - [f5466a93] 开始执行买入操作: 方向=买入, 价格=768.54, 数量=1000克
2025-05-29 23:53:46,776 - trade_service_actual - INFO - [SPOT:f5466a93] 开始获取现货服务实例
2025-05-29 23:53:46,776 - trade_service_actual - INFO - [SPOT:f5466a93] 成功获取现货服务实例，登录状态: True
2025-05-29 23:53:46,776 - trade_service_actual - FATAL - [SPOT:f5466a93] 准备执行买入操作，数量=1000克
2025-05-29 23:53:46,776 - trade_service_actual - FATAL - [SPOT:f5466a93] 调用spot_service.place_order: direction=1, price=768.54, amount=1000, max_slippage=0.2
2025-05-29 23:53:54,386 - trade_service_actual - FATAL - [SPOT:f5466a93] spot_service.place_order返回结果: {'success': True, 'message': '下单成功', 'order_id': 'S2025052923534877332', 'direction': 1, 'price': 768.54, 'amount': 1000, 'execution_time': 7.60962700843811}
2025-05-29 23:53:54,386 - trade_service_actual - FATAL - [SPOT:f5466a93] 现货交易成功: 订单ID=S2025052923534877332, 实际价格=768.54, 耗时=7.61秒
2025-05-29 23:53:54,386 - trade_service_actual - INFO - 现货交易成功: 账户=SPOT337855A0, 方向=买入, 参考价格=768.54, 实际价格=768.54, 数量=1000克, 订单ID=S2025052923534877332
2025-05-29 23:53:54,386 - trade_service_actual - INFO - [TradeService] 现货交易结果，订单ID ORD20250529235346773889: {'success': True, 'message': '现货交易成功', 'order_id': 'S2025052923534877332', 'price': 768.54, 'execution_time': 7.610232830047607}
2025-05-29 23:53:54,389 - trade_service_actual - INFO - [TradeService] 现货交易成功，开始执行期货交易，订单ID: ORD20250529235346773889
2025-05-29 23:53:54,389 - trade_service_actual - INFO - 执行期货开仓，现货已成功，期货使用市价单确保100%成交
2025-05-29 23:53:54,389 - trade_service_actual - INFO - [FUTURE:1fd8cd18] [期货开仓开始] 账户=FUTURE337855A0, 方向=卖出, 价格=771.54, 数量=1000克
2025-05-29 23:53:54,389 - trade_service_actual - INFO - [FUTURE:1fd8cd18] 使用市价单执行卖出操作，数量=1000克
2025-05-29 23:53:54,389 - trade_service_actual - INFO - [FUTURE:1fd8cd18] 现货已成功，期货采用市价单策略确保100%成交，避免撤单
2025-05-29 23:53:54,483 - trade_service_actual - INFO - [FUTURE:1fd8cd18] 期货交易成功: 订单ID=PYSDK_insert_297802dbc35fed2df3b5946bff74090b, 实际价格=773.3000000000001, 耗时=0.09秒
2025-05-29 23:53:54,483 - trade_service_actual - INFO - 期货交易成功: 账户=FUTURE337855A0, 方向=卖出, 参考价格=771.54, 实际价格=773.3000000000001, 数量=1000克, 订单ID=PYSDK_insert_297802dbc35fed2df3b5946bff74090b
2025-05-29 23:53:54,483 - trade_service_actual - INFO - 期货开仓成功: 期货交易成功
2025-05-29 23:53:54,483 - trade_service_actual - INFO - [TradeService] 期货交易结果，订单ID ORD20250529235346773889: {'success': True, 'message': '期货交易成功', 'order_id': 'PYSDK_insert_297802dbc35fed2df3b5946bff74090b', 'price': 773.3000000000001, 'execution_time': 0.09378981590270996}
2025-05-29 23:53:54,484 - trade_service_actual - ERROR - 交易执行过程中出错: value too long for type character varying(36)

2025-05-29 23:53:54,484 - trade_service_actual - ERROR - Traceback (most recent call last):
  File "/gold/backend/services/trade_service.py", line 748, in open_position
    await order_service.update_order_future_id(order.id, future_order_result["order_id"])
  File "/gold/backend/services/order_service.py", line 344, in update_order_future_id
    updated_order = self.postgres_client.fetch_one(query, (future_order_id, order_id))
  File "/gold/backend/data_storage/postgres_client.py", line 164, in fetch_one
    cur = self.execute(query, params)
  File "/gold/backend/data_storage/postgres_client.py", line 126, in execute
    cur.execute(query, params)
  File "/usr/local/lib/python3.10/dist-packages/psycopg2/extras.py", line 236, in execute
    return super().execute(query, vars)
psycopg2.errors.StringDataRightTruncation: value too long for type character varying(36)


2025-05-29 23:53:54,485 - trade_service_actual - INFO - [SPOT:12519253] [取消现货订单开始] 账户=SPOT337855A0, 订单ID=S2025052923534877332
2025-05-29 23:53:54,485 - trade_service_actual - INFO - [SPOT:12519253] 执行取消操作，订单ID=S2025052923534877332
2025-05-29 23:53:59,560 - trade_service_actual - ERROR - [SPOT:12519253] 现货订单取消失败: 未找到订单: S2025052923534877332, 耗时=5.08秒
2025-05-29 23:53:59,560 - trade_service_actual - ERROR - 现货订单取消失败: 账户=SPOT337855A0, 订单ID=S2025052923534877332, 错误=未找到订单: S2025052923534877332
2025-05-29 23:53:59,560 - trade_service_actual - WARNING - 取消现货订单失败 (尝试 1/3): 未找到订单: S2025052923534877332
2025-05-29 23:54:01,560 - trade_service_actual - INFO - [SPOT:b0e1eac8] [取消现货订单开始] 账户=SPOT337855A0, 订单ID=S2025052923534877332
2025-05-29 23:54:01,561 - trade_service_actual - INFO - [SPOT:b0e1eac8] 执行取消操作，订单ID=S2025052923534877332
2025-05-29 23:54:06,598 - trade_service_actual - ERROR - [SPOT:b0e1eac8] 现货订单取消失败: 未找到订单: S2025052923534877332, 耗时=5.04秒
2025-05-29 23:54:06,598 - trade_service_actual - ERROR - 现货订单取消失败: 账户=SPOT337855A0, 订单ID=S2025052923534877332, 错误=未找到订单: S2025052923534877332
2025-05-29 23:54:06,599 - trade_service_actual - WARNING - 取消现货订单失败 (尝试 2/3): 未找到订单: S2025052923534877332
2025-05-29 23:54:08,599 - trade_service_actual - INFO - [SPOT:624f5aaa] [取消现货订单开始] 账户=SPOT337855A0, 订单ID=S2025052923534877332
2025-05-29 23:54:08,599 - trade_service_actual - INFO - [SPOT:624f5aaa] 执行取消操作，订单ID=S2025052923534877332
2025-05-29 23:54:13,627 - trade_service_actual - ERROR - [SPOT:624f5aaa] 现货订单取消失败: 未找到订单: S2025052923534877332, 耗时=5.03秒
2025-05-29 23:54:13,628 - trade_service_actual - ERROR - 现货订单取消失败: 账户=SPOT337855A0, 订单ID=S2025052923534877332, 错误=未找到订单: S2025052923534877332
2025-05-29 23:54:13,628 - trade_service_actual - WARNING - 取消现货订单失败 (尝试 3/3): 未找到订单: S2025052923534877332
2025-05-29 23:54:15,629 - trade_service_actual - ERROR - 无法取消现货订单，请手动处理: S2025052923534877332
2025-05-29 23:54:15,629 - trade_service_actual - INFO - [FUTURE:cf953f96] [取消期货订单开始] 账户=FUTURE337855A0, 订单ID=PYSDK_insert_297802dbc35fed2df3b5946bff74090b
2025-05-29 23:54:15,629 - trade_service_actual - INFO - [FUTURE:cf953f96] 执行取消操作，订单ID=PYSDK_insert_297802dbc35fed2df3b5946bff74090b
2025-05-29 23:54:15,630 - trade_service_actual - INFO - [FUTURE:cf953f96] 期货订单取消成功: 订单ID=PYSDK_insert_297802dbc35fed2df3b5946bff74090b, 耗时=0.00秒
2025-05-29 23:54:15,630 - trade_service_actual - INFO - 期货订单取消成功: 账户=FUTURE337855A0, 订单ID=PYSDK_insert_297802dbc35fed2df3b5946bff74090b
2025-05-29 23:54:15,632 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 00:21:36,538 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:21:38,268 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:21:41,123 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 00:51:26,910 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:51:29,743 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 00:52:27,356 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:52:30,150 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 00:55:21,526 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:55:24,292 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 00:56:07,228 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:56:09,983 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 00:56:52,400 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:56:55,181 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 00:57:18,923 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:57:21,688 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 00:57:45,167 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:57:47,932 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 00:58:11,497 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:58:14,263 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 00:58:41,744 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:58:44,520 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 00:59:08,030 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:59:10,792 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 00:59:43,359 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 00:59:46,113 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 01:00:14,310 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 01:00:17,068 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 01:02:23,031 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 01:02:24,741 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 01:02:27,520 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 01:07:27,879 - trade_service_actual - FATAL - 开始执行开仓操作: user_id=USR337855A0, direction=-1
2025-05-30 01:07:27,879 - trade_service_actual - ERROR - 开仓请求接收: user_id=USR337855A0, direction=-1
2025-05-30 01:07:27,879 - trade_service_actual - WARNING - 处理开仓请求: user_id=USR337855A0, direction=-1
2025-05-30 01:07:27,879 - trade_service_actual - INFO - 开仓请求: user_id=USR337855A0, direction=-1
2025-05-30 01:07:27,879 - trade_service_actual - DEBUG - 开仓请求详情: user_id=USR337855A0, direction=-1, 时间戳=2025-05-30T01:07:27.879885
2025-05-30 01:07:27,880 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 01:07:27,880 - trade_service_actual - INFO - 获取到用户账户 - 现货账户ID: SPOT337855A0, 期货账户ID: FUTURE337855A0
2025-05-30 01:07:27,882 - trade_service_actual - INFO - [TradeService] 优先执行现货交易，订单ID: ORD20250530010727880650, 用户ID: USR337855A0
2025-05-30 01:07:27,883 - trade_service_actual - INFO - [SPOT:a2fb8fc2] [现货交易开始] 账户=SPOT337855A0, 方向=买入, 参考价格=769.45, 数量=1000克
2025-05-30 01:07:27,883 - trade_service_actual - INFO - [a2fb8fc2] 开始执行买入操作: 方向=买入, 价格=769.45, 数量=1000克
2025-05-30 01:07:27,883 - trade_service_actual - INFO - [SPOT:a2fb8fc2] 开始获取现货服务实例
2025-05-30 01:07:27,883 - trade_service_actual - INFO - [SPOT:a2fb8fc2] 成功获取现货服务实例，登录状态: True
2025-05-30 01:07:27,883 - trade_service_actual - FATAL - [SPOT:a2fb8fc2] 准备执行买入操作，数量=1000克
2025-05-30 01:07:27,883 - trade_service_actual - FATAL - [SPOT:a2fb8fc2] 调用spot_service.place_order: direction=1, price=769.45, amount=1000, max_slippage=0.2
2025-05-30 01:07:35,796 - trade_service_actual - FATAL - [SPOT:a2fb8fc2] spot_service.place_order返回结果: {'success': True, 'message': '下单成功', 'order_id': 'S2025053001072955876', 'direction': 1, 'price': 769.45, 'amount': 1000, 'execution_time': 7.913003206253052}
2025-05-30 01:07:35,796 - trade_service_actual - FATAL - [SPOT:a2fb8fc2] 现货交易成功: 订单ID=S2025053001072955876, 实际价格=769.45, 耗时=7.91秒
2025-05-30 01:07:35,796 - trade_service_actual - INFO - 现货交易成功: 账户=SPOT337855A0, 方向=买入, 参考价格=769.45, 实际价格=769.45, 数量=1000克, 订单ID=S2025053001072955876
2025-05-30 01:07:35,796 - trade_service_actual - INFO - [TradeService] 现货交易结果，订单ID ORD20250530010727880650: {'success': True, 'message': '现货交易成功', 'order_id': 'S2025053001072955876', 'price': 769.45, 'execution_time': 7.913643836975098}
2025-05-30 01:07:35,799 - trade_service_actual - INFO - [TradeService] 现货交易成功，开始执行期货交易，订单ID: ORD20250530010727880650
2025-05-30 01:07:35,799 - trade_service_actual - INFO - 执行期货开仓，现货已成功，期货使用市价单确保100%成交
2025-05-30 01:07:35,799 - trade_service_actual - INFO - [FUTURE:f95eb7c1] [期货开仓开始] 账户=FUTURE337855A0, 方向=卖出, 价格=771.54, 数量=1000克
2025-05-30 01:07:35,799 - trade_service_actual - INFO - [FUTURE:f95eb7c1] 使用市价单执行卖出操作，数量=1000克
2025-05-30 01:07:35,799 - trade_service_actual - INFO - [FUTURE:f95eb7c1] 现货已成功，期货采用市价单策略确保100%成交，避免撤单
2025-05-30 01:07:35,893 - trade_service_actual - INFO - [FUTURE:f95eb7c1] 期货交易成功: 订单ID=PYSDK_insert_3dabd1c77dcc96824ddb01803de40df7, 实际价格=774.52, 耗时=0.09秒
2025-05-30 01:07:35,893 - trade_service_actual - INFO - 期货交易成功: 账户=FUTURE337855A0, 方向=卖出, 参考价格=771.54, 实际价格=774.52, 数量=1000克, 订单ID=PYSDK_insert_3dabd1c77dcc96824ddb01803de40df7
2025-05-30 01:07:35,893 - trade_service_actual - INFO - 期货开仓成功: 期货交易成功
2025-05-30 01:07:35,893 - trade_service_actual - INFO - [TradeService] 期货交易结果，订单ID ORD20250530010727880650: {'success': True, 'message': '期货交易成功', 'order_id': 'PYSDK_insert_3dabd1c77dcc96824ddb01803de40df7', 'price': 774.52, 'execution_time': 0.09434294700622559}
2025-05-30 01:07:35,895 - trade_service_actual - INFO - [TradeService] 更新期货实际成交价格: 774.52，原价格: 771.54
2025-05-30 01:07:35,897 - trade_service_actual - ERROR - 交易执行过程中出错: 1 validation error for Order
open_basis
  Value error, 开仓基差(-2.089999999999918)与计算值(-5.07)不匹配 [type=value_error, input_value=-2.089999999999918, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error
2025-05-30 01:07:35,897 - trade_service_actual - ERROR - Traceback (most recent call last):
  File "/gold/backend/services/trade_service.py", line 754, in open_position
    await order_service.update_future_price(order.id, actual_future_price)
  File "/gold/backend/services/order_service.py", line 412, in update_future_price
    return Order(**updated_order)
  File "/usr/local/lib/python3.10/dist-packages/pydantic/main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for Order
open_basis
  Value error, 开仓基差(-2.089999999999918)与计算值(-5.07)不匹配 [type=value_error, input_value=-2.089999999999918, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error

2025-05-30 01:07:35,898 - trade_service_actual - INFO - [SPOT:4e9b5489] [取消现货订单开始] 账户=SPOT337855A0, 订单ID=S2025053001072955876
2025-05-30 01:07:35,898 - trade_service_actual - INFO - [SPOT:4e9b5489] 执行取消操作，订单ID=S2025053001072955876
2025-05-30 01:07:40,928 - trade_service_actual - ERROR - [SPOT:4e9b5489] 现货订单取消失败: 未找到订单: S2025053001072955876, 耗时=5.03秒
2025-05-30 01:07:40,928 - trade_service_actual - ERROR - 现货订单取消失败: 账户=SPOT337855A0, 订单ID=S2025053001072955876, 错误=未找到订单: S2025053001072955876
2025-05-30 01:07:40,928 - trade_service_actual - WARNING - 取消现货订单失败 (尝试 1/3): 未找到订单: S2025053001072955876
2025-05-30 01:07:42,929 - trade_service_actual - INFO - [SPOT:b442802a] [取消现货订单开始] 账户=SPOT337855A0, 订单ID=S2025053001072955876
2025-05-30 01:07:42,930 - trade_service_actual - INFO - [SPOT:b442802a] 执行取消操作，订单ID=S2025053001072955876
2025-05-30 01:07:47,961 - trade_service_actual - ERROR - [SPOT:b442802a] 现货订单取消失败: 未找到订单: S2025053001072955876, 耗时=5.03秒
2025-05-30 01:07:47,961 - trade_service_actual - ERROR - 现货订单取消失败: 账户=SPOT337855A0, 订单ID=S2025053001072955876, 错误=未找到订单: S2025053001072955876
2025-05-30 01:07:47,961 - trade_service_actual - WARNING - 取消现货订单失败 (尝试 2/3): 未找到订单: S2025053001072955876
2025-05-30 01:07:49,961 - trade_service_actual - INFO - [SPOT:9d0568e8] [取消现货订单开始] 账户=SPOT337855A0, 订单ID=S2025053001072955876
2025-05-30 01:07:49,962 - trade_service_actual - INFO - [SPOT:9d0568e8] 执行取消操作，订单ID=S2025053001072955876
2025-05-30 01:07:54,998 - trade_service_actual - ERROR - [SPOT:9d0568e8] 现货订单取消失败: 未找到订单: S2025053001072955876, 耗时=5.04秒
2025-05-30 01:07:54,998 - trade_service_actual - ERROR - 现货订单取消失败: 账户=SPOT337855A0, 订单ID=S2025053001072955876, 错误=未找到订单: S2025053001072955876
2025-05-30 01:07:54,998 - trade_service_actual - WARNING - 取消现货订单失败 (尝试 3/3): 未找到订单: S2025053001072955876
2025-05-30 01:07:56,999 - trade_service_actual - ERROR - 无法取消现货订单，请手动处理: S2025053001072955876
2025-05-30 01:07:57,000 - trade_service_actual - INFO - [FUTURE:da1b7fa3] [取消期货订单开始] 账户=FUTURE337855A0, 订单ID=PYSDK_insert_3dabd1c77dcc96824ddb01803de40df7
2025-05-30 01:07:57,000 - trade_service_actual - INFO - [FUTURE:da1b7fa3] 执行取消操作，订单ID=PYSDK_insert_3dabd1c77dcc96824ddb01803de40df7
2025-05-30 01:07:57,000 - trade_service_actual - INFO - [FUTURE:da1b7fa3] 期货订单取消成功: 订单ID=PYSDK_insert_3dabd1c77dcc96824ddb01803de40df7, 耗时=0.00秒
2025-05-30 01:07:57,000 - trade_service_actual - INFO - 期货订单取消成功: 账户=FUTURE337855A0, 订单ID=PYSDK_insert_3dabd1c77dcc96824ddb01803de40df7
2025-05-30 01:07:57,002 - trade_service_actual - ERROR - 开仓失败: value too long for type character varying(200)

2025-05-30 01:07:57,002 - trade_service_actual - ERROR - Traceback (most recent call last):
  File "/gold/backend/services/trade_service.py", line 754, in open_position
    await order_service.update_future_price(order.id, actual_future_price)
  File "/gold/backend/services/order_service.py", line 412, in update_future_price
    return Order(**updated_order)
  File "/usr/local/lib/python3.10/dist-packages/pydantic/main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for Order
open_basis
  Value error, 开仓基差(-2.089999999999918)与计算值(-5.07)不匹配 [type=value_error, input_value=-2.089999999999918, input_type=float]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/gold/backend/services/trade_service.py", line 926, in open_position
    await order_service.cancel_order(order.id, error_message)
  File "/gold/backend/services/order_service.py", line 482, in cancel_order
    self.postgres_client.update("orders", data, "id = %s", (order_id,))
  File "/gold/backend/data_storage/postgres_client.py", line 290, in update
    cur.execute(query, values)
psycopg2.errors.StringDataRightTruncation: value too long for type character varying(200)


2025-05-30 01:07:57,002 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 01:11:32,689 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 01:11:35,465 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 01:11:58,983 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 01:12:01,744 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 01:15:45,163 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530010727880650
2025-05-30 01:15:45,164 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 01:15:45,165 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530010727880650, 用户ID: USR337855A0
2025-05-30 01:15:45,165 - trade_service_actual - INFO - [SPOT:f15bb124] [现货交易开始] 账户=SPOT337855A0, 方向=卖出, 参考价格=769.31, 数量=1000克
2025-05-30 01:15:45,165 - trade_service_actual - INFO - [f15bb124] 开始执行卖出操作: 方向=卖出, 价格=769.31, 数量=1000克
2025-05-30 01:15:45,165 - trade_service_actual - INFO - [SPOT:f15bb124] 开始获取现货服务实例
2025-05-30 01:15:45,165 - trade_service_actual - INFO - [SPOT:f15bb124] 成功获取现货服务实例，登录状态: True
2025-05-30 01:15:45,165 - trade_service_actual - FATAL - [SPOT:f15bb124] 准备执行卖出操作，数量=1000克
2025-05-30 01:15:45,165 - trade_service_actual - FATAL - [SPOT:f15bb124] 调用spot_service.place_order: direction=-1, price=769.31, amount=1000, max_slippage=0.2
2025-05-30 01:15:52,771 - trade_service_actual - FATAL - [SPOT:f15bb124] spot_service.place_order返回结果: {'success': True, 'message': '下单成功', 'order_id': 'B2025053001154628029', 'direction': -1, 'price': 769.31, 'amount': 1000, 'execution_time': 7.605743408203125}
2025-05-30 01:15:52,771 - trade_service_actual - FATAL - [SPOT:f15bb124] 现货交易成功: 订单ID=B2025053001154628029, 实际价格=769.31, 耗时=7.61秒
2025-05-30 01:15:52,771 - trade_service_actual - INFO - 现货交易成功: 账户=SPOT337855A0, 方向=卖出, 参考价格=769.31, 实际价格=769.31, 数量=1000克, 订单ID=B2025053001154628029
2025-05-30 01:15:52,771 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530010727880650: {'success': True, 'message': '现货交易成功', 'order_id': 'B2025053001154628029', 'price': 769.31, 'execution_time': 7.606328964233398}
2025-05-30 01:15:52,772 - trade_service_actual - INFO - [TradeService] 现货平仓成功，开始执行期货平仓交易，订单ID: ORD20250530010727880650
2025-05-30 01:15:52,772 - trade_service_actual - INFO - 执行期货平仓，现货已成功，期货使用市价单确保100%成交
2025-05-30 01:15:52,772 - trade_service_actual - INFO - [FUTURE:fd2c8e24] [期货平仓开始] 账户=FUTURE337855A0, 方向=买入, 价格=771.65, 数量=1000克
2025-05-30 01:15:52,772 - trade_service_actual - INFO - [FUTURE:fd2c8e24] 使用市价单执行买入操作，数量=1000克
2025-05-30 01:15:52,772 - trade_service_actual - INFO - [FUTURE:fd2c8e24] 现货已成功，期货采用市价单策略确保100%成交，避免撤单
2025-05-30 01:15:52,927 - trade_service_actual - INFO - [FUTURE:fd2c8e24] 期货交易成功: 订单ID=PYSDK_insert_b8996b9b0905aa8ac4750e85b9f851c2, 实际价格=nan, 耗时=0.16秒
2025-05-30 01:15:52,927 - trade_service_actual - INFO - 期货交易成功: 账户=FUTURE337855A0, 方向=买入, 参考价格=771.65, 实际价格=nan, 数量=1000克, 订单ID=PYSDK_insert_b8996b9b0905aa8ac4750e85b9f851c2
2025-05-30 01:15:52,927 - trade_service_actual - INFO - 期货平仓成功: 期货交易成功
2025-05-30 01:15:52,927 - trade_service_actual - INFO - [TradeService] 期货平仓交易结果，订单ID ORD20250530010727880650: {'success': True, 'message': '期货交易成功', 'order_id': 'PYSDK_insert_b8996b9b0905aa8ac4750e85b9f851c2', 'price': nan, 'execution_time': 0.1551518440246582}
2025-05-30 01:15:52,931 - trade_service_actual - INFO - 平仓成功: 订单ID=ORD20250530010727880650, 用户ID=USR337855A0, 方向=-1, 现货平仓价格=769.31, 期货平仓价格=771.65, 平仓基差=-2.340000000000032, 总盈亏=2729.9999999999045, 净利润=2319.9999999999045
2025-05-30 01:15:52,931 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 01:41:20,164 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 01:41:23,216 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 01:43:55,537 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 01:43:58,340 - trade_service_actual - INFO - 交易服务初始化完成
