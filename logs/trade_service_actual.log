2025-05-30 13:31:56,645 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:31:59,680 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:32:01,329 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:32:02,154 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:32:04,153 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:32:06,025 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:33:04,526 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:33:06,153 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:33:08,926 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:36:34,468 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530021730543492
2025-05-30 13:36:34,469 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:36:34,470 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530021730543492, 用户ID: USR337855A0
2025-05-30 13:36:34,470 - trade_service_actual - INFO - [SPOT_CLOSE:976cd859] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:36:34,470 - trade_service_actual - INFO - [SPOT_CLOSE:976cd859] 成功获取现货服务实例，登录状态: True
2025-05-30 13:36:34,470 - trade_service_actual - FATAL - [SPOT_CLOSE:976cd859] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:36:34,470 - trade_service_actual - FATAL - [SPOT_CLOSE:976cd859] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:36:39,515 - trade_service_actual - FATAL - [SPOT_CLOSE:976cd859] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:36:39,515 - trade_service_actual - ERROR - [SPOT_CLOSE:976cd859] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.05秒
2025-05-30 13:36:39,515 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:36:39,516 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 未找到订单: S2025053002173230988
2025-05-30 13:36:41,516 - trade_service_actual - INFO - [SPOT_CLOSE:b4f477d4] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:36:41,517 - trade_service_actual - INFO - [SPOT_CLOSE:b4f477d4] 成功获取现货服务实例，登录状态: True
2025-05-30 13:36:41,517 - trade_service_actual - FATAL - [SPOT_CLOSE:b4f477d4] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:36:41,517 - trade_service_actual - FATAL - [SPOT_CLOSE:b4f477d4] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:36:46,545 - trade_service_actual - FATAL - [SPOT_CLOSE:b4f477d4] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:36:46,546 - trade_service_actual - ERROR - [SPOT_CLOSE:b4f477d4] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.03秒
2025-05-30 13:36:46,546 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:36:46,546 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 未找到订单: S2025053002173230988
2025-05-30 13:36:48,546 - trade_service_actual - INFO - [SPOT_CLOSE:2e9adbb6] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:36:48,547 - trade_service_actual - INFO - [SPOT_CLOSE:2e9adbb6] 成功获取现货服务实例，登录状态: True
2025-05-30 13:36:48,547 - trade_service_actual - FATAL - [SPOT_CLOSE:2e9adbb6] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:36:48,547 - trade_service_actual - FATAL - [SPOT_CLOSE:2e9adbb6] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:36:53,575 - trade_service_actual - FATAL - [SPOT_CLOSE:2e9adbb6] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:36:53,575 - trade_service_actual - ERROR - [SPOT_CLOSE:2e9adbb6] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.03秒
2025-05-30 13:36:53,575 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:36:53,575 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: S2025053002173230988
2025-05-30 13:36:53,576 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530021730543492: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:36:53,576 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 13:37:09,010 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530021730543492
2025-05-30 13:37:09,012 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:37:09,013 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530021730543492, 用户ID: USR337855A0
2025-05-30 13:37:09,013 - trade_service_actual - INFO - [SPOT_CLOSE:8793d454] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:37:09,013 - trade_service_actual - INFO - [SPOT_CLOSE:8793d454] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:09,013 - trade_service_actual - FATAL - [SPOT_CLOSE:8793d454] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:37:09,014 - trade_service_actual - FATAL - [SPOT_CLOSE:8793d454] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:37:14,046 - trade_service_actual - FATAL - [SPOT_CLOSE:8793d454] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:37:14,046 - trade_service_actual - ERROR - [SPOT_CLOSE:8793d454] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.03秒
2025-05-30 13:37:14,046 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:37:14,046 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 未找到订单: S2025053002173230988
2025-05-30 13:37:16,047 - trade_service_actual - INFO - [SPOT_CLOSE:7a7fa99c] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:37:16,048 - trade_service_actual - INFO - [SPOT_CLOSE:7a7fa99c] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:16,048 - trade_service_actual - FATAL - [SPOT_CLOSE:7a7fa99c] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:37:16,048 - trade_service_actual - FATAL - [SPOT_CLOSE:7a7fa99c] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:37:21,077 - trade_service_actual - FATAL - [SPOT_CLOSE:7a7fa99c] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:37:21,078 - trade_service_actual - ERROR - [SPOT_CLOSE:7a7fa99c] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.03秒
2025-05-30 13:37:21,078 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:37:21,078 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 未找到订单: S2025053002173230988
2025-05-30 13:37:23,137 - trade_service_actual - INFO - [SPOT_CLOSE:a5bd5765] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:37:23,137 - trade_service_actual - INFO - [SPOT_CLOSE:a5bd5765] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:23,137 - trade_service_actual - FATAL - [SPOT_CLOSE:a5bd5765] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:37:23,137 - trade_service_actual - FATAL - [SPOT_CLOSE:a5bd5765] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:37:28,168 - trade_service_actual - FATAL - [SPOT_CLOSE:a5bd5765] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:37:28,168 - trade_service_actual - ERROR - [SPOT_CLOSE:a5bd5765] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.03秒
2025-05-30 13:37:28,168 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:37:28,168 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: S2025053002173230988
2025-05-30 13:37:28,168 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530021730543492: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:37:28,168 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 13:37:37,957 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530021730539173
2025-05-30 13:37:37,959 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:37:37,961 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530021730539173, 用户ID: USR337855A0
2025-05-30 13:37:37,961 - trade_service_actual - INFO - [SPOT_CLOSE:fba44df6] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 13:37:37,961 - trade_service_actual - INFO - [SPOT_CLOSE:fba44df6] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:37,961 - trade_service_actual - FATAL - [SPOT_CLOSE:fba44df6] 准备执行现货平仓操作，订单ID=None
2025-05-30 13:37:37,961 - trade_service_actual - FATAL - [SPOT_CLOSE:fba44df6] 调用spot_service.close_position: order_id=None
2025-05-30 13:37:42,993 - trade_service_actual - FATAL - [SPOT_CLOSE:fba44df6] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 13:37:42,993 - trade_service_actual - ERROR - [SPOT_CLOSE:fba44df6] 现货平仓失败: 未找到订单: None, 耗时=5.03秒
2025-05-30 13:37:42,993 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 13:37:42,993 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 未找到订单: None
2025-05-30 13:37:44,994 - trade_service_actual - INFO - [SPOT_CLOSE:c4da8519] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 13:37:44,994 - trade_service_actual - INFO - [SPOT_CLOSE:c4da8519] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:44,994 - trade_service_actual - FATAL - [SPOT_CLOSE:c4da8519] 准备执行现货平仓操作，订单ID=None
2025-05-30 13:37:44,994 - trade_service_actual - FATAL - [SPOT_CLOSE:c4da8519] 调用spot_service.close_position: order_id=None
2025-05-30 13:37:50,026 - trade_service_actual - FATAL - [SPOT_CLOSE:c4da8519] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 13:37:50,027 - trade_service_actual - ERROR - [SPOT_CLOSE:c4da8519] 现货平仓失败: 未找到订单: None, 耗时=5.03秒
2025-05-30 13:37:50,027 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 13:37:50,027 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 未找到订单: None
2025-05-30 13:37:52,027 - trade_service_actual - INFO - [SPOT_CLOSE:2a9e2cda] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 13:37:52,027 - trade_service_actual - INFO - [SPOT_CLOSE:2a9e2cda] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:52,028 - trade_service_actual - FATAL - [SPOT_CLOSE:2a9e2cda] 准备执行现货平仓操作，订单ID=None
2025-05-30 13:37:52,028 - trade_service_actual - FATAL - [SPOT_CLOSE:2a9e2cda] 调用spot_service.close_position: order_id=None
2025-05-30 13:37:57,087 - trade_service_actual - FATAL - [SPOT_CLOSE:2a9e2cda] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 13:37:57,087 - trade_service_actual - ERROR - [SPOT_CLOSE:2a9e2cda] 现货平仓失败: 未找到订单: None, 耗时=5.06秒
2025-05-30 13:37:57,087 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 13:37:57,087 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: None
2025-05-30 13:37:57,087 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530021730539173: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:37:57,087 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 13:40:04,965 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:40:07,734 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:40:31,282 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:40:34,065 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:40:57,627 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:41:00,385 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:41:22,524 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530021730543492
2025-05-30 13:41:22,531 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:41:22,532 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530021730543492, 用户ID: USR337855A0
2025-05-30 13:41:22,532 - trade_service_actual - INFO - [SPOT_CLOSE:c06d3899] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:41:22,532 - trade_service_actual - INFO - [SPOT_CLOSE:c06d3899] 成功获取现货服务实例，登录状态: True
2025-05-30 13:41:22,532 - trade_service_actual - FATAL - [SPOT_CLOSE:c06d3899] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:41:22,532 - trade_service_actual - FATAL - [SPOT_CLOSE:c06d3899] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:41:27,574 - trade_service_actual - FATAL - [SPOT_CLOSE:c06d3899] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:41:27,574 - trade_service_actual - ERROR - [SPOT_CLOSE:c06d3899] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.04秒
2025-05-30 13:41:27,574 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:41:27,574 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 未找到订单: S2025053002173230988
2025-05-30 13:41:29,575 - trade_service_actual - INFO - [SPOT_CLOSE:7f0c7485] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:41:29,576 - trade_service_actual - INFO - [SPOT_CLOSE:7f0c7485] 成功获取现货服务实例，登录状态: True
2025-05-30 13:41:29,576 - trade_service_actual - FATAL - [SPOT_CLOSE:7f0c7485] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:41:29,576 - trade_service_actual - FATAL - [SPOT_CLOSE:7f0c7485] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:41:34,652 - trade_service_actual - FATAL - [SPOT_CLOSE:7f0c7485] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:41:34,653 - trade_service_actual - ERROR - [SPOT_CLOSE:7f0c7485] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.08秒
2025-05-30 13:41:34,653 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:41:34,653 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 未找到订单: S2025053002173230988
2025-05-30 13:41:36,654 - trade_service_actual - INFO - [SPOT_CLOSE:86167460] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:41:36,654 - trade_service_actual - INFO - [SPOT_CLOSE:86167460] 成功获取现货服务实例，登录状态: True
2025-05-30 13:41:36,654 - trade_service_actual - FATAL - [SPOT_CLOSE:86167460] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:41:36,655 - trade_service_actual - FATAL - [SPOT_CLOSE:86167460] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:41:41,696 - trade_service_actual - FATAL - [SPOT_CLOSE:86167460] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:41:41,696 - trade_service_actual - ERROR - [SPOT_CLOSE:86167460] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.04秒
2025-05-30 13:41:41,696 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:41:41,696 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: S2025053002173230988
2025-05-30 13:41:41,697 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530021730543492: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:41:41,697 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 13:43:32,068 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:43:34,867 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:44:17,770 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:44:20,551 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:44:44,060 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:44:46,825 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:45:10,444 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:45:13,201 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:49:16,649 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:49:19,446 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:49:43,093 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:49:45,857 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:50:09,474 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:50:12,241 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:50:42,883 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:50:45,679 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:51:50,910 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:51:53,699 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:59:13,285 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:59:16,055 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:59:49,222 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:59:52,010 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:00:15,897 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:00:18,660 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:00:42,266 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:00:45,051 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:01:15,819 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:01:18,579 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:02:46,677 - trade_service_actual - FATAL - 开始执行开仓操作: user_id=USR337855A0, direction=1
2025-05-30 14:02:46,677 - trade_service_actual - ERROR - 开仓请求接收: user_id=USR337855A0, direction=1
2025-05-30 14:02:46,677 - trade_service_actual - WARNING - 处理开仓请求: user_id=USR337855A0, direction=1
2025-05-30 14:02:46,677 - trade_service_actual - INFO - 开仓请求: user_id=USR337855A0, direction=1
2025-05-30 14:02:46,677 - trade_service_actual - DEBUG - 开仓请求详情: user_id=USR337855A0, direction=1, 时间戳=2025-05-30T14:02:46.677383
2025-05-30 14:02:46,678 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 14:02:46,678 - trade_service_actual - INFO - 获取到用户账户 - 现货账户ID: SPOT337855A0, 期货账户ID: FUTURE337855A0
2025-05-30 14:02:46,680 - trade_service_actual - INFO - [TradeService] 优先执行现货交易，订单ID: ORD20250530140246678506, 用户ID: USR337855A0
2025-05-30 14:02:46,680 - trade_service_actual - INFO - [SPOT:343eb21c] [现货交易开始] 账户=SPOT337855A0, 方向=卖出, 参考价格=766.1, 数量=1000克
2025-05-30 14:02:46,680 - trade_service_actual - INFO - [343eb21c] 开始执行卖出操作: 方向=卖出, 价格=766.1, 数量=1000克
2025-05-30 14:02:46,680 - trade_service_actual - INFO - [SPOT:343eb21c] 开始获取现货服务实例
2025-05-30 14:02:46,681 - trade_service_actual - INFO - [SPOT:343eb21c] 成功获取现货服务实例，登录状态: True
2025-05-30 14:02:46,681 - trade_service_actual - FATAL - [SPOT:343eb21c] 准备执行卖出操作，数量=1000克
2025-05-30 14:02:46,681 - trade_service_actual - FATAL - [SPOT:343eb21c] 调用spot_service.place_order: direction=-1, price=766.1, amount=1000, max_slippage=0.2
2025-05-30 14:02:54,308 - trade_service_actual - FATAL - [SPOT:343eb21c] spot_service.place_order返回结果: {'success': True, 'message': '下单成功', 'order_id': 'B2025053014024825524', 'direction': -1, 'price': 766.1, 'amount': 1000, 'execution_time': 7.626984357833862}
2025-05-30 14:02:54,308 - trade_service_actual - FATAL - [SPOT:343eb21c] 现货交易成功: 订单ID=B2025053014024825524, 实际价格=766.1, 耗时=7.63秒
2025-05-30 14:02:54,308 - trade_service_actual - INFO - 现货交易成功: 账户=SPOT337855A0, 方向=卖出, 参考价格=766.1, 实际价格=766.1, 数量=1000克, 订单ID=B2025053014024825524
2025-05-30 14:02:54,308 - trade_service_actual - INFO - [TradeService] 现货交易结果，订单ID ORD20250530140246678506: {'success': True, 'message': '现货交易成功', 'order_id': 'B2025053014024825524', 'price': 766.1, 'execution_time': 7.627654075622559}
2025-05-30 14:02:54,311 - trade_service_actual - INFO - [TradeService] 现货交易成功，开始执行期货交易，订单ID: ORD20250530140246678506
2025-05-30 14:02:54,311 - trade_service_actual - INFO - 执行期货开仓，现货已成功，期货使用市价单确保100%成交
2025-05-30 14:02:54,311 - trade_service_actual - INFO - [FUTURE:027ab8f5] [期货开仓开始] 账户=FUTURE337855A0, 方向=买入, 价格=771.65, 数量=1000克
2025-05-30 14:02:54,311 - trade_service_actual - INFO - [FUTURE:027ab8f5] 使用市价单执行买入操作，数量=1000克
2025-05-30 14:02:54,311 - trade_service_actual - INFO - [FUTURE:027ab8f5] 现货已成功，期货采用市价单策略确保100%成交，避免撤单
2025-05-30 14:02:54,425 - trade_service_actual - INFO - [FUTURE:027ab8f5] 期货交易成功: 订单ID=PYSDK_insert_1ac0679e39faa9ab79fbd7364f4c9f2d, 实际价格=770.78, 耗时=0.11秒
2025-05-30 14:02:54,425 - trade_service_actual - INFO - 期货交易成功: 账户=FUTURE337855A0, 方向=买入, 参考价格=771.65, 实际价格=770.78, 数量=1000克, 订单ID=PYSDK_insert_1ac0679e39faa9ab79fbd7364f4c9f2d
2025-05-30 14:02:54,425 - trade_service_actual - INFO - 期货开仓成功: 期货交易成功
2025-05-30 14:02:54,425 - trade_service_actual - INFO - [TradeService] 期货交易结果，订单ID ORD20250530140246678506: {'success': True, 'message': '期货交易成功', 'order_id': 'PYSDK_insert_1ac0679e39faa9ab79fbd7364f4c9f2d', 'price': 770.78, 'execution_time': 0.11383390426635742}
2025-05-30 14:02:54,427 - trade_service_actual - INFO - [TradeService] 更新期货实际成交价格: 770.78，原价格: 771.65
2025-05-30 14:02:54,430 - trade_service_actual - INFO - 开仓成功: 订单ID=ORD20250530140246678506, 用户ID=USR337855A0, 方向=1, 现货价格=766.1, 期货价格=770.78, 基差=-4.67999999999995, 目标基差=-4.5499999999999545
2025-05-30 14:02:54,431 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 14:26:17,543 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:27:15,162 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:27:16,966 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:27:19,900 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:34:12,715 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:34:14,376 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:34:17,136 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:35:19,988 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530140246678506
2025-05-30 14:35:19,989 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 14:35:19,990 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530140246678506, 用户ID: USR337855A0
2025-05-30 14:35:19,990 - trade_service_actual - INFO - [SPOT_CLOSE:6aec85ad] [现货平仓开始] 账户=SPOT337855A0, 订单ID=B2025053014024825524
2025-05-30 14:35:19,990 - trade_service_actual - INFO - [SPOT_CLOSE:6aec85ad] 成功获取现货服务实例，登录状态: True
2025-05-30 14:35:19,990 - trade_service_actual - FATAL - [SPOT_CLOSE:6aec85ad] 准备执行现货平仓操作，订单ID=B2025053014024825524
2025-05-30 14:35:19,990 - trade_service_actual - FATAL - [SPOT_CLOSE:6aec85ad] 调用spot_service.close_position: order_id=B2025053014024825524
2025-05-30 14:35:37,145 - trade_service_actual - FATAL - [SPOT_CLOSE:6aec85ad] spot_service.close_position返回结果: {'success': False, 'message': '等待平仓弹窗出现超时'}
2025-05-30 14:35:37,145 - trade_service_actual - ERROR - [SPOT_CLOSE:6aec85ad] 现货平仓失败: 等待平仓弹窗出现超时, 耗时=17.15秒
2025-05-30 14:35:37,145 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=B2025053014024825524, 错误=等待平仓弹窗出现超时
2025-05-30 14:35:37,145 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 等待平仓弹窗出现超时
2025-05-30 14:35:39,146 - trade_service_actual - INFO - [SPOT_CLOSE:0fd7c4aa] [现货平仓开始] 账户=SPOT337855A0, 订单ID=B2025053014024825524
2025-05-30 14:35:39,146 - trade_service_actual - INFO - [SPOT_CLOSE:0fd7c4aa] 成功获取现货服务实例，登录状态: True
2025-05-30 14:35:39,146 - trade_service_actual - FATAL - [SPOT_CLOSE:0fd7c4aa] 准备执行现货平仓操作，订单ID=B2025053014024825524
2025-05-30 14:35:39,146 - trade_service_actual - FATAL - [SPOT_CLOSE:0fd7c4aa] 调用spot_service.close_position: order_id=B2025053014024825524
2025-05-30 14:35:56,284 - trade_service_actual - FATAL - [SPOT_CLOSE:0fd7c4aa] spot_service.close_position返回结果: {'success': False, 'message': '等待平仓弹窗出现超时'}
2025-05-30 14:35:56,285 - trade_service_actual - ERROR - [SPOT_CLOSE:0fd7c4aa] 现货平仓失败: 等待平仓弹窗出现超时, 耗时=17.14秒
2025-05-30 14:35:56,285 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=B2025053014024825524, 错误=等待平仓弹窗出现超时
2025-05-30 14:35:56,285 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 等待平仓弹窗出现超时
2025-05-30 14:35:58,285 - trade_service_actual - INFO - [SPOT_CLOSE:29da85ad] [现货平仓开始] 账户=SPOT337855A0, 订单ID=B2025053014024825524
2025-05-30 14:35:58,286 - trade_service_actual - INFO - [SPOT_CLOSE:29da85ad] 成功获取现货服务实例，登录状态: True
2025-05-30 14:35:58,286 - trade_service_actual - FATAL - [SPOT_CLOSE:29da85ad] 准备执行现货平仓操作，订单ID=B2025053014024825524
2025-05-30 14:35:58,286 - trade_service_actual - FATAL - [SPOT_CLOSE:29da85ad] 调用spot_service.close_position: order_id=B2025053014024825524
2025-05-30 14:36:15,417 - trade_service_actual - FATAL - [SPOT_CLOSE:29da85ad] spot_service.close_position返回结果: {'success': False, 'message': '等待平仓弹窗出现超时'}
2025-05-30 14:36:15,417 - trade_service_actual - ERROR - [SPOT_CLOSE:29da85ad] 现货平仓失败: 等待平仓弹窗出现超时, 耗时=17.13秒
2025-05-30 14:36:15,417 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=B2025053014024825524, 错误=等待平仓弹窗出现超时
2025-05-30 14:36:15,417 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 等待平仓弹窗出现超时
2025-05-30 14:36:15,418 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530140246678506: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 14:36:15,418 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 14:38:16,900 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:38:19,697 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:38:43,304 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:38:46,116 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:43:56,029 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:44:15,162 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:44:16,797 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:44:19,565 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:47:35,782 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530140246678506
2025-05-30 14:47:35,783 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 14:47:35,784 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530140246678506, 用户ID: USR337855A0
2025-05-30 14:47:35,784 - trade_service_actual - INFO - [SPOT_CLOSE:e393ddeb] [现货平仓开始] 账户=SPOT337855A0, 订单ID=B2025053014024825524
2025-05-30 14:47:35,784 - trade_service_actual - INFO - [SPOT_CLOSE:e393ddeb] 成功获取现货服务实例，登录状态: True
2025-05-30 14:47:35,784 - trade_service_actual - FATAL - [SPOT_CLOSE:e393ddeb] 准备执行现货平仓操作，订单ID=B2025053014024825524
2025-05-30 14:47:35,784 - trade_service_actual - FATAL - [SPOT_CLOSE:e393ddeb] 调用spot_service.close_position: order_id=B2025053014024825524
2025-05-30 14:48:12,973 - trade_service_actual - FATAL - [SPOT_CLOSE:e393ddeb] spot_service.close_position返回结果: {'success': False, 'message': '处理平仓弹窗失败: ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n    - retrying click action\n      - waiting 100ms\n    58 × waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n     - retrying click action\n       - waiting 500ms\n'}
2025-05-30 14:48:12,973 - trade_service_actual - ERROR - [SPOT_CLOSE:e393ddeb] 现货平仓失败: 处理平仓弹窗失败: ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    58 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms
, 耗时=37.19秒
2025-05-30 14:48:12,973 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=B2025053014024825524, 错误=处理平仓弹窗失败: ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    58 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-05-30 14:48:12,973 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 处理平仓弹窗失败: ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    58 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-05-30 14:48:14,974 - trade_service_actual - INFO - [SPOT_CLOSE:7a5caf71] [现货平仓开始] 账户=SPOT337855A0, 订单ID=B2025053014024825524
2025-05-30 14:48:14,974 - trade_service_actual - INFO - [SPOT_CLOSE:7a5caf71] 成功获取现货服务实例，登录状态: True
2025-05-30 14:48:14,974 - trade_service_actual - FATAL - [SPOT_CLOSE:7a5caf71] 准备执行现货平仓操作，订单ID=B2025053014024825524
2025-05-30 14:48:14,974 - trade_service_actual - FATAL - [SPOT_CLOSE:7a5caf71] 调用spot_service.close_position: order_id=B2025053014024825524
2025-05-30 14:48:43,169 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530140246674649
2025-05-30 14:48:43,171 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 14:48:43,171 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530140246674649, 用户ID: USR337855A0
2025-05-30 14:48:43,172 - trade_service_actual - INFO - [SPOT_CLOSE:5542cea3] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 14:48:43,172 - trade_service_actual - INFO - [SPOT_CLOSE:5542cea3] 成功获取现货服务实例，登录状态: True
2025-05-30 14:48:43,172 - trade_service_actual - FATAL - [SPOT_CLOSE:5542cea3] 准备执行现货平仓操作，订单ID=None
2025-05-30 14:48:43,172 - trade_service_actual - FATAL - [SPOT_CLOSE:5542cea3] 调用spot_service.close_position: order_id=None
2025-05-30 14:48:43,301 - trade_service_actual - FATAL - [SPOT_CLOSE:7a5caf71] spot_service.close_position返回结果: {'success': False, 'message': '处理平仓弹窗失败: ElementHandle.click: Element is not attached to the DOM\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n    - retrying click action\n      - waiting 100ms\n    40 × waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n     - retrying click action\n       - waiting 500ms\n    - waiting for" https://j.jtd9999.vip/h5/" navigation to finish...\n    - navigated to "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"\n    - waiting for element to be visible, enabled and stable\n'}
2025-05-30 14:48:43,301 - trade_service_actual - ERROR - [SPOT_CLOSE:7a5caf71] 现货平仓失败: 处理平仓弹窗失败: ElementHandle.click: Element is not attached to the DOM
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    40 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms
    - waiting for" https://j.jtd9999.vip/h5/" navigation to finish...
    - navigated to "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"
    - waiting for element to be visible, enabled and stable
, 耗时=28.33秒
2025-05-30 14:48:43,302 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=B2025053014024825524, 错误=处理平仓弹窗失败: ElementHandle.click: Element is not attached to the DOM
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    40 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms
    - waiting for" https://j.jtd9999.vip/h5/" navigation to finish...
    - navigated to "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"
    - waiting for element to be visible, enabled and stable

2025-05-30 14:48:43,302 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 处理平仓弹窗失败: ElementHandle.click: Element is not attached to the DOM
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    40 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms
    - waiting for" https://j.jtd9999.vip/h5/" navigation to finish...
    - navigated to "https://j.jtd9999.vip/h5/#/pages/order/myorder?demp_code=944440b68743bdaaf6e4cf7b5745893e"
    - waiting for element to be visible, enabled and stable

2025-05-30 14:48:45,302 - trade_service_actual - INFO - [SPOT_CLOSE:1ce2a320] [现货平仓开始] 账户=SPOT337855A0, 订单ID=B2025053014024825524
2025-05-30 14:48:45,303 - trade_service_actual - INFO - [SPOT_CLOSE:1ce2a320] 成功获取现货服务实例，登录状态: True
2025-05-30 14:48:45,303 - trade_service_actual - FATAL - [SPOT_CLOSE:1ce2a320] 准备执行现货平仓操作，订单ID=B2025053014024825524
2025-05-30 14:48:45,303 - trade_service_actual - FATAL - [SPOT_CLOSE:1ce2a320] 调用spot_service.close_position: order_id=B2025053014024825524
2025-05-30 14:48:48,221 - trade_service_actual - FATAL - [SPOT_CLOSE:5542cea3] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 14:48:48,221 - trade_service_actual - ERROR - [SPOT_CLOSE:5542cea3] 现货平仓失败: 未找到订单: None, 耗时=5.05秒
2025-05-30 14:48:48,221 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 14:48:48,221 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 未找到订单: None
2025-05-30 14:48:50,222 - trade_service_actual - INFO - [SPOT_CLOSE:7fb08661] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 14:48:50,223 - trade_service_actual - INFO - [SPOT_CLOSE:7fb08661] 成功获取现货服务实例，登录状态: True
2025-05-30 14:48:50,223 - trade_service_actual - FATAL - [SPOT_CLOSE:7fb08661] 准备执行现货平仓操作，订单ID=None
2025-05-30 14:48:50,223 - trade_service_actual - FATAL - [SPOT_CLOSE:7fb08661] 调用spot_service.close_position: order_id=None
2025-05-30 14:48:50,398 - trade_service_actual - FATAL - [SPOT_CLOSE:1ce2a320] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: B2025053014024825524', 'order_id': 'B2025053014024825524'}
2025-05-30 14:48:50,398 - trade_service_actual - ERROR - [SPOT_CLOSE:1ce2a320] 现货平仓失败: 未找到订单: B2025053014024825524, 耗时=5.10秒
2025-05-30 14:48:50,398 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=B2025053014024825524, 错误=未找到订单: B2025053014024825524
2025-05-30 14:48:50,398 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: B2025053014024825524
2025-05-30 14:48:50,398 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530140246678506: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 14:48:50,398 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 14:48:55,353 - trade_service_actual - FATAL - [SPOT_CLOSE:7fb08661] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 14:48:55,353 - trade_service_actual - ERROR - [SPOT_CLOSE:7fb08661] 现货平仓失败: 未找到订单: None, 耗时=5.13秒
2025-05-30 14:48:55,354 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 14:48:55,354 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 未找到订单: None
2025-05-30 14:48:57,355 - trade_service_actual - INFO - [SPOT_CLOSE:0f1e607e] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 14:48:57,356 - trade_service_actual - INFO - [SPOT_CLOSE:0f1e607e] 成功获取现货服务实例，登录状态: True
2025-05-30 14:48:57,356 - trade_service_actual - FATAL - [SPOT_CLOSE:0f1e607e] 准备执行现货平仓操作，订单ID=None
2025-05-30 14:48:57,356 - trade_service_actual - FATAL - [SPOT_CLOSE:0f1e607e] 调用spot_service.close_position: order_id=None
2025-05-30 14:49:02,430 - trade_service_actual - FATAL - [SPOT_CLOSE:0f1e607e] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 14:49:02,431 - trade_service_actual - ERROR - [SPOT_CLOSE:0f1e607e] 现货平仓失败: 未找到订单: None, 耗时=5.08秒
2025-05-30 14:49:02,431 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 14:49:02,431 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: None
2025-05-30 14:49:02,431 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530140246674649: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 14:49:02,432 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 14:50:47,669 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:50:50,444 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:51:13,954 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:51:16,729 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:51:40,188 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:51:42,978 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:53:58,757 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:54:00,425 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:54:03,168 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:54:40,855 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:54:42,505 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 14:54:45,263 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 14:58:08,375 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530140246678506
2025-05-30 14:58:08,377 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 14:58:08,377 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530140246678506, 用户ID: USR337855A0
2025-05-30 14:58:08,377 - trade_service_actual - INFO - [SPOT_CLOSE:fd841f50] [现货平仓开始] 账户=SPOT337855A0, 订单ID=B2025053014024825524
2025-05-30 14:58:08,378 - trade_service_actual - INFO - [SPOT_CLOSE:fd841f50] 成功获取现货服务实例，登录状态: True
2025-05-30 14:58:08,378 - trade_service_actual - FATAL - [SPOT_CLOSE:fd841f50] 准备执行现货平仓操作，订单ID=B2025053014024825524
2025-05-30 14:58:08,378 - trade_service_actual - FATAL - [SPOT_CLOSE:fd841f50] 调用spot_service.close_position: order_id=B2025053014024825524
2025-05-30 14:58:20,887 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530140246674649
2025-05-30 14:58:20,889 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 14:58:20,890 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530140246674649, 用户ID: USR337855A0
2025-05-30 14:58:20,890 - trade_service_actual - INFO - [SPOT_CLOSE:41b150ec] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 14:58:20,890 - trade_service_actual - INFO - [SPOT_CLOSE:41b150ec] 成功获取现货服务实例，登录状态: True
2025-05-30 14:58:20,890 - trade_service_actual - FATAL - [SPOT_CLOSE:41b150ec] 准备执行现货平仓操作，订单ID=None
2025-05-30 14:58:20,890 - trade_service_actual - FATAL - [SPOT_CLOSE:41b150ec] 调用spot_service.close_position: order_id=None
2025-05-30 14:58:20,924 - trade_service_actual - FATAL - [SPOT_CLOSE:fd841f50] spot_service.close_position返回结果: {'success': False, 'message': '处理平仓弹窗失败: ElementHandle.click: Element is not attached to the DOM\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n    - retrying click action\n      - waiting 100ms\n    10 × waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n     - retrying click action\n       - waiting 500ms\n'}
2025-05-30 14:58:20,925 - trade_service_actual - ERROR - [SPOT_CLOSE:fd841f50] 现货平仓失败: 处理平仓弹窗失败: ElementHandle.click: Element is not attached to the DOM
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    10 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms
, 耗时=12.55秒
2025-05-30 14:58:20,926 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=B2025053014024825524, 错误=处理平仓弹窗失败: ElementHandle.click: Element is not attached to the DOM
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    10 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-05-30 14:58:20,926 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 处理平仓弹窗失败: ElementHandle.click: Element is not attached to the DOM
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    10 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-05-30 14:58:22,926 - trade_service_actual - INFO - [SPOT_CLOSE:6faec8c2] [现货平仓开始] 账户=SPOT337855A0, 订单ID=B2025053014024825524
2025-05-30 14:58:22,926 - trade_service_actual - INFO - [SPOT_CLOSE:6faec8c2] 成功获取现货服务实例，登录状态: True
2025-05-30 14:58:22,927 - trade_service_actual - FATAL - [SPOT_CLOSE:6faec8c2] 准备执行现货平仓操作，订单ID=B2025053014024825524
2025-05-30 14:58:22,927 - trade_service_actual - FATAL - [SPOT_CLOSE:6faec8c2] 调用spot_service.close_position: order_id=B2025053014024825524
2025-05-30 14:58:25,943 - trade_service_actual - FATAL - [SPOT_CLOSE:41b150ec] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 14:58:25,943 - trade_service_actual - ERROR - [SPOT_CLOSE:41b150ec] 现货平仓失败: 未找到订单: None, 耗时=5.05秒
2025-05-30 14:58:25,943 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 14:58:25,943 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 未找到订单: None
2025-05-30 14:58:27,951 - trade_service_actual - INFO - [SPOT_CLOSE:5e18163e] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 14:58:27,951 - trade_service_actual - INFO - [SPOT_CLOSE:5e18163e] 成功获取现货服务实例，登录状态: True
2025-05-30 14:58:27,951 - trade_service_actual - FATAL - [SPOT_CLOSE:5e18163e] 准备执行现货平仓操作，订单ID=None
2025-05-30 14:58:27,951 - trade_service_actual - FATAL - [SPOT_CLOSE:5e18163e] 调用spot_service.close_position: order_id=None
2025-05-30 14:58:27,989 - trade_service_actual - FATAL - [SPOT_CLOSE:6faec8c2] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: B2025053014024825524', 'order_id': 'B2025053014024825524'}
2025-05-30 14:58:27,989 - trade_service_actual - ERROR - [SPOT_CLOSE:6faec8c2] 现货平仓失败: 未找到订单: B2025053014024825524, 耗时=5.06秒
2025-05-30 14:58:27,989 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=B2025053014024825524, 错误=未找到订单: B2025053014024825524
2025-05-30 14:58:27,989 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 未找到订单: B2025053014024825524
2025-05-30 14:58:29,990 - trade_service_actual - INFO - [SPOT_CLOSE:a084620f] [现货平仓开始] 账户=SPOT337855A0, 订单ID=B2025053014024825524
2025-05-30 14:58:29,990 - trade_service_actual - INFO - [SPOT_CLOSE:a084620f] 成功获取现货服务实例，登录状态: True
2025-05-30 14:58:29,990 - trade_service_actual - FATAL - [SPOT_CLOSE:a084620f] 准备执行现货平仓操作，订单ID=B2025053014024825524
2025-05-30 14:58:29,990 - trade_service_actual - FATAL - [SPOT_CLOSE:a084620f] 调用spot_service.close_position: order_id=B2025053014024825524
2025-05-30 14:58:33,000 - trade_service_actual - FATAL - [SPOT_CLOSE:5e18163e] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 14:58:33,000 - trade_service_actual - ERROR - [SPOT_CLOSE:5e18163e] 现货平仓失败: 未找到订单: None, 耗时=5.05秒
2025-05-30 14:58:33,000 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 14:58:33,000 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 未找到订单: None
2025-05-30 14:58:35,001 - trade_service_actual - INFO - [SPOT_CLOSE:83e69156] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 14:58:35,001 - trade_service_actual - INFO - [SPOT_CLOSE:83e69156] 成功获取现货服务实例，登录状态: True
2025-05-30 14:58:35,001 - trade_service_actual - FATAL - [SPOT_CLOSE:83e69156] 准备执行现货平仓操作，订单ID=None
2025-05-30 14:58:35,001 - trade_service_actual - FATAL - [SPOT_CLOSE:83e69156] 调用spot_service.close_position: order_id=None
2025-05-30 14:58:35,032 - trade_service_actual - FATAL - [SPOT_CLOSE:a084620f] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: B2025053014024825524', 'order_id': 'B2025053014024825524'}
2025-05-30 14:58:35,032 - trade_service_actual - ERROR - [SPOT_CLOSE:a084620f] 现货平仓失败: 未找到订单: B2025053014024825524, 耗时=5.04秒
2025-05-30 14:58:35,032 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=B2025053014024825524, 错误=未找到订单: B2025053014024825524
2025-05-30 14:58:35,032 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: B2025053014024825524
2025-05-30 14:58:35,032 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530140246678506: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 14:58:35,032 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 14:58:40,049 - trade_service_actual - FATAL - [SPOT_CLOSE:83e69156] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 14:58:40,050 - trade_service_actual - ERROR - [SPOT_CLOSE:83e69156] 现货平仓失败: 未找到订单: None, 耗时=5.05秒
2025-05-30 14:58:40,050 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 14:58:40,050 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: None
2025-05-30 14:58:40,050 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530140246674649: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 14:58:40,050 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 14:58:42,296 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530021730543492
2025-05-30 14:58:42,298 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 14:58:42,299 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530021730543492, 用户ID: USR337855A0
2025-05-30 14:58:42,299 - trade_service_actual - INFO - [SPOT_CLOSE:492fb2ad] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 14:58:42,299 - trade_service_actual - INFO - [SPOT_CLOSE:492fb2ad] 成功获取现货服务实例，登录状态: True
2025-05-30 14:58:42,299 - trade_service_actual - FATAL - [SPOT_CLOSE:492fb2ad] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 14:58:42,299 - trade_service_actual - FATAL - [SPOT_CLOSE:492fb2ad] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 14:59:19,554 - trade_service_actual - FATAL - [SPOT_CLOSE:492fb2ad] spot_service.close_position返回结果: {'success': False, 'message': '处理平仓弹窗失败: ElementHandle.click: Timeout 30000ms exceeded.\nCall log:\n  - attempting click action\n    2 × waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n    - retrying click action\n    - waiting 20ms\n    2 × waiting for element to be visible, enabled and stable\n      - element is visible, enabled and stable\n      - scrolling into view if needed\n      - done scrolling\n      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n    - retrying click action\n      - waiting 100ms\n    58 × waiting for element to be visible, enabled and stable\n       - element is visible, enabled and stable\n       - scrolling into view if needed\n       - done scrolling\n       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events\n     - retrying click action\n       - waiting 500ms\n'}
2025-05-30 14:59:19,554 - trade_service_actual - ERROR - [SPOT_CLOSE:492fb2ad] 现货平仓失败: 处理平仓弹窗失败: ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    58 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms
, 耗时=37.26秒
2025-05-30 14:59:19,554 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=处理平仓弹窗失败: ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    58 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-05-30 14:59:19,554 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 处理平仓弹窗失败: ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    58 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <uni-view data-v-0860d320="" class="u-drawer-content u-drawer-center u-drawer-content-visible u-animation-zoom">…</uni-view> from <uni-view class="u-drawer" data-v-0860d320="" data-v-14224f5e="">…</uni-view> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-05-30 14:59:21,555 - trade_service_actual - INFO - [SPOT_CLOSE:b2e9226a] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 14:59:21,555 - trade_service_actual - INFO - [SPOT_CLOSE:b2e9226a] 成功获取现货服务实例，登录状态: True
2025-05-30 14:59:21,556 - trade_service_actual - FATAL - [SPOT_CLOSE:b2e9226a] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 14:59:21,556 - trade_service_actual - FATAL - [SPOT_CLOSE:b2e9226a] 调用spot_service.close_position: order_id=S2025053002173230988
