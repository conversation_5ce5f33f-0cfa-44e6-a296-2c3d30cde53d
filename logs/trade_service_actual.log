2025-05-30 13:31:56,645 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:31:59,680 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:32:01,329 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:32:02,154 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:32:04,153 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:32:06,025 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:33:04,526 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:33:06,153 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:33:08,926 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:36:34,468 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530021730543492
2025-05-30 13:36:34,469 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:36:34,470 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530021730543492, 用户ID: USR337855A0
2025-05-30 13:36:34,470 - trade_service_actual - INFO - [SPOT_CLOSE:976cd859] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:36:34,470 - trade_service_actual - INFO - [SPOT_CLOSE:976cd859] 成功获取现货服务实例，登录状态: True
2025-05-30 13:36:34,470 - trade_service_actual - FATAL - [SPOT_CLOSE:976cd859] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:36:34,470 - trade_service_actual - FATAL - [SPOT_CLOSE:976cd859] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:36:39,515 - trade_service_actual - FATAL - [SPOT_CLOSE:976cd859] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:36:39,515 - trade_service_actual - ERROR - [SPOT_CLOSE:976cd859] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.05秒
2025-05-30 13:36:39,515 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:36:39,516 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 未找到订单: S2025053002173230988
2025-05-30 13:36:41,516 - trade_service_actual - INFO - [SPOT_CLOSE:b4f477d4] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:36:41,517 - trade_service_actual - INFO - [SPOT_CLOSE:b4f477d4] 成功获取现货服务实例，登录状态: True
2025-05-30 13:36:41,517 - trade_service_actual - FATAL - [SPOT_CLOSE:b4f477d4] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:36:41,517 - trade_service_actual - FATAL - [SPOT_CLOSE:b4f477d4] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:36:46,545 - trade_service_actual - FATAL - [SPOT_CLOSE:b4f477d4] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:36:46,546 - trade_service_actual - ERROR - [SPOT_CLOSE:b4f477d4] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.03秒
2025-05-30 13:36:46,546 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:36:46,546 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 未找到订单: S2025053002173230988
2025-05-30 13:36:48,546 - trade_service_actual - INFO - [SPOT_CLOSE:2e9adbb6] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:36:48,547 - trade_service_actual - INFO - [SPOT_CLOSE:2e9adbb6] 成功获取现货服务实例，登录状态: True
2025-05-30 13:36:48,547 - trade_service_actual - FATAL - [SPOT_CLOSE:2e9adbb6] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:36:48,547 - trade_service_actual - FATAL - [SPOT_CLOSE:2e9adbb6] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:36:53,575 - trade_service_actual - FATAL - [SPOT_CLOSE:2e9adbb6] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:36:53,575 - trade_service_actual - ERROR - [SPOT_CLOSE:2e9adbb6] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.03秒
2025-05-30 13:36:53,575 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:36:53,575 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: S2025053002173230988
2025-05-30 13:36:53,576 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530021730543492: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:36:53,576 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 13:37:09,010 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530021730543492
2025-05-30 13:37:09,012 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:37:09,013 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530021730543492, 用户ID: USR337855A0
2025-05-30 13:37:09,013 - trade_service_actual - INFO - [SPOT_CLOSE:8793d454] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:37:09,013 - trade_service_actual - INFO - [SPOT_CLOSE:8793d454] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:09,013 - trade_service_actual - FATAL - [SPOT_CLOSE:8793d454] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:37:09,014 - trade_service_actual - FATAL - [SPOT_CLOSE:8793d454] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:37:14,046 - trade_service_actual - FATAL - [SPOT_CLOSE:8793d454] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:37:14,046 - trade_service_actual - ERROR - [SPOT_CLOSE:8793d454] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.03秒
2025-05-30 13:37:14,046 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:37:14,046 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 未找到订单: S2025053002173230988
2025-05-30 13:37:16,047 - trade_service_actual - INFO - [SPOT_CLOSE:7a7fa99c] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:37:16,048 - trade_service_actual - INFO - [SPOT_CLOSE:7a7fa99c] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:16,048 - trade_service_actual - FATAL - [SPOT_CLOSE:7a7fa99c] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:37:16,048 - trade_service_actual - FATAL - [SPOT_CLOSE:7a7fa99c] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:37:21,077 - trade_service_actual - FATAL - [SPOT_CLOSE:7a7fa99c] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:37:21,078 - trade_service_actual - ERROR - [SPOT_CLOSE:7a7fa99c] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.03秒
2025-05-30 13:37:21,078 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:37:21,078 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 未找到订单: S2025053002173230988
2025-05-30 13:37:23,137 - trade_service_actual - INFO - [SPOT_CLOSE:a5bd5765] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:37:23,137 - trade_service_actual - INFO - [SPOT_CLOSE:a5bd5765] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:23,137 - trade_service_actual - FATAL - [SPOT_CLOSE:a5bd5765] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:37:23,137 - trade_service_actual - FATAL - [SPOT_CLOSE:a5bd5765] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:37:28,168 - trade_service_actual - FATAL - [SPOT_CLOSE:a5bd5765] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:37:28,168 - trade_service_actual - ERROR - [SPOT_CLOSE:a5bd5765] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.03秒
2025-05-30 13:37:28,168 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:37:28,168 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: S2025053002173230988
2025-05-30 13:37:28,168 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530021730543492: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:37:28,168 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 13:37:37,957 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530021730539173
2025-05-30 13:37:37,959 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:37:37,961 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530021730539173, 用户ID: USR337855A0
2025-05-30 13:37:37,961 - trade_service_actual - INFO - [SPOT_CLOSE:fba44df6] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 13:37:37,961 - trade_service_actual - INFO - [SPOT_CLOSE:fba44df6] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:37,961 - trade_service_actual - FATAL - [SPOT_CLOSE:fba44df6] 准备执行现货平仓操作，订单ID=None
2025-05-30 13:37:37,961 - trade_service_actual - FATAL - [SPOT_CLOSE:fba44df6] 调用spot_service.close_position: order_id=None
2025-05-30 13:37:42,993 - trade_service_actual - FATAL - [SPOT_CLOSE:fba44df6] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 13:37:42,993 - trade_service_actual - ERROR - [SPOT_CLOSE:fba44df6] 现货平仓失败: 未找到订单: None, 耗时=5.03秒
2025-05-30 13:37:42,993 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 13:37:42,993 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 未找到订单: None
2025-05-30 13:37:44,994 - trade_service_actual - INFO - [SPOT_CLOSE:c4da8519] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 13:37:44,994 - trade_service_actual - INFO - [SPOT_CLOSE:c4da8519] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:44,994 - trade_service_actual - FATAL - [SPOT_CLOSE:c4da8519] 准备执行现货平仓操作，订单ID=None
2025-05-30 13:37:44,994 - trade_service_actual - FATAL - [SPOT_CLOSE:c4da8519] 调用spot_service.close_position: order_id=None
2025-05-30 13:37:50,026 - trade_service_actual - FATAL - [SPOT_CLOSE:c4da8519] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 13:37:50,027 - trade_service_actual - ERROR - [SPOT_CLOSE:c4da8519] 现货平仓失败: 未找到订单: None, 耗时=5.03秒
2025-05-30 13:37:50,027 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 13:37:50,027 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 未找到订单: None
2025-05-30 13:37:52,027 - trade_service_actual - INFO - [SPOT_CLOSE:2a9e2cda] [现货平仓开始] 账户=SPOT337855A0, 订单ID=None
2025-05-30 13:37:52,027 - trade_service_actual - INFO - [SPOT_CLOSE:2a9e2cda] 成功获取现货服务实例，登录状态: True
2025-05-30 13:37:52,028 - trade_service_actual - FATAL - [SPOT_CLOSE:2a9e2cda] 准备执行现货平仓操作，订单ID=None
2025-05-30 13:37:52,028 - trade_service_actual - FATAL - [SPOT_CLOSE:2a9e2cda] 调用spot_service.close_position: order_id=None
2025-05-30 13:37:57,087 - trade_service_actual - FATAL - [SPOT_CLOSE:2a9e2cda] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: None', 'order_id': None}
2025-05-30 13:37:57,087 - trade_service_actual - ERROR - [SPOT_CLOSE:2a9e2cda] 现货平仓失败: 未找到订单: None, 耗时=5.06秒
2025-05-30 13:37:57,087 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=None, 错误=未找到订单: None
2025-05-30 13:37:57,087 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: None
2025-05-30 13:37:57,087 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530021730539173: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:37:57,087 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 13:40:04,965 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:40:07,734 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:40:31,282 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:40:34,065 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:40:57,627 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:41:00,385 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:41:22,524 - trade_service_actual - INFO - 开始执行平仓操作: user_id=USR337855A0, order_id=ORD20250530021730543492
2025-05-30 13:41:22,531 - trade_service_actual - INFO - 使用硬编码账户配置: 现货账户=SPOT337855A0, 期货账户=FUTURE337855A0
2025-05-30 13:41:22,532 - trade_service_actual - INFO - [TradeService] 优先执行现货平仓交易，订单ID: ORD20250530021730543492, 用户ID: USR337855A0
2025-05-30 13:41:22,532 - trade_service_actual - INFO - [SPOT_CLOSE:c06d3899] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:41:22,532 - trade_service_actual - INFO - [SPOT_CLOSE:c06d3899] 成功获取现货服务实例，登录状态: True
2025-05-30 13:41:22,532 - trade_service_actual - FATAL - [SPOT_CLOSE:c06d3899] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:41:22,532 - trade_service_actual - FATAL - [SPOT_CLOSE:c06d3899] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:41:27,574 - trade_service_actual - FATAL - [SPOT_CLOSE:c06d3899] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:41:27,574 - trade_service_actual - ERROR - [SPOT_CLOSE:c06d3899] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.04秒
2025-05-30 13:41:27,574 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:41:27,574 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 1/3): 未找到订单: S2025053002173230988
2025-05-30 13:41:29,575 - trade_service_actual - INFO - [SPOT_CLOSE:7f0c7485] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:41:29,576 - trade_service_actual - INFO - [SPOT_CLOSE:7f0c7485] 成功获取现货服务实例，登录状态: True
2025-05-30 13:41:29,576 - trade_service_actual - FATAL - [SPOT_CLOSE:7f0c7485] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:41:29,576 - trade_service_actual - FATAL - [SPOT_CLOSE:7f0c7485] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:41:34,652 - trade_service_actual - FATAL - [SPOT_CLOSE:7f0c7485] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:41:34,653 - trade_service_actual - ERROR - [SPOT_CLOSE:7f0c7485] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.08秒
2025-05-30 13:41:34,653 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:41:34,653 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 2/3): 未找到订单: S2025053002173230988
2025-05-30 13:41:36,654 - trade_service_actual - INFO - [SPOT_CLOSE:86167460] [现货平仓开始] 账户=SPOT337855A0, 订单ID=S2025053002173230988
2025-05-30 13:41:36,654 - trade_service_actual - INFO - [SPOT_CLOSE:86167460] 成功获取现货服务实例，登录状态: True
2025-05-30 13:41:36,654 - trade_service_actual - FATAL - [SPOT_CLOSE:86167460] 准备执行现货平仓操作，订单ID=S2025053002173230988
2025-05-30 13:41:36,655 - trade_service_actual - FATAL - [SPOT_CLOSE:86167460] 调用spot_service.close_position: order_id=S2025053002173230988
2025-05-30 13:41:41,696 - trade_service_actual - FATAL - [SPOT_CLOSE:86167460] spot_service.close_position返回结果: {'success': False, 'message': '未找到订单: S2025053002173230988', 'order_id': 'S2025053002173230988'}
2025-05-30 13:41:41,696 - trade_service_actual - ERROR - [SPOT_CLOSE:86167460] 现货平仓失败: 未找到订单: S2025053002173230988, 耗时=5.04秒
2025-05-30 13:41:41,696 - trade_service_actual - ERROR - 现货平仓失败: 账户=SPOT337855A0, 订单ID=S2025053002173230988, 错误=未找到订单: S2025053002173230988
2025-05-30 13:41:41,696 - trade_service_actual - WARNING - 执行现货平仓失败 (尝试 3/3): 未找到订单: S2025053002173230988
2025-05-30 13:41:41,697 - trade_service_actual - INFO - [TradeService] 现货平仓交易结果，订单ID ORD20250530021730543492: {'success': False, 'message': '执行现货平仓失败，已达到最大重试次数 (3)', 'order_id': None}
2025-05-30 13:41:41,697 - trade_service_actual - DEBUG - 用户交易锁已释放: USR337855A0
2025-05-30 13:43:32,068 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:43:34,867 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:44:17,770 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:44:20,551 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:44:44,060 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:44:46,825 - trade_service_actual - INFO - 交易服务初始化完成
2025-05-30 13:45:10,444 - trade_service_actual - INFO - 交易服务日志初始化完成，日志文件路径: /gold/logs/trade_service_actual.log
2025-05-30 13:45:13,201 - trade_service_actual - INFO - 交易服务初始化完成
