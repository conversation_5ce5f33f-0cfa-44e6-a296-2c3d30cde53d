使用默认SECRET_KEY，建议在生产环境中更改
在使用天勤量化之前，默认您已经知晓并同意以下免责条款，如果不同意请立即停止使用：https://www.shinnytech.com/blog/disclaimer/
在使用天勤量化之前，默认您已经知晓并同意以下免责条款，如果不同意请立即停止使用：https://www.shinnytech.com/blog/disclaimer/
执行原始SQL语句（存在SQL注入风险）: -- 完整的数据库迁移脚本
-- 包含所有必要的表结构和字段

-- 创建数据库版本表
CREATE TABLE IF NOT EXISTS db_version (
    id SERIAL PR...
执行原始SQL语句（存在SQL注入风险）: -- 创建迁移记录表
CREATE TABLE IF NOT EXISTS migrations (
    name VARCHAR(100) PRIMARY KEY,
    executed_a...
执行原始SQL语句（存在SQL注入风险）: -- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50)...
执行原始SQL语句（存在SQL注入风险）: -- 创建账户表
CREATE TABLE IF NOT EXISTS accounts (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(3...
执行原始SQL语句（存在SQL注入风险）: -- 创建索引
CREATE INDEX IF NOT EXISTS ix_accounts_broker_id ON accounts(broker_id)...
执行原始SQL语句（存在SQL注入风险）: -- 创建设置表
CREATE TABLE IF NOT EXISTS settings (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(36) NO...
执行原始SQL语句（存在SQL注入风险）: -- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36)...
执行原始SQL语句（存在SQL注入风险）: -- 创建风控限制表
CREATE TABLE IF NOT EXISTS risk_limits (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(3...
执行原始SQL语句（存在SQL注入风险）: -- 创建风控指标表
CREATE TABLE IF NOT EXISTS risk_metrics (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(...
执行原始SQL语句（存在SQL注入风险）: -- 创建风控日志表
CREATE TABLE IF NOT EXISTS risk_logs (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(36)...
执行原始SQL语句（存在SQL注入风险）: -- 创建自动交易设置表
CREATE TABLE IF NOT EXISTS auto_trade_settings (
    id SERIAL PRIMARY KEY,
    user_id...
执行原始SQL语句（存在SQL注入风险）: -- 记录迁移版本
INSERT INTO migrations (name, executed_at)
VALUES ('001_complete_schema.sql', CURRENT_TIME...
执行原始SQL语句（存在SQL注入风险）: -- 更新数据库版本
INSERT INTO db_version (version, description)
VALUES ('1.0.0', '完整数据库结构')
ON CONFLICT (ve...
批量执行原始SQL语句失败: there is no unique or exclusion constraint matching the ON CONFLICT specification

2025-05-30 13:32:02,785 - ctp_gateway.tq_md_api - INFO - 启动天勤行情API服务...
2025-05-30 13:32:02,785 - ctp_gateway.tq_md_api - INFO - 交易时间内，尝试完整连接天勤SDK
2025-05-30 13:32:02,785 - ctp_gateway.tq_md_api - INFO - 开始连接天勤行情服务器...
2025-05-30 13:32:02,785 - ctp_gateway.tq_md_api - INFO - 使用账户连接: chenxinsg
2025-05-30 13:32:02,785 - ctp_gateway.tq_md_api - INFO - 创建天勤API实例...
2025-05-30 13:32:03,866 - ctp_gateway.tq_md_api - INFO - 天勤API实例创建成功
2025-05-30 13:32:03,866 - ctp_gateway.tq_md_api - INFO - 获取测试合约行情...
2025-05-30 13:32:03,955 - ctp_gateway.tq_md_api - INFO - 测试合约行情对象: {'datetime': '2025-05-30 13:32:03.500000', 'ask_price1': 770.0, 'ask_volume1': 10, 'bid_price1': 769.98, 'bid_volume1': 4, 'ask_price2': 770.02, 'ask_volume2': 16, 'bid_price2': 769.96, 'bid_volume2': 2, 'ask_price3': 770.04, 'ask_volume3': 1, 'bid_price3': 769.94, 'bid_volume3': 2, 'ask_price4': 770.06, 'ask_volume4': 2, 'bid_price4': 769.92, 'bid_volume4': 3, 'ask_price5': 770.08, 'ask_volume5': 3, 'bid_price5': 769.88, 'bid_volume5': 3, 'last_price': 770.0, 'highest': 776.16, 'lowest': 768.7, 'open': 773.58, 'close': nan, 'average': 772.69, 'volume': 197032, 'amount': 152245087180.0, 'open_interest': 183158, 'settlement': nan, 'upper_limit': 866.08, 'lower_limit': 666.82, 'pre_open_interest': 195076, 'pre_settlement': 766.46, 'pre_close': 764.32, 'price_tick': 0.02, 'price_decs': 2, 'volume_multiple': 1000.0, 'max_limit_order_volume': 500, 'max_market_order_volume': 0, 'min_limit_order_volume': 1, 'min_market_order_volume': 0, 'open_max_market_order_volume': 0, 'open_max_limit_order_volume': 500, 'open_min_market_order_volume': 0, 'open_min_limit_order_volume': 1, 'underlying_symbol': '', 'strike_price': nan, 'ins_class': 'FUTURE', 'instrument_id': 'SHFE.au2508', 'instrument_name': '沪金2508', 'exchange_id': 'SHFE', 'expired': False, 'trading_time': {"day": [["09:00:00", "10:15:00"], ["10:30:00", "11:30:00"], ["13:30:00", "15:00:00"]], "night": [["21:00:00", "26:30:00"]]}, 'expire_datetime': 1755241200.0, 'delivery_year': 2025, 'delivery_month': 8, 'last_exercise_datetime': nan, 'exercise_year': 0, 'exercise_month': 0, 'option_class': '', 'exercise_type': '', 'product_id': 'au', 'iopv': nan, 'public_float_share_quantity': 0, 'stock_dividend_ratio': [], 'cash_dividend_ratio': [], 'expire_rest_days': 77, 'categories': [{'id': 'PRECIOUS_METALS', 'name': '贵金属'}], 'position_limit': 9000, 'margin': 38323.0, 'commission': 10.0}
2025-05-30 13:32:03,955 - ctp_gateway.tq_md_api - INFO - 等待连接建立...
2025-05-30 13:32:06,023 - ctp_gateway.tq_md_api - INFO - 测试合约最新价格: 770.0
2025-05-30 13:32:06,023 - ctp_gateway.tq_md_api - INFO - 启动行情更新循环
2025-05-30 13:32:06,024 - ctp_gateway.tq_md_api - INFO - 天勤行情API连接成功
2025-05-30 13:32:06,024 - ctp_gateway.tq_md_api - INFO - 天勤行情API已登录
2025-05-30 13:32:06,024 - ctp_gateway.tq_md_api - INFO - 订阅目标合约: SHFE.au2508
2025-05-30 13:32:06,024 - ctp_gateway.tq_md_api - INFO - 成功订阅合约: SHFE.au2508
2025-05-30 13:32:06,024 - ctp_gateway.tq_md_api - INFO - 天勤行情API服务启动成功
2025-05-30 13:32:06,026 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:06,026 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:06,026 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 0
2025-05-30 13:32:06,026 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:06,026 - ctp_gateway.tq_md_api - WARNING - 未找到SHFE.au2508合约的行情数据，尝试重新订阅
2025-05-30 13:32:06,026 - ctp_gateway.tq_md_api - INFO - 订阅目标合约: SHFE.au2508
2025-05-30 13:32:06,026 - ctp_gateway.tq_md_api - INFO - 成功订阅合约: SHFE.au2508
2025-05-30 13:32:06,124 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.94, ask=769.98, last=769.96
2025-05-30 13:32:06,624 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.92, ask=769.96, last=769.96
2025-05-30 13:32:07,124 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.96, ask=769.98, last=769.96
2025-05-30 13:32:07,540 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.98, ask=770.0, last=770.0
2025-05-30 13:32:08,026 - ctp_gateway.tq_md_api - INFO - 重新订阅后获取到SHFE.au2508合约数据
2025-05-30 13:32:08,027 - ctp_gateway.tq_md_api - INFO - 重新订阅后获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=769.98, ask=770.0
2025-05-30 13:32:08,027 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:08,027 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:08,027 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:08,027 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:08,027 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=769.98, ask=770.0
2025-05-30 13:32:08,036 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.02, last=770.02
2025-05-30 13:32:08,629 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.02, last=770.0
2025-05-30 13:32:09,029 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.02, last=770.02
2025-05-30 13:32:09,030 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:09,030 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:09,030 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:09,030 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:09,030 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.02
2025-05-30 13:32:10,633 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:10,634 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:10,634 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:10,634 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:10,639 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.02
2025-05-30 13:32:10,724 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.02, last=770.0
2025-05-30 13:32:10,824 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.06, last=770.0
2025-05-30 13:32:11,124 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.98, ask=770.0, last=770.0
2025-05-30 13:32:11,539 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:11,539 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:11,539 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:11,539 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:11,540 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=769.98, ask=770.0
2025-05-30 13:32:11,548 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.98, ask=770.06, last=770.02
2025-05-30 13:32:11,724 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:11,724 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:11,724 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:11,724 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:11,724 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=769.98, ask=770.06
2025-05-30 13:32:12,031 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.08, last=770.06
2025-05-30 13:32:12,530 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.08, last=770.08
2025-05-30 13:32:12,726 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:12,727 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:12,727 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:12,727 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:12,727 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.08
2025-05-30 13:32:13,037 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.04, ask=770.08, last=770.08
2025-05-30 13:32:13,537 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.06, last=770.08
2025-05-30 13:32:13,728 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:13,729 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:13,729 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:13,729 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:13,729 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:14,032 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.04, ask=770.06, last=770.08
2025-05-30 13:32:14,538 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.06, ask=770.08, last=770.06
2025-05-30 13:32:14,730 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:14,730 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:14,730 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:14,730 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:14,730 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.06, ask=770.08
2025-05-30 13:32:15,042 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.04, ask=770.08, last=770.06
2025-05-30 13:32:15,732 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:15,732 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:15,732 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:15,732 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:15,732 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.04, ask=770.08
2025-05-30 13:32:16,035 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.04, last=770.04
2025-05-30 13:32:16,538 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.06, last=770.06
2025-05-30 13:32:16,734 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:16,734 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:16,734 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:16,734 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:16,734 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:17,046 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.08, last=770.06
2025-05-30 13:32:17,539 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.04, ask=770.08, last=770.06
2025-05-30 13:32:17,735 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:17,735 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:17,735 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:17,735 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:17,736 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.04, ask=770.08
2025-05-30 13:32:18,530 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.06, last=770.06
2025-05-30 13:32:18,737 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:18,737 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:18,737 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:18,737 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:18,737 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:19,033 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.06, last=770.06
2025-05-30 13:32:19,535 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.06, last=770.06
2025-05-30 13:32:19,738 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:19,738 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:19,738 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:19,738 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:19,739 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:20,036 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.06, last=770.06
2025-05-30 13:32:20,534 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.06, last=770.06
2025-05-30 13:32:20,739 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:20,740 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:20,740 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:20,740 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:20,740 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:21,741 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:21,741 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:21,741 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:21,741 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:21,742 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:21,742 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:21,742 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:21,742 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:21,742 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:21,742 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:22,742 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:22,742 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:22,742 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:22,742 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:22,743 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:22,746 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:22,746 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:22,746 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:22,746 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:22,746 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:23,531 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.06, last=770.02
2025-05-30 13:32:23,744 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:23,744 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:23,744 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:23,744 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:23,744 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.06
2025-05-30 13:32:24,034 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.06, last=770.06
2025-05-30 13:32:24,535 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.06, last=770.06
2025-05-30 13:32:24,745 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:24,745 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:24,745 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:24,745 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:24,746 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.06
2025-05-30 13:32:25,038 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.02, last=770.06
2025-05-30 13:32:25,530 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.06, last=770.02
2025-05-30 13:32:25,747 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:25,747 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:25,747 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:25,747 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:25,747 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.06
2025-05-30 13:32:26,039 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.98, ask=770.0, last=770.06
2025-05-30 13:32:26,532 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.06, last=770.0
2025-05-30 13:32:26,748 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:26,748 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:26,748 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:26,748 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:26,749 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:27,040 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.08, last=770.0
2025-05-30 13:32:27,539 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.98, ask=770.06, last=769.98
2025-05-30 13:32:27,750 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:27,750 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:27,750 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:27,750 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:27,751 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=769.98, ask=770.06
2025-05-30 13:32:28,033 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.02, last=769.98
2025-05-30 13:32:28,538 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.06, last=770.06
2025-05-30 13:32:28,752 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:28,752 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:28,752 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:28,752 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:28,752 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:29,028 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.04, ask=770.08, last=770.06
2025-05-30 13:32:29,532 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.06, ask=770.08, last=770.06
2025-05-30 13:32:29,753 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:29,753 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:29,753 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:29,753 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:29,754 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.06, ask=770.08
2025-05-30 13:32:30,035 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.08, ask=770.1, last=770.08
2025-05-30 13:32:30,550 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.08, ask=770.1, last=770.1
2025-05-30 13:32:30,755 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:30,755 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:30,755 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:30,755 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:30,756 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.08, ask=770.1
2025-05-30 13:32:31,534 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.06, ask=770.08, last=770.08
2025-05-30 13:32:31,757 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:31,757 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:31,757 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:31,757 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:31,758 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.06, ask=770.08
2025-05-30 13:32:32,058 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.06, ask=770.1, last=770.08
2025-05-30 13:32:32,541 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.08, ask=770.1, last=770.08
2025-05-30 13:32:32,759 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:32,759 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:32,759 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:32,759 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:32,759 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.08, ask=770.1
2025-05-30 13:32:33,041 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.06, ask=770.1, last=770.08
2025-05-30 13:32:33,544 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.06, ask=770.1, last=770.1
2025-05-30 13:32:33,761 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:33,761 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:33,761 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:33,761 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:33,762 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.06, ask=770.1
2025-05-30 13:32:34,040 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.06, ask=770.1, last=770.06
2025-05-30 13:32:34,538 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.06, ask=770.08, last=770.06
2025-05-30 13:32:34,763 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:34,763 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:34,763 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:34,763 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:34,763 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.06, ask=770.08
2025-05-30 13:32:35,033 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.06, ask=770.08, last=770.08
2025-05-30 13:32:35,532 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.06, ask=770.08, last=770.06
2025-05-30 13:32:35,764 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:35,764 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:35,764 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:35,764 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:35,765 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.06, ask=770.08
2025-05-30 13:32:36,541 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.08, last=770.06
2025-05-30 13:32:36,766 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:36,766 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:36,766 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:36,766 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:36,767 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.08
2025-05-30 13:32:37,039 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.02, ask=770.06, last=770.06
2025-05-30 13:32:37,768 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:37,768 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:37,768 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:37,768 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:37,768 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.02, ask=770.06
2025-05-30 13:32:38,529 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.04, last=770.0
2025-05-30 13:32:38,769 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:38,769 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:38,769 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:38,769 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:38,770 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.04
2025-05-30 13:32:39,045 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.02, last=770.04
2025-05-30 13:32:39,543 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.02, last=770.04
2025-05-30 13:32:39,771 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:39,771 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:39,771 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:39,771 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:39,771 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.02
2025-05-30 13:32:40,038 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.02, last=770.04
2025-05-30 13:32:40,530 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.06, last=770.02
2025-05-30 13:32:40,773 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:40,773 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:40,773 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:40,773 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:40,773 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.06
2025-05-30 13:32:41,037 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.04, last=770.02
2025-05-30 13:32:41,537 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.04, last=770.0
2025-05-30 13:32:41,775 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:41,775 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:41,775 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:41,775 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:41,776 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.04
2025-05-30 13:32:42,035 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.04, last=770.0
2025-05-30 13:32:42,533 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.04, last=770.0
2025-05-30 13:32:42,777 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:42,777 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:42,777 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:42,777 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:42,777 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.04
2025-05-30 13:32:43,042 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.04, last=770.0
2025-05-30 13:32:43,532 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.04, last=770.0
2025-05-30 13:32:43,779 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:43,779 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:43,779 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:43,779 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:43,780 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.04
2025-05-30 13:32:44,045 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.04, ask=770.06, last=770.04
2025-05-30 13:32:44,541 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.04, last=770.04
2025-05-30 13:32:44,781 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:44,781 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:44,781 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:44,781 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:44,781 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.04
2025-05-30 13:32:45,041 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.04, last=770.0
2025-05-30 13:32:45,782 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:45,782 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:45,782 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:45,782 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:45,783 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=770.0, ask=770.04
2025-05-30 13:32:46,037 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=770.0, ask=770.04, last=770.0
2025-05-30 13:32:46,533 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.96, ask=769.98, last=769.98
2025-05-30 13:32:46,784 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:46,784 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:46,784 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:46,784 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:46,785 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=769.96, ask=769.98
2025-05-30 13:32:47,034 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.9, ask=769.94, last=769.94
2025-05-30 13:32:47,532 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.9, ask=769.94, last=769.94
2025-05-30 13:32:47,786 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:47,787 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:47,787 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:47,787 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:47,787 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=769.9, ask=769.94
2025-05-30 13:32:48,037 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.9, ask=769.92, last=769.94
2025-05-30 13:32:48,544 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.88, ask=769.92, last=769.9
2025-05-30 13:32:48,788 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:48,788 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:48,788 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:48,788 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:48,789 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=769.88, ask=769.92
2025-05-30 13:32:49,029 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.86, ask=769.88, last=769.88
2025-05-30 13:32:49,534 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.86, ask=769.88, last=769.88
2025-05-30 13:32:49,791 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:49,791 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:49,791 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:49,791 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:49,792 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=769.86, ask=769.88
2025-05-30 13:32:50,042 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.68, ask=769.72, last=769.68
2025-05-30 13:32:50,544 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.56, ask=769.64, last=769.62
2025-05-30 13:32:50,793 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:50,793 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:50,793 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:50,793 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:50,793 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=769.56, ask=769.64
2025-05-30 13:32:51,047 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.54, ask=769.56, last=769.58
2025-05-30 13:32:51,539 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.56, ask=769.66, last=769.58
2025-05-30 13:32:51,795 - ctp_gateway.tq_md_api - INFO - 当前交易状态: 交易中, 当前处于交易时间
2025-05-30 13:32:51,795 - ctp_gateway.tq_md_api - INFO - 天勤SDK连接状态: connected=True, logged_in=True, api=True
2025-05-30 13:32:51,795 - ctp_gateway.tq_md_api - INFO - 天勤SDK缓存数据数量: 1
2025-05-30 13:32:51,795 - ctp_gateway.tq_md_api - INFO - 交易时段，尝试获取天勤SDK实时数据: SHFE.au2508
2025-05-30 13:32:51,796 - ctp_gateway.tq_md_api - INFO - 交易时段获取并缓存天勤SDK实时数据 (键: ['SHFE.au2508', 'SHFE.au2508', 'SHFE.au2508']): bid=769.56, ask=769.66
2025-05-30 13:32:52,028 - ctp_gateway.tq_md_api - INFO - 天勤行情数据更新: SHFE.au2508, bid=769.58, ask=769.64, last=769.58
